stages:
  - build

# job的锚点
.job_template: &job_configuration
  image:
    name: harbordev.zlattice.top/library/kaniko-executor:v1.23.2-debug
    entrypoint: [ "" ]
  stage: build
  services:
    - harbordev.zlattice.top/library/docker:27.3.1-dind

build-image:
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^develop.*$/ || $CI_COMMIT_BRANCH == "test" || $CI_COMMIT_BRANCH == "release"'
      changes:
        - reg-trace-customer-web/**/*
        - reg-trace-auth/**/*
        - reg-trace-common/**/*
        - reg-trace-core/**/*
        - reg-trace-persistence/**/*
      when: on_success
  <<: *job_configuration
  script:
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor --context $CI_PROJECT_DIR --dockerfile ./Dockerfile-customer --destination $CI_REGISTRY/reg-trace/customer:$CI_COMMIT_BRANCH

build-image2:
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^develop.*$/ || $CI_COMMIT_BRANCH == "test" || $CI_COMMIT_BRANCH == "release"'
      changes:
        - reg-trace-pc-web/**/*
        - reg-trace-auth/**/*
        - reg-trace-common/**/*
        - reg-trace-core/**/*
        - reg-trace-persistence/**/*
      when: on_success
  <<: *job_configuration
  script:
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor --context $CI_PROJECT_DIR --dockerfile ./Dockerfile-pc --destination $CI_REGISTRY/reg-trace/pc:$CI_COMMIT_BRANCH
