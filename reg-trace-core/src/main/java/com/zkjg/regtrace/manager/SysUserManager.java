package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.login.LoginRequest;
import com.zkjg.regtrace.persistence.vo.request.register.RegisterRequest;
import com.zkjg.regtrace.persistence.vo.request.register.ResetPasswordRequest;
import com.zkjg.regtrace.persistence.vo.request.userinfo.*;
import com.zkjg.regtrace.persistence.vo.response.userinfo.ImageShowVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserHistoryVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserDetailVo;
import org.springframework.web.multipart.MultipartFile;

public interface SysUserManager {

    /**
     * 用户注册
     */
    void register(RegisterRequest req);

    /**
     * 用户登录
     */
    String login(LoginRequest req);

    /**
     * 用户详情
     */
    UserDetailVo selectDetail(Integer userId);

    /**
     * 编辑用户
     */
    void editUser(EditUserInfoRequest req);

    /**
     * 查询用户编辑历史
     */
    PageResult<UserHistoryVo> selectList(UserHistoryInfoListRequest req);

    /**
     * 重置用户信息
     */
    void resetUser(ResetUserinfoRequest req);

    /**
     * 通过邮箱重置密码
     */
    void resetPassword(ResetPasswordRequest req);

    /**
     * 平台用户申请重置权限
     */
    void resetPermission(EditUserPermissionRequest req);

    /**
     * 修改用户头像
     */
    void changeHeadImg(ChangeUserHeadImgRequest req);

    /**
     * 用户信息上传影像件
     */
    ImageShowVo uploadInfoImage(MultipartFile file, LoginUser user, String module);
}