package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import com.zkjg.regtrace.manager.ExportTaskManager;
import com.zkjg.regtrace.persistence.assembler.ExportTaskAssembler;
import com.zkjg.regtrace.persistence.entity.ExportTaskDO;
import com.zkjg.regtrace.persistence.vo.response.schedule.ExportTaskVo;
import com.zkjg.regtrace.service.ExportTaskService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ExportTaskManagerImpl implements ExportTaskManager {

    @Resource
    private ExportTaskService exportTaskService;

    @Override
    public void updateScheduleType(Integer id, ExportTaskScheduleTypeEnum scheduleType) {
        exportTaskService.updateScheduleType(id, scheduleType);
    }

    @Override
    public ExportTaskVo getTaskByCreatorId() {
        LoginUser loginUser = TokenService.getLoginUser();
        ExportTaskDO exportTaskDO = exportTaskService.findByCreatorId(loginUser.getUserId());
        return ExportTaskAssembler.convertDoToVo(exportTaskDO);
    }
}
