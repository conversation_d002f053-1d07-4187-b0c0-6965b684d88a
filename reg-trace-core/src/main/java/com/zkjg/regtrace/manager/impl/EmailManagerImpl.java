package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.util.RandomUtil;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.EmailSendTypeEnum;
import com.zkjg.regtrace.common.enums.EmailTemplateEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.manager.EmailManager;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.vo.request.register.SendEmailRequest;
import com.zkjg.regtrace.service.EmailService;
import com.zkjg.regtrace.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 8:56
 */
@Service
public class EmailManagerImpl implements EmailManager {

    @Resource
    private EmailService emailService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private RedisUtil redisUtil;

    @Value("${external.service.email-url}")
    private String emailUrl;

    @Override
    public String sendEmail(SendEmailRequest req) {
        if (req.getEmailType() == EmailSendTypeEnum.RESET_PASSWORD.getCode()) {
            //校验用户
            SysUserDO sysUserDO = null;
            if (StringUtils.isNotEmpty(req.getEmail())) {
                sysUserDO = sysUserService.findUserByEmail(req.getEmail());
            } else if (StringUtils.isNotEmpty(req.getUsername())) {
                sysUserDO = sysUserService.findUserByUsername(req.getUsername());
            }
            if (Objects.isNull(sysUserDO)) {
                throw new BusinessException("该用户不存在");
            }
            //设置校验token
            String token = RandomUtil.randomString(32);
            redisUtil.setEx(String.format(RedisKeyConstant.EMAIL_RESET_URL, token), sysUserDO.getEmail(), 5, TimeUnit.MINUTES);
            //邮件参数
            Map<String, String> model = new HashMap<>();
            model.put("userName", sysUserDO.getUsername());
            String encodedEmail;
            try {
                encodedEmail = URLEncoder.encode(sysUserDO.getEmail(), StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            model.put("resetPasswordUrl", emailUrl + "?email=" + encodedEmail + "&token=" + token);

            emailService.sendEmail(sysUserDO.getEmail(), EmailTemplateEnum.RESET_PASSWORD, model);
            return emailUrl + "?email=" + encodedEmail + "&token=" + token;
        } else {
            if (req.getEmailType() == EmailSendTypeEnum.REGISTER.getCode()) {
                SysUserDO sysUserDO = sysUserService.findUserByEmail(req.getEmail());
                if (Objects.nonNull(sysUserDO)) {
                    throw new BusinessException("该邮箱已被注册");
                }
            }
            if (req.getEmailType() == EmailSendTypeEnum.LOGIN.getCode()) {
                SysUserDO sysUserDO = sysUserService.findUserByEmail(req.getEmail());
                if (Objects.isNull(sysUserDO)) {
                    throw new BusinessException("该邮箱还未注册,请先注册");
                }
            }
            String redisKey = String.format(RedisKeyConstant.EMAIL_VERIFICATION_CODE, req.getEmail(), req.getEmailType());
            String code = RandomUtil.randomNumbers(6);

            Map<String, String> model = new HashMap<>();
            model.put("code", code);

            emailService.sendEmail(req.getEmail(), EmailTemplateEnum.VERIFICATION_CODE, model);
            redisUtil.setEx(redisKey, code, 5, TimeUnit.MINUTES);
            return code;
        }

    }
}
