package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleQueryUserListForAddVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleQueryUserListVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:34
 */
public interface RoleManager {

    /**
     * 添加角色
     */
    void addRole(RoleAddRequest req);

    /**
     * 查询角色列表
     */
    PageResult<RoleListVo> selectRoleList(RoleListRequest req);

    /**
     * 查询权限列表
     */
    List<PermissionDetailVo> selectPermissionListByRole(Integer roleId);

    /**
     * 编辑角色
     */
    void editRole(RoleEditRequest req);

    /**
     * 批量删除角色
     */
    void deleteRoles(List<Integer> roleIds);

    /**
     * 查询角色已分配用户
     */
    PageResult<RoleQueryUserListVo> selectUser(RoleQueryUserListRequest request);

    /**
     * 删除某角色下用户
     */
    void deleteUserRoles(RoleDeleteOrAddUserListRequest roleDeleteOrAddUserListRequest);

    /**
     * 查询某角色下未添加用户
     */
    PageResult<RoleQueryUserListForAddVo> selectUserForAdd(RoleQueryUserListForAddRequest request);

    /**
     * 角色添加用户
     */
    void addUserRoles(RoleDeleteOrAddUserListRequest request);

}
