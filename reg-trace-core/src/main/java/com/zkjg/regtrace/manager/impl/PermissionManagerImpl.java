package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.enums.PermissionTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.manager.PermissionManager;
import com.zkjg.regtrace.persistence.assembler.PermissionAssembler;
import com.zkjg.regtrace.persistence.entity.*;
import com.zkjg.regtrace.persistence.jpa.PermissionJpaSpec;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionListVo;
import com.zkjg.regtrace.service.*;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:34
 */
@Service
public class PermissionManagerImpl implements PermissionManager {

    @Resource
    private SysPermissionService sysPermissionService;

    @Override
    public List<PermissionListVo> selectPermissionList(PermissionListRequest req) {
        //查询结果
        Specification<SysPermissionDO> specification = QueryConvertUtils.toSpecification(req);
        List<SysPermissionDO> permissionDOS = sysPermissionService.findPermission(specification);

        return BeanUtil.copyToList(permissionDOS, PermissionListVo.class);
    }

    @Override
    @Transactional
    public Integer insertPermission(PermissionAddRequest req) {
        if (!req.getParentId().equals(0)) {
            SysPermissionDO permissionDO = sysPermissionService.findById(req.getParentId()).orElseThrow(() -> new BusinessException("参数异常"));
            if (permissionDO.getPermissionType().equals(PermissionTypeEnum.BUTTON.getValue())) {
                throw new BusinessException("按钮不可作为上级菜单");
            }
            if (permissionDO.getPermissionType() > req.getPermissionType()) {
                throw new BusinessException("菜单不可作为目录上级菜单");
            }
        }

        SysPermissionDO save = sysPermissionService.save(PermissionAssembler.initInsertPermission(req, TokenService.getLoginUser().getUsername()));
        //刷新下标
        refreshSortNumber(save);
        return save.getId();
    }

    @Override
    @Transactional
    public void updatePermission(PermissionEditRequest req) {
        SysPermissionDO permission = sysPermissionService.findById(req.getId()).orElseThrow(() -> new BusinessException("参数异常"));
        sysPermissionService.save(PermissionAssembler.initEditPermission(req, permission, TokenService.getLoginUser().getUsername()));

        //刷新下标
        refreshSortNumber(permission);
    }

    @Override
    @Transactional
    public void deletePermission(Integer id) {
        SysPermissionDO permissionDO = sysPermissionService.findById(id).orElseThrow(() -> new BusinessException("参数异常"));
        permissionDO.setDeleted(StatusEnum.NO.getCode());
        permissionDO.setModifyTime(LocalDateTime.now());
        permissionDO.setModifier(TokenService.getLoginUser().getUsername());
        sysPermissionService.save(permissionDO);
        //刷新下标
        refreshSortNumber(permissionDO);
    }

    @Override
    public boolean checkSameNameByParentId(String name, Integer parentId, Integer id) {
        Optional<SysPermissionDO> nameDO;
        if (Objects.isNull(id)) {
            PermissionJpaSpec jpaSpec = PermissionJpaSpec.builder()
                    .permissionNameEq(name)
                    .parentIdEq(parentId)
                    .deletedEq(StatusEnum.NO.getCode())
                    .build();
            Specification<SysPermissionDO> spec = QueryConvertUtils.toSpecification(jpaSpec);
            nameDO = sysPermissionService.findOne(spec);
        } else {
            SysPermissionDO permissionDO = sysPermissionService.findById(id).orElseThrow(() -> new BusinessException("参数异常"));
            PermissionJpaSpec jpaSpec = PermissionJpaSpec.builder()
                    .permissionNameEq(name)
                    .parentIdEq(permissionDO.getParentId())
                    .idNotEq(id)
                    .deletedEq(StatusEnum.NO.getCode())
                    .build();
            Specification<SysPermissionDO> spec = QueryConvertUtils.toSpecification(jpaSpec);
            nameDO = sysPermissionService.findOne(spec);
        }
        return nameDO.isPresent();
    }

    @Override
    @Transactional
    public void refreshSortNumber(SysPermissionDO permission) {
        Sort sort = Sort.by(Sort.Direction.ASC, "sortNumber");

        PermissionJpaSpec jpaSpec = PermissionJpaSpec.builder()
                .parentIdEq(permission.getParentId())
                .permissionTypeEq(permission.getPermissionType())
                .idNotEq(permission.getId())
                .deletedEq(StatusEnum.NO.getCode()).build();
        Specification<SysPermissionDO> spec = QueryConvertUtils.toSpecification(jpaSpec);
        List<SysPermissionDO> all = sysPermissionService.findAll(spec, sort);
        for (int i = 0; i < all.size(); i++) {
            all.get(i).setSortNumber(i + 1);
            if (i + 1 >= permission.getSortNumber() && permission.getDeleted().equals(StatusEnum.NO.getCode())) {
                all.get(i).setSortNumber(i + 2);
            }
        }
    }

    @Override
    public boolean hasChild(Integer id) {
        return !sysPermissionService.findByParentId(id).isEmpty();
    }

    @Override
    public boolean hasPermissionExistRole(Integer id) {
        return !sysPermissionService.findByParentId(id).isEmpty();
    }
}
