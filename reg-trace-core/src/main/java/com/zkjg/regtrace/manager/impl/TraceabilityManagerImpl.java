package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.MessageCategoryConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.*;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import com.zkjg.regtrace.manager.TraceabilityManager;
import com.zkjg.regtrace.persistence.assembler.TraceabilityAssembler;
import com.zkjg.regtrace.persistence.assembler.WorkRegistrationAssembler;
import com.zkjg.regtrace.persistence.dto.excel.ConditionalTraceabilityExcelRow;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationJpaSpec;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.dto.workregistration.BatchTraceabilityResult;
import com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto;
import com.zkjg.regtrace.persistence.entity.*;
import com.zkjg.regtrace.persistence.entity.TraceabilityDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityAdminQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRecordRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.*;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;
import com.zkjg.regtrace.service.*;
import com.zkjg.regtrace.validation.TraceabilityValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 8:56
 */
@Service
@Slf4j
public class TraceabilityManagerImpl implements TraceabilityManager {

    @Resource
    private TraceabilityService traceabilityService;

    @Resource
    private MessageNotifyService messageNotifyService;

    @Resource
    private Executor traceExecutor;

    @Resource
    private WorkRegistrationApplicationService workRegistrationApplicationService;

    @Resource
    private WorkRegistrationService workRegistrationService;

    @Resource
    private WorkRegistrationApplicationFileService workRegistrationApplicationFileService;

    @Resource
    private TraceMetricsService traceMetricsService;

    @Resource
    private TraceabilityValidator traceabilityValidator;

    private static final String TRACEABILITY_PREFIX = "TS_";

    @Override
    public TraceabilityResultVo addTraceability(TraceabilityRequest req) {
        // Step 0: 生成溯源编号
        String traceabilityNumber = TRACEABILITY_PREFIX + IdUtil.getSnowflakeNextIdStr();
        // Step 1: 登录用户
        LoginUser loginUser = TokenService.getLoginUser();
        req.setCreator(loginUser.getUsername());
        //参数校验，出现异常记录日志
        req.setTraceabilityNumber(traceabilityNumber);
        traceabilityValidator.validate(req);


        TraceabilityDO traceabilityDO = TraceabilityAssembler.convertToDO(req);
        traceabilityDO.setTraceabilityType(TraceabilityTypeEnum.FILE_TRACEABILITY.getType());
        traceabilityDO.setCreatorId(loginUser.getUserId());
        traceabilityDO.setCreator(loginUser.getUsername());
        traceabilityDO.setTraceabilityNumber(traceabilityNumber);

        WorkRegistrationDO registrationDO = null;
        WorkRegistrationApplicationFileDO fileDO = null;

        // Step 2: 判断是否提取到登记号（水印方式）
        if (StringUtils.isNotBlank(req.getRegistrationNumber())) {
            registrationDO = workRegistrationService.findOne(QueryConvertUtils.toSpecification(QueryWorkRegistrationJpaSpec.builder()
                    .deletedEq(SqlConstant.UN_DELETED)
                    .registrationNumberEq(req.getRegistrationNumber())
                    .recycleEq(StatusEnum.NO.getCode())
                    .build()));

            if (registrationDO != null) {
                traceabilityDO.setSourceType(TraceabilitySourceTypeEnum.WATERMARK.getType());
                //查询文件信息
                fileDO = workRegistrationApplicationFileService.findByApplicationId(registrationDO.getApplicationId());
            }
        } else {
            // Step 3: 没有水印，使用文件哈希匹配
            String hash = StringUtils.defaultIfBlank(req.getWatermarkedWorkHash(), req.getOriginalWorkHash());

            fileDO = workRegistrationApplicationFileService.findByFileOrMarkedHash(hash);
            if (fileDO != null) {
                registrationDO = workRegistrationService.findOne(QueryConvertUtils.toSpecification(QueryWorkRegistrationJpaSpec.builder()
                        .recycleEq(StatusEnum.NO.getCode())
                        .deletedEq(SqlConstant.UN_DELETED)
                        .applicationIdIn(Collections.singletonList(fileDO.getApplicationId()))
                        .build()));

                if (registrationDO != null) {
                    traceabilityDO.setRegistrationNumber(registrationDO.getRegistrationNumber());
                    traceabilityDO.setSourceType(TraceabilitySourceTypeEnum.FILE_HASH.getType());
                }
            }
        }

        // Step 4: 判断是否匹配成功，设置状态
        boolean matched = registrationDO != null;
        traceabilityDO.setStatus(matched ? TraceabilityStatusEnum.SUCCESS.getType() : TraceabilityStatusEnum.FAIL.getType());

        // Step 5: 消息通知登记用户（如果匹配成功） 暂不发送
        /*if (matched) {
            String applicantContent = String.format(
                    MessageEnum.TRACEABILITY_REGISTER.getContent(),
                    traceabilityNumber,
                    req.getFileName(),
                    TraceabilityStatusEnum.getDesc(traceabilityDO.getStatus())
            );
            messageNotifyService.sendMessageNotify(
                    applicationDO.getApplicantUserId(),
                    null,
                    applicantContent,
                    MessageEnum.TRACEABILITY_REGISTER.getSummary(),
                    MessageCategoryConstant.TRACEABILITY_MESSAGE,
                    MessageTypeEnum.INTERNAL.getType(),
                    StatusEnum.YES.getCode()
            );
        }*/

        // Step 6: 保存溯源记录
        traceabilityService.saveTraceabilityDO(traceabilityDO);
        TraceMetricsDO metricsDO = TraceMetricsDO.builder().traceNo(traceabilityDO.getTraceabilityNumber())
                .traceUserId(loginUser.getUserId())
                .traceType(TraceabilityTypeEnum.HASH_TRACEABILITY.getType())
                .status(traceabilityDO.getStatus())
                .createTime(LocalDateTime.now())
                .build();
        recordMetrics(Collections.singletonList(metricsDO));

        // Step 7: 通知溯源用户
        CompletableFuture.runAsync(()-> sendTraceMessageToTracer(false, traceabilityDO, loginUser), traceExecutor);

        // Step 8: 构建返回对象
        WorkRegistrationSourceVo sourceVo = WorkRegistrationAssembler.buildWorkRegistrationSourceVo(registrationDO, fileDO);
        return TraceabilityAssembler.buildTraceabilityResultVo(
                traceabilityNumber,
                traceabilityDO.getStatus(),
                traceabilityDO.getSourceType(),
                sourceVo
        );
    }

    @Override
    public PageResult<TraceabilityRecordDto> queryTraceabilityRecords(TraceabilityRecordRequest req) {
        req.setUserId(TokenService.getLoginUser().getUserId());
        Page<TraceabilityRecordDto> recordDtos = traceabilityService.pageQueryTraceabilityRecord(req);
        PageResult<TraceabilityRecordDto> pageResult = new PageResult<>(recordDtos);
        pageResult.setData(recordDtos.getContent());
        return pageResult;
    }

    @Override
    public TraceabilityDetailVo traceabilityDetail(String traceabilityNumber) {
        TraceabilityDO traceabilityDO = traceabilityService.queryTraceability(traceabilityNumber);
        if (traceabilityDO == null) {
            return null;
        }

        TraceabilityDetailVo detailVo = TraceabilityAssembler.convertToDetailVo(traceabilityDO);

        // 溯源失败，无需加载登记信息
        if (!TraceabilityStatusEnum.SUCCESS.getType().equals(traceabilityDO.getStatus())) {
            return detailVo;
        }

        WorkRegistrationDO registrationDO = null;
        WorkRegistrationApplicationFileDO fileDO = null;

        if (TraceabilityTypeEnum.FILE_TRACEABILITY.getType().equals(traceabilityDO.getTraceabilityType())) {
            // 文件溯源
            registrationDO = workRegistrationService.findOne(QueryConvertUtils.toSpecification(QueryWorkRegistrationJpaSpec.builder()
                    .registrationNumberEq(traceabilityDO.getRegistrationNumber())
                    .deletedEq(SqlConstant.UN_DELETED)
                    .recycleEq(StatusEnum.NO.getCode())
                    .build()));
            if (registrationDO != null) {
                fileDO = workRegistrationApplicationFileService.findByApplicationId(registrationDO.getApplicationId());
            }
        } else {
            // 条件溯源
            String traceabilityParams = traceabilityDO.getTraceabilityParams();
            ConditionalTraceabilityRequest req = JSON.parseObject(traceabilityParams, ConditionalTraceabilityRequest.class);
            WorkFileQueryResultDto fileResult = workRegistrationApplicationFileService.findByConditionalTraceabilityRequest(req);
            if (fileResult != null) {
                registrationDO = workRegistrationService.findOne(QueryConvertUtils.toSpecification(QueryWorkRegistrationJpaSpec.builder()
                        .registrationNumberEq(fileResult.getRegistrationNumber())
                        .recycleEq(StatusEnum.NO.getCode())
                        .deletedEq(SqlConstant.UN_DELETED)
                        .build()));
                if (registrationDO != null) {
                    fileDO = workRegistrationApplicationFileService.findByApplicationId(registrationDO.getApplicationId());
                }
            }
        }

        // 缺少数据，直接返回
        if (registrationDO == null || fileDO == null) {
            return detailVo;
        }

        WorkRegistrationSourceVo sourceVo = WorkRegistrationAssembler.buildWorkRegistrationSourceVo(registrationDO, fileDO);
        detailVo.setSourceWorkInfo(sourceVo);

        return detailVo;
    }

    @Override
    public void exportTraceabilityRecord(TraceabilityRecordRequest req, HttpServletResponse response, String type) {
        PageResult<TraceabilityRecordDto> traceabilityVoPageResult = queryTraceabilityRecords(req);
        UniversalExportUtil.exportStream(traceabilityVoPageResult.getData(), TraceabilityRecordDto.class, "溯源记录", type, response);
    }

    @Override
    public BatchTraceabilityVo batchAddTraceability(MultipartFile file) {

        LoginUser loginUser = TokenService.getLoginUser();

        //记录无效行id
        List<Integer> invalidIds = new ArrayList<>();
        List<ConditionalTraceabilityExcelRow> validRows = new ArrayList<>();

        try {
            List<ConditionalTraceabilityExcelRow> rows = EasyExcel.read(file.getInputStream())
                    .head(ConditionalTraceabilityExcelRow.class)
                    .sheet()
                    .doReadSync();

            for (ConditionalTraceabilityExcelRow row : rows) {
                //条件校验
                boolean hasAnyRequiredField =
                        StringUtils.isNotBlank(row.getFileName()) ||
                                StringUtils.isNotBlank(row.getRegistrationNumber()) ||
                                StringUtils.isNotBlank(row.getFileHash());

               /* boolean timeValid = true;
                //时间校验
                try {
                    if (StringUtils.isNotBlank(row.getStartTime())) {
                        LocalDateTime.parse(row.getStartTime().trim());
                    }
                    if (StringUtils.isNotBlank(row.getEndTime())) {
                        LocalDateTime end = LocalDateTime.parse(row.getEndTime().trim());
                        if (StringUtils.isNotBlank(row.getStartTime())) {
                            LocalDateTime start = LocalDateTime.parse(row.getStartTime().trim());
                            if (start.isAfter(end)) {
                                timeValid = false;
                            }
                        }
                    }
                } catch (DateTimeParseException e) {
                    timeValid = false;
                }*/

                if (!hasAnyRequiredField) {
                    invalidIds.add(row.getId());
                } else {
                    validRows.add(row);
                }
            }

        } catch (Exception e) {
            log.error("batchAddTraceability parse excel error: {}", e.getMessage(), e);
        }

        if (!validRows.isEmpty()) {
            asyncTraceability(loginUser, validRows);
        }

        return BatchTraceabilityVo.builder()
                .total(invalidIds.size() + validRows.size())
                .errors(invalidIds).build();

    }

    private void asyncTraceability(LoginUser loginUser, List<ConditionalTraceabilityExcelRow> validRows) {
        CompletableFuture.runAsync(() -> {

            // 构造请求列表
            List<ConditionalTraceabilityRequest> requestList = validRows.stream().map(row -> {
                /*LocalDateTime start = StringUtils.isNotBlank(row.getStartTime())
                        ? LocalDateTime.parse(row.getStartTime().trim())
                        : null;

                LocalDateTime end = StringUtils.isNotBlank(row.getEndTime())
                        ? LocalDateTime.parse(row.getEndTime().trim())
                        : null;*/

                return ConditionalTraceabilityRequest.builder()
                        .rowIndex(row.getId())
                        .fileName(row.getFileName())
                        .registrationNumber(row.getRegistrationNumber())
                        .hash(row.getFileHash())
/*                        .startTime(start)
                        .endTime(end)*/
                        .build();
            }).collect(Collectors.toList());

            //溯源查询
            List<BatchTraceabilityResult> traceResults =
                    workRegistrationApplicationService.batchQueryRegistrationApplication(requestList);

            //入库
            List<TraceabilityDO> traceabilityDOList = toTraceabilityDOList(traceResults, loginUser);
            traceabilityService.batchSave(traceabilityDOList);
            List<TraceMetricsDO> traceMetricsDOS = traceabilityDOList.stream()
                    .map(e -> TraceMetricsDO.builder().traceNo(e.getTraceabilityNumber())
                    .traceUserId(loginUser.getUserId())
                    .traceType(TraceabilityTypeEnum.HASH_TRACEABILITY.getType())
                    .status(e.getStatus())
                    .createTime(LocalDateTime.now())
                    .build()).collect(Collectors.toList());
            recordMetrics(traceMetricsDOS);
            sendTraceMessageToTracer(true, null, loginUser);
        }, traceExecutor);
    }

    public List<TraceabilityDO> toTraceabilityDOList(
            List<BatchTraceabilityResult> batchTraceabilityResults,
            LoginUser loginUser
    ) {

        List<TraceabilityDO> traceabilityDOS = new ArrayList<>();

        for (BatchTraceabilityResult result : batchTraceabilityResults) {
            ConditionalTraceabilityRequest req = result.getRequest();
            WorkRegistrationSourceVo match = result.getMatchedRecord();

            boolean matched = match != null;
            boolean hasHash = req.getHash() != null && !req.getHash().trim().isEmpty();

            String sourceType = matched && hasHash ? TraceabilitySourceTypeEnum.FILE_HASH.getType() : null;

            TraceabilityDO.TraceabilityDOBuilder builder = TraceabilityDO.builder()
                    .registrationNumber(matched ? match.getRegistrationNumber() : null)
                    .traceabilityParams(JSON.toJSONString(req))
                    .traceabilityType(TraceabilityTypeEnum.HASH_TRACEABILITY.getType())
                    .sourceType(sourceType)
                    .deleted(SqlConstant.UN_DELETED)
                    .creatorId(loginUser.getUserId())
                    .creator(loginUser.getUsername())
                    .traceabilityNumber(TRACEABILITY_PREFIX + IdUtil.getSnowflakeNextIdStr())
                    .status(matched ? TraceabilityStatusEnum.SUCCESS.getType() : TraceabilityStatusEnum.FAIL.getType())
                    .createTime(LocalDateTime.now());
            traceabilityDOS.add(builder.build());
        }

        return traceabilityDOS;
    }


    @Override
    public TraceabilityResultVo conditionTraceability(ConditionalTraceabilityRequest req) {
        LoginUser loginUser = TokenService.getLoginUser();

        String traceabilityNumber = TRACEABILITY_PREFIX + IdUtil.getSnowflakeNextIdStr();

        WorkFileQueryResultDto fileDO = workRegistrationApplicationFileService.findByConditionalTraceabilityRequest(req);

        TraceabilityDO traceabilityDO = (Objects.nonNull(fileDO))
                ? TraceabilityAssembler.buildTraceabilityDO(fileDO, traceabilityNumber)
                : TraceabilityAssembler.buildEmptyTraceabilityDO(traceabilityNumber);

        TraceabilityResultVo resultVo = TraceabilityAssembler.buildTraceabilityResultVo(fileDO, traceabilityNumber);

        traceabilityDO.setCreator(loginUser.getUsername());
        traceabilityDO.setCreatorId(loginUser.getUserId());
        traceabilityDO.setTraceabilityParams(JSON.toJSONString(req));
        resultVo.setTraceabilityStatus(traceabilityDO.getStatus());

        if (TraceabilityStatusEnum.SUCCESS.getType().equals(traceabilityDO.getStatus()) && StringUtils.isNotBlank(req.getHash())) {
            resultVo.setSourceType(TraceabilitySourceTypeEnum.FILE_HASH.getType());
            traceabilityDO.setSourceType(TraceabilitySourceTypeEnum.FILE_HASH.getType());
        }

        traceabilityService.saveTraceabilityDO(traceabilityDO);
        TraceMetricsDO metricsDO = TraceMetricsDO.builder().traceNo(traceabilityDO.getTraceabilityNumber())
                .traceUserId(loginUser.getUserId())
                .traceType(TraceabilityTypeEnum.HASH_TRACEABILITY.getType())
                .status(traceabilityDO.getStatus())
                .createTime(LocalDateTime.now())
                .build();
        recordMetrics(Collections.singletonList(metricsDO));

        //发送站内通知消息
        sendTraceMessageToTracer(false, traceabilityDO, loginUser);
        return resultVo;
    }

    private void recordMetrics(List<TraceMetricsDO> traceMetricsDOS) {
        traceMetricsService.batchSave(traceMetricsDOS);
    }

    @Override
    public void batchUpdateTraceabilityLogStatus(List<Long> ids, String status) {
        List<Integer> integerIds = ids.stream().map(Long::intValue).collect(Collectors.toList());
        traceabilityService.batchUpdateTraceabilityLogStatus(integerIds, status);
    }

    @Override
    public PageResult<TraceabilityAdminQueryResponse> queryTraceabilityAdminList(TraceabilityAdminQueryRequest req) {
        return traceabilityService.queryTraceabilityAdminList(req);
    }

    @Override
    public StatsVo traceStatistics(Integer days) {
        return traceMetricsService.traceStatistics(days);
    }

    private void sendTraceMessageToTracer(boolean isBatch, TraceabilityDO traceabilityDO, LoginUser loginUser) {
        String userContent = "您的溯源认证已完成，请前往查看详情。";
        if (isBatch) {
            userContent = MessageEnum.BATCH_TRACEABILITY_USER.getContent();
        } else {
            TraceabilityTypeEnum type = TraceabilityTypeEnum.fromType(traceabilityDO.getTraceabilityType());
            if (Objects.nonNull(type)) {
                switch (type) {
                    case FILE_TRACEABILITY:
                        userContent = String.format(
                                MessageEnum.TRACEABILITY_USER.getContent(),
                                traceabilityDO.getTraceabilityNumber(),
                                traceabilityDO.getFileName(),
                                TraceabilityStatusEnum.getDesc(traceabilityDO.getStatus())
                        );
                        break;
                    case HASH_TRACEABILITY:
                        userContent = String.format(
                                MessageEnum.CONDITION_TRACEABILITY_USER.getContent(),
                                traceabilityDO.getTraceabilityNumber(),
                                TraceabilityStatusEnum.getDesc(traceabilityDO.getStatus())
                        );
                        break;
                }
            }
        }
        messageNotifyService.sendMessageNotify(
                loginUser.getUserId(),
                null,
                userContent,
                MessageEnum.TRACEABILITY_USER.getSummary(),
                MessageCategoryConstant.TRACEABILITY_MESSAGE,
                MessageTypeEnum.INTERNAL.getType(),
                StatusEnum.YES.getCode(),
                MessageEnum.TRACEABILITY_USER.getLevel()
        );
    }
}
