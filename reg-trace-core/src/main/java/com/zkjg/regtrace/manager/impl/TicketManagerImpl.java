package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.MessageCategoryConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.constants.TicketConstant;
import com.zkjg.regtrace.common.enums.CommunicationTypeEnum;
import com.zkjg.regtrace.common.enums.MessageEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.enums.TicketStatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.DateUtil;
import com.zkjg.regtrace.common.utils.TicketNoGenerator;
import com.zkjg.regtrace.manager.TicketManager;
import com.zkjg.regtrace.persistence.entity.*;
import com.zkjg.regtrace.persistence.vo.common.AttachmentInfo;
import com.zkjg.regtrace.persistence.vo.request.ticket.*;
import com.zkjg.regtrace.persistence.vo.response.ticket.*;
import com.zkjg.regtrace.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单Manager实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class TicketManagerImpl implements TicketManager {

    @Resource
    private TicketService ticketService;

    @Resource
    private TicketCommunicationService ticketCommunicationService;

    @Resource
    private TicketAttachmentService ticketAttachmentService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private MessageNotifyService messageNotifyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTicket(TicketCreateRequest request) {
        Integer creatorId = TokenService.getLoginUser().getUserId();
        // 创建工单
        TicketDO ticket = new TicketDO();
        BeanUtils.copyProperties(request, ticket);
        ticket.setTicketNo(TicketNoGenerator.generate()); // 生成工单编号
        ticket.setCreatorId(creatorId);
        ticket.setStatus(TicketStatusEnum.PENDING_ASSIGNMENT.getCode());
        ticket.setDeleted(SqlConstant.UN_DELETED);
        ticket.setCreateTime(LocalDateTime.now());
        ticket = ticketService.save(ticket);

        // 创建初始交流记录
        TicketCommunicationDO communication = TicketCommunicationDO.builder()
                .ticketId(ticket.getId())
                .type(CommunicationTypeEnum.USER_SUBMIT.getCode())
                .content(request.getDescription())
                .operatorId(creatorId)
                .build();
        ticketCommunicationService.save(communication);

        // 保存附件
        if (!CollectionUtils.isEmpty(request.getAttachments())) {
            saveAttachments(ticket.getId(), communication.getId(), request.getAttachments());
        }
        // 分配客服
        assignTicket(ticket.getId());
        return ticket.getId();
    }

    @Override
    public PageResult<TicketListVO> queryTicketList(TicketQueryRequest request) {
        Specification<TicketDO> spec = buildTicketSpecification(request);
        PageRequest pageRequest = PageRequest.of(request.getPage()-CommonConstant.ONE_INT, request.getSize(),
                Sort.by(Sort.Order.desc("urgency"), Sort.Order.asc("status")));

        Page<TicketDO> page = ticketService.findAll(spec, pageRequest);
        List<TicketListVO> list = convertToTicketListVO(page.getContent());

        PageResult<TicketListVO> result = new PageResult<>(page);
        result.setData(list);
        return result;
    }

    @Override
    public TicketDetailVO getTicketDetail(Long ticketId) {
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));

        TicketDetailVO detailVO = new TicketDetailVO();
        BeanUtils.copyProperties(ticket, detailVO);

        // 收集需要查询的用户ID并批量查询
        Set<Integer> userIds = new HashSet<>();
        if (ticket.getCreatorId() != null) {
            userIds.add(ticket.getCreatorId());
        }
        if (ticket.getAssignedToId() != null) {
            userIds.add(ticket.getAssignedToId());
        }

        Map<Integer, SysUserDO> userMap = sysUserService.findUsersByIds(new ArrayList<>(userIds));

        // 设置用户名称
        if (ticket.getCreatorId() != null) {
            SysUserDO creator = userMap.get(ticket.getCreatorId());
            if (creator != null) {
                detailVO.setCreatorName(creator.getUsername());
            }
        }
        if (ticket.getAssignedToId() != null) {
            SysUserDO assignedTo = userMap.get(ticket.getAssignedToId());
            if (assignedTo != null) {
                detailVO.setAssignedToName(assignedTo.getUsername());
            }
        }

        // 获取交流记录
        List<TicketCommunicationDO> communications = ticketCommunicationService.findByTicketId(ticketId);
        detailVO.setCommunications(convertToCommunicationVO(communications));

        // 只获取提交时的附件（USER_SUBMIT类型的交流记录关联的附件）
        List<TicketAttachmentDO> submitAttachments = new ArrayList<>();
        for (TicketCommunicationDO communication : communications) {
            if (CommunicationTypeEnum.USER_SUBMIT.getCode().equals(communication.getType())) {
                List<TicketAttachmentDO> attachments = ticketAttachmentService.findByCommunicationId(communication.getId());
                submitAttachments.addAll(attachments);
                break; // 只需要找到第一条用户提交的记录即可
            }
        }
        detailVO.setAttachments(convertToAttachmentVO(submitAttachments));

        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmTicket(Long ticketId) {
        Integer operatorId = TokenService.getLoginUser().getUserId();
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));

        if (!ticket.getStatus().equals(TicketStatusEnum.PENDING_CONFIRMATION.getCode())) {
            throw new BusinessException("工单状态不正确");
        }

        if (!ticket.getAssignedToId().equals(operatorId)) {
            throw new BusinessException("只能确认分配给自己的工单");
        }

        ticket.setStatus(TicketStatusEnum.IN_PROGRESS.getCode());
        ticket.setUpdateTime(LocalDateTime.now());
        ticketService.save(ticket);

        TicketCommunicationDO communication = TicketCommunicationDO.builder()
                .ticketId(ticketId)
                .type(CommunicationTypeEnum.CS_CONFIRM.getCode())
                .content("客服确认接收工单")
                .operatorId(operatorId)
                .build();
        ticketCommunicationService.save(communication);

        // 发送通知给用户
        String content = String.format(MessageEnum.TICKET_CONFIRMED.getContent(), ticket.getTicketNo());
        messageNotifyService.sendMessageNotify(ticket.getCreatorId(), operatorId, content,
                MessageEnum.TICKET_CONFIRMED.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                1, StatusEnum.NO.getCode(), MessageEnum.TICKET_CONFIRMED.getLevel());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectTicket(Long ticketId, String reason) {
        Integer operatorId = TokenService.getLoginUser().getUserId();
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));

        if (!ticket.getStatus().equals(TicketStatusEnum.PENDING_CONFIRMATION.getCode())) {
            throw new BusinessException("工单状态不正确");
        }

        if (!ticket.getAssignedToId().equals(operatorId)) {
            throw new BusinessException("只能拒绝分配给自己的工单");
        }

        // 记录拒绝原因
        TicketCommunicationDO communication = TicketCommunicationDO.builder()
                .ticketId(ticketId)
                .type(CommunicationTypeEnum.CS_REJECT.getCode())
                .content(reason)
                .operatorId(operatorId)
                .build();
        ticketCommunicationService.save(communication);

        // 重新分配
        assignTicket(ticketId, operatorId);

        // 发送拒绝通知给提交人
        String content = String.format(MessageEnum.TICKET_REJECTED.getContent(), ticket.getTicketNo(), reason);
        messageNotifyService.sendMessageNotify(ticket.getCreatorId(), 0, content,
                MessageEnum.TICKET_REJECTED.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                1, StatusEnum.NO.getCode(), MessageEnum.TICKET_REJECTED.getLevel());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void replyTicket(Long ticketId, TicketReplyRequest request) {
        Integer operatorId = TokenService.getLoginUser().getUserId();
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));

        if (!ticket.getStatus().equals(TicketStatusEnum.IN_PROGRESS.getCode())) {
            throw new BusinessException("工单状态不正确");
        }

        if (!ticket.getAssignedToId().equals(operatorId)) {
            throw new BusinessException("只能回复分配给自己的工单");
        }

        // 保存回复
        TicketCommunicationDO communication = TicketCommunicationDO.builder()
                .ticketId(ticketId)
                .type(CommunicationTypeEnum.CS_REPLY.getCode())
                .content(request.getContent())
                .operatorId(operatorId)
                .build();
        communication = ticketCommunicationService.save(communication);

        // 保存附件
        if (!CollectionUtils.isEmpty(request.getAttachments())) {
            saveAttachments(ticketId, communication.getId(), request.getAttachments());
        }

        // 更新工单状态
        ticket.setStatus(TicketStatusEnum.PENDING_USER_CONFIRMATION.getCode());
        ticket.setUpdateTime(LocalDateTime.now());
        ticketService.save(ticket);

        // 发送回复通知
        String content = String.format(MessageEnum.TICKET_REPLIED.getContent(), ticket.getTicketNo());
        messageNotifyService.sendMessageNotify(ticket.getCreatorId(), operatorId, content,
                MessageEnum.TICKET_REPLIED.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                1, StatusEnum.NO.getCode(), MessageEnum.TICKET_REPLIED.getLevel());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resolveTicket(Long ticketId) {
        Integer operatorId = TokenService.getLoginUser().getUserId();
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));

        if (!ticket.getStatus().equals(TicketStatusEnum.PENDING_USER_CONFIRMATION.getCode())) {
            throw new BusinessException("工单状态不正确");
        }

        if (!ticket.getCreatorId().equals(operatorId)) {
            throw new BusinessException("只能确认自己提交的工单");
        }

        ticket.setStatus(TicketStatusEnum.CLOSED.getCode());
        ticket.setCloseTime(LocalDateTime.now());
        ticketService.save(ticket);

        TicketCommunicationDO communication = TicketCommunicationDO.builder()
                .ticketId(ticketId)
                .type(CommunicationTypeEnum.USER_CONFIRM.getCode())
                .content("用户确认问题已解决")
                .operatorId(operatorId)
                .build();
        ticketCommunicationService.save(communication);

        // 发送确认解决通知给客服
        String content = String.format(MessageEnum.TICKET_USER_CONFIRMED.getContent(), ticket.getTicketNo());
        messageNotifyService.sendMessageNotify(ticket.getAssignedToId(), operatorId, content,
                MessageEnum.TICKET_USER_CONFIRMED.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                1, StatusEnum.NO.getCode(), MessageEnum.TICKET_USER_CONFIRMED.getLevel());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void followUpTicket(Long ticketId, TicketFollowUpRequest request, Integer operatorId) {
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));

        if (!ticket.getStatus().equals(TicketStatusEnum.PENDING_USER_CONFIRMATION.getCode())) {
            throw new BusinessException("工单状态不正确");
        }

        if (!ticket.getCreatorId().equals(operatorId)) {
            throw new BusinessException("只能追问自己提交的工单");
        }

        // 保存追问
        TicketCommunicationDO communication = TicketCommunicationDO.builder()
                .ticketId(ticketId)
                .type(CommunicationTypeEnum.USER_FOLLOW_UP.getCode())
                .content(request.getContent())
                .operatorId(operatorId)
                .build();
        communication = ticketCommunicationService.save(communication);

        // 保存附件
        if (!CollectionUtils.isEmpty(request.getAttachments())) {
            saveAttachments(ticketId, communication.getId(), request.getAttachments());
        }

        // 发送追问通知给客服
        String content = String.format(MessageEnum.TICKET_USER_FOLLOWUP.getContent(), ticket.getTicketNo());
        messageNotifyService.sendMessageNotify(ticket.getAssignedToId(), operatorId, content,
                MessageEnum.TICKET_USER_FOLLOWUP.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                1, StatusEnum.NO.getCode(), MessageEnum.TICKET_USER_FOLLOWUP.getLevel());

        // 更新工单状态
        ticket.setStatus(TicketStatusEnum.IN_PROGRESS.getCode());
        ticketService.save(ticket);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTimeoutTickets() {
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusHours(TicketConstant.TICKET_TIMEOUT_HOURS);
        List<TicketDO> timeoutTickets = ticketService.findTimeoutTickets(timeoutThreshold);

        for (TicketDO ticket : timeoutTickets) {
            try {
                // 记录超时
                TicketCommunicationDO communication = TicketCommunicationDO.builder()
                        .ticketId(ticket.getId())
                        .type(CommunicationTypeEnum.SYSTEM_OPERATION.getCode())
                        .content("超时未确认，自动拒绝接收")
                        .build();
                ticketCommunicationService.save(communication);

                // 重新分配
                assignTicket(ticket.getId(), ticket.getAssignedToId());

                // 发送超时重新分配通知给新客服
                String notifyContent = String.format(MessageEnum.TICKET_TIMEOUT_REJECT.getContent(), ticket.getTicketNo());
                messageNotifyService.sendMessageNotify(ticket.getAssignedToId(), 0, notifyContent,
                        MessageEnum.TICKET_TIMEOUT_REJECT.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                        1, StatusEnum.NO.getCode(), MessageEnum.TICKET_TIMEOUT_REJECT.getLevel());

            } catch (Exception e) {
                log.error("处理超时工单失败, ticketId: {}", ticket.getId(), e);
            }
        }
    }

    @Override
    public PageResult<TicketListVO> queryUserTickets(TicketQueryRequest request) {
        request.setCreatorId(TokenService.getLoginUser().getUserId());
        return queryTicketList(request);
    }

    @Override
    public PageResult<TicketListVO> queryCustomerServiceTickets(Integer csId, TicketQueryRequest request) {
        // 构建查询条件，包含分配给客服的条件
        Specification<TicketDO> spec = buildCustomerServiceTicketSpecification(csId, request);
        PageRequest pageRequest = PageRequest.of(request.getPage()-CommonConstant.ONE_INT, request.getSize(),
                Sort.by(Sort.Direction.DESC, "createTime"));

        Page<TicketDO> page = ticketService.findAll(spec, pageRequest);
        List<TicketListVO> list = convertToTicketListVO(page.getContent());

        PageResult<TicketListVO> result = new PageResult<>(page);
        result.setData(list);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleUserTimeoutTickets() {
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusDays(TicketConstant.USER_CONFIRM_TIMEOUT_DAYS);
        List<TicketDO> timeoutTickets = ticketService.findUserTimeoutTickets(timeoutThreshold);

        for (TicketDO ticket : timeoutTickets) {
            try {
                // 更新工单状态为已关闭
                ticket.setStatus(TicketStatusEnum.CLOSED.getCode());
                ticket.setCloseTime(LocalDateTime.now());
                ticketService.save(ticket);

                // 记录系统操作
                TicketCommunicationDO communication = TicketCommunicationDO.builder()
                        .ticketId(ticket.getId())
                        .type(CommunicationTypeEnum.SYSTEM_OPERATION.getCode())
                        .content("超时未确认，自动关闭工单")
                        .build();
                ticketCommunicationService.save(communication);

                log.info("工单 {} 因用户超时未确认已自动关闭", ticket.getId());

                // 发送自动关闭通知给用户
                String closeContent = String.format(MessageEnum.TICKET_AUTO_CLOSED.getContent(), ticket.getTicketNo());
                messageNotifyService.sendMessageNotify(ticket.getCreatorId(), 0, closeContent,
                        MessageEnum.TICKET_AUTO_CLOSED.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                        1, StatusEnum.NO.getCode(), MessageEnum.TICKET_AUTO_CLOSED.getLevel());
            } catch (Exception e) {
                log.error("处理用户超时工单失败, ticketId: {}", ticket.getId(), e);
            }
        }
    }

    @Override
    public List<CustomerServicePerformanceVO> getCustomerServicePerformance() {
        // 统计近一个月的数据（从上个月的今天到现在）
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneMonthAgo = now.minusMonths(1);
        List<SysUserDO> csUsers = sysUserService.findAllCustomerServiceUsers();
        List<CustomerServicePerformanceVO> performances = new ArrayList<>();
        for (SysUserDO user : csUsers) {
            Long confirmedCount = ticketService.countConfirmedTickets(user.getId(), oneMonthAgo, now);
            Long rejectedCount = ticketService.countRejectedTickets(user.getId(), oneMonthAgo, now);
            Long completedCount = ticketService.countCompletedTickets(user.getId(), oneMonthAgo, now);

            CustomerServicePerformanceVO vo = new CustomerServicePerformanceVO();
            vo.setCsId(user.getId());
            vo.setCsName(user.getUsername());
            vo.setConfirmedCount(confirmedCount);
            vo.setRejectedCount(rejectedCount);
            vo.setCompletedCount(completedCount);

            performances.add(vo);
        }

        return performances;
    }

    @Override
    public List<TicketCategoryStatisticsVO> getTicketCategoryStatistics() {
        // 统计近一个月的数据
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMonths(1);
        List<Object[]> countsByCategory = ticketService.countTicketsByCategory(startTime, endTime);
        long totalTickets = countsByCategory.stream().mapToLong(arr -> (Long) arr[1]).sum();

        List<TicketCategoryStatisticsVO> statistics = countsByCategory.stream().map(arr -> {
            TicketCategoryStatisticsVO vo = new TicketCategoryStatisticsVO();
            vo.setCategory((String)arr[0]);
            vo.setCount((Long) arr[1]);
            vo.setPercentage(calculatePercentage((Long) arr[1], totalTickets));
            return vo;
        }).collect(Collectors.toList());

        return statistics;
    }

    private Double calculatePercentage(Long count, long total) {
        return total == 0 ? 0 : (count * 100.0 / total);
    }

    /**
     * 分配工单给客服
     */
    private void assignTicket(Long ticketId) {
        assignTicket(ticketId, null);
    }

    /**
     * 分配工单给客服（排除某个客服）
     */
    private void assignTicket(Long ticketId, Integer excludeCsId) {
        TicketDO ticket = ticketService.findById(ticketId)
                .orElseThrow(() -> new BusinessException("工单不存在"));
        // 获取所有客服
        List<SysUserDO> customerServiceList = sysUserService.findByRoleName(TicketConstant.CUSTOMER_SERVICE_ROLE_NAME);
        if (CollectionUtils.isEmpty(customerServiceList)) {
            throw new BusinessException("系统中没有客服人员");
        }
        // 排除指定客服
        if (excludeCsId != null) {
            customerServiceList = customerServiceList.stream()
                    .filter(cs -> !cs.getId().equals(excludeCsId))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(customerServiceList)) {
            throw new BusinessException("没有可用的客服人员");
        }
        // 计算每个客服的工作量
        Map<Integer, Integer> workloadMap = new HashMap<>();
        int minWorkload = Integer.MAX_VALUE;
        for (SysUserDO cs : customerServiceList) {
            int workload = ticketService.countWorkload(cs.getId(),
                    Arrays.asList(TicketStatusEnum.PENDING_CONFIRMATION.getCode(),
                            TicketStatusEnum.IN_PROGRESS.getCode()));
            workloadMap.put(cs.getId(), workload);
            if (workload < minWorkload) {
                minWorkload = workload;
            }
        }

        // 找出工作量最少的客服
        List<Integer> candidateIds = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : workloadMap.entrySet()) {
            if (entry.getValue() == minWorkload) {
                candidateIds.add(entry.getKey());
            }
        }
        // 随机选择一个
        Random random = new Random();
        Integer selectedCsId = candidateIds.get(random.nextInt(candidateIds.size()));

        // 分配工单
        ticket.setAssignedToId(selectedCsId);
        ticket.setStatus(TicketStatusEnum.PENDING_CONFIRMATION.getCode());
        ticket.setUpdateTime(LocalDateTime.now());
        ticketService.save(ticket);

        log.info("工单 {} 已分配给客服 {}", ticketId, selectedCsId);

        // 发送通知给客服
        String assignContent = String.format(MessageEnum.TICKET_ASSIGNED.getContent(), ticket.getTicketNo(), ticket.getTitle());
        messageNotifyService.sendMessageNotify(selectedCsId, 0, assignContent,
                MessageEnum.TICKET_ASSIGNED.getSummary(), MessageCategoryConstant.SYSTEM_MESSAGE,
                1, StatusEnum.NO.getCode(), MessageEnum.TICKET_ASSIGNED.getLevel());
    }

    /**
     * 保存附件
     */
    private void saveAttachments(Long ticketId, Long communicationId, List<AttachmentInfo> attachmentInfos) {
        List<TicketAttachmentDO> attachments = attachmentInfos.stream().map(info -> {
            TicketAttachmentDO attachment = new TicketAttachmentDO();
            attachment.setTicketId(ticketId);
            attachment.setCommunicationId(communicationId);
            attachment.setFileName(info.getFileName());
            attachment.setFilePath(info.getFilePath());
            attachment.setUploadTime(LocalDateTime.now());
            return attachment;
        }).collect(Collectors.toList());

        ticketAttachmentService.saveAll(attachments);
    }

    /**
     * 构建查询条件
     */
    private Specification<TicketDO> buildTicketSpecification(TicketQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 删除标记
            predicates.add(criteriaBuilder.equal(root.get("deleted"), SqlConstant.UN_DELETED));
            // 工单编号
            if (request.getTicketNo() != null) {
                predicates.add(criteriaBuilder.like(root.get("ticketNo"), "%"+request.getTicketNo()+"%"));
            }
            // 标题
            if (StringUtils.isNotEmpty(request.getTitle())) {
                predicates.add(criteriaBuilder.like(root.get("title"), "%" + request.getTitle() + "%"));
            }
            // 分类
            if (request.getCategory() != null) {
                predicates.add(criteriaBuilder.equal(root.get("category"), request.getCategory()));
            }
            // 紧急程度
            if (request.getUrgency() != null) {
                predicates.add(criteriaBuilder.equal(root.get("urgency"), request.getUrgency()));
            }
            // 状态
            if (request.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }
            // 提交人名称（支持模糊查询）
            if (StringUtils.isNotEmpty(request.getCreatorName())) {
                // 使用模糊查询查找包含该姓名的所有用户
                List<SysUserDO> users = sysUserService.findUsersByUserNameContaining(request.getCreatorName());
                if (!users.isEmpty()) {
                    // 收集所有匹配用户的ID
                    List<Integer> userIds = users.stream()
                            .map(SysUserDO::getId)
                            .collect(Collectors.toList());
                    // 使用IN查询
                    predicates.add(root.get("submitterId").in(userIds));
                } else {
                    // 如果没有找到匹配的用户，添加一个永远为假的条件
                    predicates.add(criteriaBuilder.equal(root.get("submitterId"), -1));
                }
            }
            // 开始时间
            if (StringUtils.isNotEmpty(request.getStartTime())) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"),  DateUtil.strToLocalDateTime(request.getStartTime())));
            }
            // 结束时间
            if (StringUtils.isNotEmpty(request.getEndTime())) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), DateUtil.strToLocalDateTime(request.getEndTime())));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 转换为工单列表VO
     */
    private List<TicketListVO> convertToTicketListVO(List<TicketDO> tickets) {
        if (CollectionUtils.isEmpty(tickets)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的用户ID
        Set<Integer> userIds = new HashSet<>();
        for (TicketDO ticket : tickets) {
            if (ticket.getCreatorId() != null) {
                userIds.add(ticket.getCreatorId());
            }
            if (ticket.getAssignedToId() != null) {
                userIds.add(ticket.getAssignedToId());
            }
        }

        // 批量查询用户信息
        Map<Integer, SysUserDO> userMap = sysUserService.findUsersByIds(new ArrayList<>(userIds));

        return tickets.stream().map(ticket -> {
            TicketListVO vo = new TicketListVO();
            BeanUtils.copyProperties(ticket, vo);

            // 设置用户名称
            if (ticket.getCreatorId() != null) {
                SysUserDO creator = userMap.get(ticket.getCreatorId());
                if (creator != null) {
                    vo.setSubmitterName(creator.getUsername());
                }
            }
            if (ticket.getAssignedToId() != null) {
                SysUserDO assignedTo = userMap.get(ticket.getAssignedToId());
                if (assignedTo != null) {
                    vo.setAssignedToName(assignedTo.getUsername());
                }
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为交流记录VO
     */
    private List<CommunicationVO> convertToCommunicationVO(List<TicketCommunicationDO> communications) {
        if (CollectionUtils.isEmpty(communications)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的用户ID
        Set<Integer> userIds = new HashSet<>();
        for (TicketCommunicationDO comm : communications) {
            if (comm.getOperatorId() != null) {
                userIds.add(comm.getOperatorId());
            }
        }

        // 批量查询用户信息
        Map<Integer, SysUserDO> userMap = sysUserService.findUsersByIds(new ArrayList<>(userIds));

        return communications.stream().map(comm -> {
            CommunicationVO vo = new CommunicationVO();
            BeanUtils.copyProperties(comm, vo);

            // 设置操作人名称
            if (comm.getOperatorId() != null) {
                SysUserDO operator = userMap.get(comm.getOperatorId());
                if (operator != null) {
                    vo.setOperatorName(operator.getUsername());
                }
            }

            // 获取附件
            List<TicketAttachmentDO> attachments = ticketAttachmentService.findByCommunicationId(comm.getId());
            vo.setAttachments(convertToAttachmentVO(attachments));

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为附件VO
     */
    private List<AttachmentVO> convertToAttachmentVO(List<TicketAttachmentDO> attachments) {
        return attachments.stream().map(att -> {
            AttachmentVO vo = new AttachmentVO();
            BeanUtils.copyProperties(att, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 构建客服工单查询条件
     */
    private Specification<TicketDO> buildCustomerServiceTicketSpecification(Integer csId, TicketQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 删除标记
            predicates.add(criteriaBuilder.equal(root.get("deleted"), SqlConstant.UN_DELETED));
            // 分配给指定客服的工单
            predicates.add(criteriaBuilder.equal(root.get("assignedToId"), csId));

            // 工单编号
            if (request.getTicketNo() != null) {
                predicates.add(criteriaBuilder.like(root.get("ticketNo"), "%"+request.getTicketNo()+"%"));
            }
            // 标题
            if (StringUtils.isNotEmpty(request.getTitle())) {
                predicates.add(criteriaBuilder.like(root.get("title"), "%" + request.getTitle() + "%"));
            }
            // 分类
            if (request.getCategory() != null) {
                predicates.add(criteriaBuilder.equal(root.get("category"), request.getCategory()));
            }
            // 紧急程度
            if (request.getUrgency() != null) {
                predicates.add(criteriaBuilder.equal(root.get("urgency"), request.getUrgency()));
            }
            // 状态
            if (request.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }
            // 提交人名称（支持模糊查询）
            if (StringUtils.isNotEmpty(request.getCreatorName())) {
                // 使用模糊查询查找包含该姓名的所有用户
                List<SysUserDO> users = sysUserService.findUsersByUserNameContaining(request.getCreatorName());
                if (!users.isEmpty()) {
                    // 收集所有匹配用户的ID
                    List<Integer> userIds = users.stream()
                            .map(SysUserDO::getId)
                            .collect(Collectors.toList());
                    // 使用IN查询
                    predicates.add(root.get("submitterId").in(userIds));
                } else {
                    // 如果没有找到匹配的用户，添加一个永远为假的条件
                    predicates.add(criteriaBuilder.equal(root.get("submitterId"), -1));
                }
            }
            // 开始时间
            if (StringUtils.isNotEmpty(request.getStartTime())) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"),  DateUtil.strToLocalDateTime(request.getStartTime())));
            }
            // 结束时间
            if (StringUtils.isNotEmpty(request.getEndTime())) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), DateUtil.strToLocalDateTime(request.getEndTime())));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
