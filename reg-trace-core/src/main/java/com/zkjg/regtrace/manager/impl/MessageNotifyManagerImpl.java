package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.MessageOperationTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import com.zkjg.regtrace.manager.MessageNotifyManager;
import com.zkjg.regtrace.persistence.assembler.MessageNotifyAssembler;
import com.zkjg.regtrace.persistence.entity.MessageNotifyDO;
import com.zkjg.regtrace.persistence.repository.MessageNotifyRepository;
import com.zkjg.regtrace.persistence.vo.request.message.BatchUpdateMessageRequest;
import com.zkjg.regtrace.persistence.vo.request.message.MessageNotifyRequest;
import com.zkjg.regtrace.persistence.vo.response.message.MessageNotifyVo;
import com.zkjg.regtrace.service.MessageNotifyService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MessageNotifyManagerImpl implements MessageNotifyManager {

    @Resource
    private MessageNotifyService messageNotifyService;

    @Resource
    private MessageNotifyRepository messageNotifyRepository;

    @Override
    public PageResult<MessageNotifyVo> messages(MessageNotifyRequest req) {
        Integer userId = TokenService.getLoginUser().getUserId();
        req.setUserId(userId);

        Page<MessageNotifyDO> messages = messageNotifyService.messages(req);
        PageResult<MessageNotifyVo> pageResult = new PageResult<>(messages);
        List<MessageNotifyDO> content = messages.getContent();
        List<MessageNotifyVo> voList = content.stream()
                .map(MessageNotifyAssembler::toVo)
                .collect(Collectors.toList());
        pageResult.setData(voList);
        return pageResult;
    }

    @Override
    public void batchOperate(BatchUpdateMessageRequest req, HttpServletResponse response) {
        Integer userId = TokenService.getLoginUser().getUserId();
        if (req == null) {
            return;
        }

        MessageNotifyRequest messageNotifyRequest = CollectionUtils.isEmpty(req.getIds())
                ? Optional.ofNullable(req.getPageQuery())
                .orElseGet(() -> MessageNotifyRequest.builder().build())
                : MessageNotifyRequest.builder()
                .ids(req.getIds())
                .build();

        messageNotifyRequest.setUserId(userId);


        MessageOperationTypeEnum operationType = req.getOperationTypeEnum();
        List<MessageNotifyDO> messages = messageNotifyService.messageList(messageNotifyRequest);
        switch (operationType) {
            case MESSAGE_DELETE:
                messages.forEach(messageNotifyDO -> {
                    messageNotifyDO.setUpdateTime(LocalDateTime.now());
                    messageNotifyDO.setDeleted(SqlConstant.DELETED);
                });
                messageNotifyRepository.saveAll(messages);
                break;
            case MESSAGE_EXPORT:
                List<MessageNotifyVo> collect = messages.stream().map(MessageNotifyAssembler::toVo).collect(Collectors.toList());
                UniversalExportUtil.exportStream(collect, MessageNotifyVo.class, "消息记录", "csv", response);
                break;
            case MESSAGE_MARK:
                messages = messages.stream()
                        .filter(messageNotifyDO -> messageNotifyDO.getIsRead().equals(StatusEnum.NO.getCode())) // 筛选未读消息
                        .peek(messageNotifyDO -> {
                            messageNotifyDO.setUpdateTime(LocalDateTime.now()); // 设置更新时间
                            messageNotifyDO.setIsRead(StatusEnum.YES.getCode()); // 标记为已读
                        })
                        .collect(Collectors.toList());
                messageNotifyRepository.saveAll(messages);
                break;
            default:
                break;
        }
        req.setMessageSize(messages.size());
    }

    @Override
    public MessageNotifyVo detail(Integer id) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Optional<MessageNotifyDO> optional = messageNotifyRepository.findById(id);
        if (!optional.isPresent()) {
            return null;
        }
        MessageNotifyDO messageNotifyDO = optional.get();
        if (!userId.equals(messageNotifyDO.getReceiverId()) || !SqlConstant.UN_DELETED.equals(messageNotifyDO.getDeleted())) {
            return null;
        }
        MessageNotifyVo vo = MessageNotifyAssembler.toVo(messageNotifyDO);
        if (StatusEnum.NO.getCode() == vo.getIsRead()) {
            messageNotifyDO.setIsRead(StatusEnum.YES.getCode());
            messageNotifyDO.setUpdateTime(LocalDateTime.now());
            messageNotifyRepository.save(messageNotifyDO);
        }
        return vo;
    }

    @Override
    public Long unReadCount() {
        LoginUser loginUser = TokenService.getLoginUser();
        return messageNotifyService.unReadCount(loginUser.getUserId());
    }
}
