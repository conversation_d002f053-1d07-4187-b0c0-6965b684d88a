package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.log.OperateLogExportRequest;
import com.zkjg.regtrace.persistence.vo.request.log.OperateLogListRequest;
import com.zkjg.regtrace.persistence.vo.response.log.OperateLogListVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 9:04
 */
public interface OperateLogManager {
    PageResult<OperateLogListVo> selectOperateLogList(OperateLogListRequest req);

    List<OperateLogListVo> exportOperateLogList(OperateLogExportRequest req);
}
