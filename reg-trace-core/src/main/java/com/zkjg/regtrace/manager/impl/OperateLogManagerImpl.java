package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.manager.OperateLogManager;
import com.zkjg.regtrace.persistence.entity.SysOperationLogDO;
import com.zkjg.regtrace.persistence.vo.request.log.OperateLogExportRequest;
import com.zkjg.regtrace.persistence.vo.request.log.OperateLogListRequest;
import com.zkjg.regtrace.persistence.vo.response.log.OperateLogListVo;
import com.zkjg.regtrace.service.SysOperationLogService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 9:04
 */
@Service
public class OperateLogManagerImpl implements OperateLogManager {

    @Resource
    private SysOperationLogService sysOperationLogService;

    @Override
    public PageResult<OperateLogListVo> selectOperateLogList(OperateLogListRequest req) {
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Specification<SysOperationLogDO> specification = QueryConvertUtils.toSpecification(req);
        Page<SysOperationLogDO> all = sysOperationLogService.findAll(specification, pageRequest);

        //封装结果
        PageResult<OperateLogListVo> pageResult = new PageResult<>(all);
        pageResult.setData(BeanUtil.copyToList(all.getContent(), OperateLogListVo.class));
        return pageResult;
    }

    @Override
    public List<OperateLogListVo> exportOperateLogList(OperateLogExportRequest req) {
        Specification<SysOperationLogDO> specification = QueryConvertUtils.toSpecification(req);
        List<SysOperationLogDO> all = sysOperationLogService.findAll(specification);
        return BeanUtil.copyToList(all, OperateLogListVo.class);
    }
}
