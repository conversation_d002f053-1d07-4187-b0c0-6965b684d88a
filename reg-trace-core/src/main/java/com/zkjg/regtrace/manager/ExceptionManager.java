package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.exception.ExceptionLogRequest;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionLogVo;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionStatisticsVo;

import javax.servlet.http.HttpServletResponse;


public interface ExceptionManager {


    PageResult<ExceptionLogVo> queryLogs(ExceptionLogRequest req);

    ExceptionStatisticsVo statistics(Integer days);

    void exportLogs(HttpServletResponse response, ExceptionLogRequest req, String type);
}
