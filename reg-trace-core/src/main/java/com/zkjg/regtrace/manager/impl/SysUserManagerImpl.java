package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.factory.LoginStrategy;
import com.zkjg.regtrace.auth.token.factory.LoginStrategyFactory;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.*;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.MinioUtils;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.manager.SysUserManager;
import com.zkjg.regtrace.persistence.assembler.UserAssembler;
import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import com.zkjg.regtrace.persistence.entity.SysUserChangeHistoryDO;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.SysUserInfoDO;
import com.zkjg.regtrace.persistence.vo.request.login.LoginRequest;
import com.zkjg.regtrace.persistence.vo.request.register.RegisterRequest;
import com.zkjg.regtrace.persistence.vo.request.register.ResetPasswordRequest;
import com.zkjg.regtrace.persistence.vo.request.userinfo.*;
import com.zkjg.regtrace.persistence.vo.response.userinfo.ImageShowVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserHistoryVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserDetailVo;
import com.zkjg.regtrace.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SysUserManagerImpl implements SysUserManager {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserInfoService sysUserInfoService;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserChangeHistoryService sysUserChangeHistoryService;
    @Resource
    private TokenService tokenService;
    @Resource
    private LoginStrategyFactory loginStrategyFactory;

    @Resource
    private RedisUtil redisUtil;

    @Value("${minio.bucket}")
    private String bucketName;

    @Value("${minio.endpoint}")
    private String endpoint;

    @Resource
    private MinioUtils minioUtils;

    @Override
    @Transactional
    public void register(RegisterRequest req) {
        SysUserDO emailUser = sysUserService.findUserByEmail(req.getEmail());
        if (Objects.nonNull(emailUser)) {
            throw new BusinessException("该邮箱已被注册");
        }
        SysUserDO usernameUser = sysUserService.findUserByUsername(req.getUsername());
        if (Objects.nonNull(usernameUser)) {
            throw new BusinessException("该用户名已被注册");
        }
        String redisKey = String.format(RedisKeyConstant.EMAIL_VERIFICATION_CODE, req.getEmail(), EmailSendTypeEnum.REGISTER.getCode());
        String verificationCode = redisUtil.get(redisKey);
        if (StringUtils.isEmpty(verificationCode)) {
            throw new BusinessException("验证码已失效");
        }
        if (!verificationCode.equals(req.getVerificationCode())) {
            throw new BusinessException("验证码不正确");
        }
        SysUserDO sysUserDO = UserAssembler.initInsert(req);
        sysUserService.saveUser(sysUserDO);
        SysUserInfoDO userInfo = sysUserDO.getUserInfo();
        if (Objects.nonNull(userInfo)) {
            userInfo.setUserId(sysUserDO.getId());
            sysUserInfoService.saveUserInfo(userInfo);
        }

        List<Integer> roleIdByRoleName = sysRoleService.findRoleIdByRoleName(req.getPermission());
        sysUserRoleService.saveUserPermission(sysUserDO.getId(), roleIdByRoleName, sysUserDO.getUsername());

        SysUserChangeHistoryDO historyDO = UserAssembler.initUserChangeHistory(sysUserDO.getId(), sysUserDO.getUsername(), JSONObject.toJSONString(sysUserDO), OperateTypeEnum.CREATE);
        sysUserChangeHistoryService.saveUserHistory(historyDO);
    }

    @Override
    public String login(LoginRequest req) {
        LoginStrategy loginHandler = loginStrategyFactory.getLoginHandler(req.getLoginType());
        Authentication auth = loginHandler.authenticate(req);
        LoginUser loginUser = (LoginUser) auth.getPrincipal();
        loginUser.setLoginPlatform(req.getLoginPlatform());
        return tokenService.generateToken(loginUser);
    }

    @Override
    public UserDetailVo selectDetail(Integer userId) {
        SysUserDO userDO = sysUserService.findUserById(userId).orElseThrow(() -> new BusinessException("该用户不存在"));
        UserDetailVo userDetailVo = BeanUtil.copyProperties(userDO, UserDetailVo.class);

        List<SysRoleDO> userRole = sysRoleService.findByUserId(userId);

        List<String> typeList = Arrays.stream(RegisterPermissionEnum.values()).map(RegisterPermissionEnum::getType).collect(Collectors.toList());
        List<String> collect = userRole.stream().map(SysRoleDO::getRoleName).filter(typeList::contains).collect(Collectors.toList());

        userDetailVo.setPermission(collect);
        if (userDO.getUserType().equals(UserTypeEnum.GOVERNMENT_USER.getCode()) || userDO.getUserType().equals(UserTypeEnum.ENTERPRISE_USER.getCode())) {
            SysUserInfoDO userInfo = sysUserInfoService.findByUserId(userId);
            userDetailVo.setUserInfo(UserAssembler.selectUserInfo(userInfo));
        }

        userDetailVo.setImageUrl(endpoint + "/" + bucketName);
        return userDetailVo;
    }

    @Override
    @Transactional
    public void editUser(EditUserInfoRequest req) {
        LoginUser loginUser = TokenService.getLoginUser();
        SysUserDO userDO = sysUserService.findUserById(req.getUserId()).orElseThrow(() -> new BusinessException("该用户不存在"));
        SysUserInfoDO userInfoDO = sysUserInfoService.findByUserId(userDO.getId());
        if (!req.getEmail().equals(userDO.getEmail())) {
            SysUserDO emailUser = sysUserService.findUserByEmail(req.getEmail());
            if (Objects.nonNull(emailUser) && !Objects.equals(emailUser.getId(), userDO.getId())) {
                throw new BusinessException("该邮箱已被注册");
            }
            String redisKey = String.format(RedisKeyConstant.EMAIL_VERIFICATION_CODE, req.getEmail(), EmailSendTypeEnum.CHANGE_EMAIL.getCode());
            String verificationCode = redisUtil.get(redisKey);
            if (StringUtils.isEmpty(req.getVerificationCode())) {
                throw new BusinessException("验证码不能为空");
            }
            if (StringUtils.isEmpty(verificationCode)) {
                throw new BusinessException("验证码已失效");
            }
            if (!verificationCode.equals(req.getVerificationCode())) {
                throw new BusinessException("验证码错误");
            }
        }
        UserAssembler.initEdit(req, userDO, userInfoDO, loginUser.getUsername());
        sysUserService.saveUser(userDO);

        if (Objects.nonNull(userInfoDO)) {
            sysUserInfoService.saveUserInfo(userInfoDO);
            userDO.setUserInfo(userInfoDO);
        }

        SysUserChangeHistoryDO historyDO = UserAssembler.initUserChangeHistory(loginUser.getUserId(), loginUser.getUsername(), JSONObject.toJSONString(userDO), OperateTypeEnum.UPDATE);
        sysUserChangeHistoryService.saveUserHistory(historyDO);
    }

    @Override
    public PageResult<UserHistoryVo> selectList(UserHistoryInfoListRequest req) {
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Specification<SysUserChangeHistoryDO> specification = QueryConvertUtils.toSpecification(req);
        Page<SysUserChangeHistoryDO> all = sysUserChangeHistoryService.findAll(specification, pageRequest);

        //封装结果
        PageResult<UserHistoryVo> pageResult = new PageResult<>(all);
        pageResult.setData(BeanUtil.copyToList(all.getContent(), UserHistoryVo.class));
        return pageResult;
    }

    @Override
    @Transactional
    public void resetUser(ResetUserinfoRequest req) {
        SysUserDO userDO = sysUserService.findUserById(TokenService.getLoginUser().getUserId()).orElseThrow(() -> new BusinessException("该用户不存在"));
        SysUserChangeHistoryDO historyDO = sysUserChangeHistoryService.selectUserHistory(req.getUserHistoryId()).orElseThrow(() -> new BusinessException("该记录不存在"));
        if (!historyDO.getUserId().equals(userDO.getId())) {
            throw new BusinessException("异常的请求信息");
        }
        String snapshotJson = historyDO.getSnapshotJson();
        SysUserDO userDO1 = JSON.parseObject(snapshotJson, SysUserDO.class);

        BeanUtil.copyProperties(userDO1, userDO, "createTime", "id", "password");
        sysUserService.saveUser(userDO);

        SysUserInfoDO userInfoDO = sysUserInfoService.findByUserId(TokenService.getLoginUser().getUserId());
        if (Objects.nonNull(userInfoDO)) {
            SysUserInfoDO userInfo = userDO1.getUserInfo();
            BeanUtil.copyProperties(userInfo, userInfoDO, "createTime", "id", "userType");
            sysUserInfoService.saveUserInfo(userInfoDO);

            userDO.setUserInfo(userInfoDO);
        }
        SysUserChangeHistoryDO history = UserAssembler.initUserChangeHistory(userDO.getId(), userDO.getUsername(), JSONObject.toJSONString(userDO), OperateTypeEnum.RESET);
        sysUserChangeHistoryService.saveUserHistory(history);
    }

    /**
     * 通过邮箱重置密码
     */
    @Override
    @Transactional
    public void resetPassword(ResetPasswordRequest req) {
        String emailCode = redisUtil.get(String.format(RedisKeyConstant.EMAIL_RESET_URL, req.getToken()));
        if (StringUtils.isEmpty(emailCode)) {
            throw new BusinessException("连接已失效");
        }
        if (!emailCode.equals(req.getEmail())) {
            throw new BusinessException("非法链接");
        }
        SysUserDO sysUserDO = sysUserService.findUserByEmail(req.getEmail());
        if (Objects.isNull(sysUserDO)) {
            throw new BusinessException("该用户不存在");
        }
        sysUserDO.setPassword(req.getPassword());
        sysUserDO.setModifyTime(LocalDateTime.now());
        sysUserService.saveUser(sysUserDO);
    }

    @Override
    @Transactional
    public void resetPermission(EditUserPermissionRequest req) {
        LoginUser loginUser = TokenService.getLoginUser();
        sysUserRoleService.deleteByUserId(loginUser.getUserId());
        sysUserRoleService.flush();
        List<Integer> roleIdByRoleName = sysRoleService.findRoleIdByRoleName(req.getPermission());
        sysUserRoleService.saveUserPermission(loginUser.getUserId(), roleIdByRoleName, loginUser.getUsername());
    }

    @Override
    @Transactional
    public void changeHeadImg(ChangeUserHeadImgRequest req) {
        SysUserDO userDO = sysUserService.findUserById(TokenService.getLoginUser().getUserId()).orElseThrow(() -> new BusinessException("该用户不存在"));
        userDO.setHeadImg(req.getHeadImg());
        sysUserService.saveUser(userDO);
    }

    /**
     * 用户信息上传影像件
     */
    @Override
    public ImageShowVo uploadInfoImage(MultipartFile file, LoginUser user, String module) {

        //图片格式校验
        String suffix;
        try {
            int indexOf = Objects.requireNonNull(file.getOriginalFilename()).lastIndexOf(".");
            suffix = file.getOriginalFilename().substring(indexOf + 1).toLowerCase();
        } catch (Exception e) {
            throw new BusinessException("图片格式错误,请重新上传！");
        }
        if (!CommonConstant.IMAGE_LIST.contains(suffix)) {
            throw new BusinessException("图片格式错误,请重新上传！");
        }

        //上传限制
        String uploadNumKey = String.format(RedisKeyConstant.REDIS_IMAGE_NUM, user.getUsername(), module);
        int imageNum = 0;
        if (redisUtil.hasKey(uploadNumKey)) {
            imageNum = Integer.parseInt(redisUtil.get(uploadNumKey));
            if (imageNum >= 7) {
                throw new BusinessException(RetCodeEnum.BUCKET_IMAGE_TOO_FREQUENTLY);
            }
        }

        //上传文件
        String fileName = String.format("/%s/%s/%s.%s", module, user.getUsername(), RandomUtil.randomString(8), suffix);
        minioUtils.upload(bucketName, fileName, file);

        redisUtil.setEx(String.format(RedisKeyConstant.REDIS_IMAGE_NUM, user.getUsername(), module), String.valueOf(imageNum + 1), 1, TimeUnit.HOURS);
        return ImageShowVo.builder().showUrl(endpoint + "/" + bucketName + fileName).imageUrl(fileName).build();
    }
}