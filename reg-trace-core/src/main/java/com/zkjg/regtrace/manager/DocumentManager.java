package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.document.DocumentQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.document.DocumentRequest;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterRequest;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentDetailVo;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentVo;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemVo;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:15
 */
public interface DocumentManager {

    PageResult<DocumentVo> selectList(DocumentQueryRequest req);

    DocumentDetailVo selectDetail(Integer id);

    Result<Boolean> deleteDocument(Integer id);

    Result<Boolean> editDocument(Integer id, DocumentRequest req);

    Result<Boolean> addDocument(DocumentRequest req);

    PageResult<DocumentVo> selectOperationGuide(HelpCenterQueryRequest request);

    void click(Integer id, HelpCenterRequest request);
}
