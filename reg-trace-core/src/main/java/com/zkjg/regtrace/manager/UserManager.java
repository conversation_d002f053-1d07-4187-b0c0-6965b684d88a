package com.zkjg.regtrace.manager;


import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.permission.UserAddRequest;
import com.zkjg.regtrace.persistence.vo.request.permission.UserManageEditRequest;
import com.zkjg.regtrace.persistence.vo.request.permission.UserListRequest;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserManageDetailVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserMenuListVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:34
 */
public interface UserManager {
    /**
     * 查询用户列表
     */
    PageResult<UserListVo> selectUserList(UserListRequest req);

    /**
     * 添加系统用户
     */
    Integer addUser(UserAddRequest req);

    /**
     * 查询用户详细信息
     */
    UserManageDetailVo selectUserDetail(Integer userId);

    /**
     * 编辑用户信息
     */
    void editUser(UserManageEditRequest req);

    /**
     * 删除用户
     */
    void deleteUser(Integer userId);

    /**
     * 查询角色列表
     */
    List<RoleListVo> selectRoleList();

    /**
     * 查看pc端用户菜单
     */
    List<UserMenuListVo> selectMenu(Integer userId);
}
