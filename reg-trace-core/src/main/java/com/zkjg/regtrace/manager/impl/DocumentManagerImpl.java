package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.manager.DocumentManager;
import com.zkjg.regtrace.persistence.entity.DocumentDO;
import com.zkjg.regtrace.persistence.entity.DocumentFileDO;
import com.zkjg.regtrace.persistence.jpa.DocumentJpaSpec;
import com.zkjg.regtrace.persistence.vo.request.document.DocumentQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.document.DocumentRequest;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterRequest;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentDetailVo;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentVo;
import com.zkjg.regtrace.service.DocumentFileService;
import com.zkjg.regtrace.service.DocumentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:26
 */
@Service
public class DocumentManagerImpl implements DocumentManager {

    @Resource
    private DocumentService documentService;

    @Resource
    private DocumentFileService documentFileService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public PageResult<DocumentVo> selectList(DocumentQueryRequest req) {

        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        DocumentJpaSpec documentJpaSpec = new DocumentJpaSpec();
        documentJpaSpec.setNameLike(req.getName());
        documentJpaSpec.setTypeEq(req.getType());
        documentJpaSpec.setIsPublicEq(req.getIsPublic());
        documentJpaSpec.setDeletedEq(SqlConstant.UN_DELETED);
        PageResult<DocumentVo> pageResult = selectDocumentVo(documentJpaSpec, pageRequest);
        Set<String> keys = redisUtil.keys("reg-trace:document:*");
        for (DocumentVo documentVo : pageResult.getData()) {
            for (String key : keys) {
                String value = redisUtil.get(key);
                String[] parts = key.split(":");
                Integer docId = Integer.valueOf(parts[3]);
                String type = parts[2];
                if (Objects.equals(documentVo.getId(), docId)) {
                    if ("view".equals(type)) {
                        documentVo.setViews(Long.valueOf(value));
                    } else if ("download".equals(type)) {
                        documentVo.setDownloads(Long.valueOf(value));
                    }
                }
            }
        }
        return pageResult;
    }

    @Override
    public DocumentDetailVo selectDetail(Integer id) {
        DocumentDO documentDO = documentService.findById(id);
        DocumentDetailVo documentDetailVo = new DocumentDetailVo();
        BeanUtils.copyProperties(documentDO, documentDetailVo);
        List<DocumentFileDO> fileDOS = documentFileService.findByDocumentId(id);
        List<DocumentDetailVo.DocumentFileVo> fileVos = fileDOS.stream().map(data -> {
            DocumentDetailVo.DocumentFileVo fileVo = new DocumentDetailVo.DocumentFileVo();
            BeanUtils.copyProperties(data, fileVo);
            return fileVo;
        }).sorted(Comparator.comparing(DocumentDetailVo.DocumentFileVo::getCreateTime)).collect(Collectors.toList());
        // 按 createTime 升序排序（最新在最后）
        // 设置完整文件列表
        documentDetailVo.setDocumentFiles(fileVos);
        // 获取 createTime 最大的一条记录，设置到 DocumentDetailVo 中
        if (!fileVos.isEmpty()) {
            DocumentDetailVo.DocumentFileVo latestFile = fileVos.get(fileVos.size() - 1);
            documentDetailVo.setFile(latestFile.getFile());
        }
        return documentDetailVo;
    }

    @Override
    @Transactional
    public Result<Boolean> editDocument(Integer id, DocumentRequest req) {
        DocumentDO documentDO = documentService.findById(id);
        if (Objects.isNull(documentDO)) {
            return Result.ofFailMsg("文档不存在");
        }
        documentDO.setDescription(req.getDescription());
        documentDO.setName(req.getName());
        documentDO.setType(req.getType());
        documentDO.setIsPublic(req.getIsPublic());
        documentService.update(documentDO);
        if (StringUtils.isNotBlank(req.getFile())) {
            DocumentFileDO documentFileDO = new DocumentFileDO();
            documentFileDO.setDocumentId(id);
            documentFileDO.setFile(req.getFile());
            documentFileDO.setDeleted(SqlConstant.UN_DELETED);
            documentFileService.save(documentFileDO);
        }
        return Result.ofSuccess();
    }

    @Override
    public Result<Boolean> deleteDocument(Integer id) {
        DocumentDO documentDO = documentService.findById(id);
        if (Objects.isNull(documentDO)) {
            return Result.ofFailMsg("文档不存在");
        }
        documentDO.setModifyTime(LocalDateTime.now());
        documentDO.setDeleted(SqlConstant.DELETED);
        documentService.update(documentDO);
        return Result.ofSuccess();
    }

    @Override
    @Transactional
    public Result<Boolean> addDocument(DocumentRequest req) {
        DocumentDO documentDO = new DocumentDO();
        BeanUtils.copyProperties(req, documentDO);
        documentDO.setDeleted(SqlConstant.UN_DELETED);
        documentDO.setViews(0L);
        documentDO.setDownloads(0L);
        documentService.save(documentDO);
        DocumentFileDO documentFileDO = new DocumentFileDO();
        documentFileDO.setDocumentId(documentDO.getId());
        documentFileDO.setFile(req.getFile());
        documentFileDO.setSize(req.getSize());
        documentFileDO.setDeleted(SqlConstant.UN_DELETED);
        documentFileService.save(documentFileDO);
        return Result.ofSuccess();
    }

    @Override
    public PageResult<DocumentVo> selectOperationGuide(HelpCenterQueryRequest req) {
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        DocumentJpaSpec documentJpaSpec = new DocumentJpaSpec();
        documentJpaSpec.setTypeEq(req.getType());
        documentJpaSpec.setNameLikeOrDescriptionLike(req.getCondition());
        documentJpaSpec.setDeletedEq(SqlConstant.UN_DELETED);
        return selectDocumentVo(documentJpaSpec, pageRequest);
    }

    @Override
    public void click(Integer id, HelpCenterRequest request) {
        String click = String.format(RedisKeyConstant.DOCUMENT_CLICK, request.getOperation(), id);
        if (redisUtil.hasKey(click)) {
            long count = Long.parseLong(redisUtil.get(click)); // 转成数字
            count += 1;
            redisUtil.set(click, Long.toString(count)); // 转回字符串再存入
        } else {
            long count;
            DocumentDO documentDO = documentService.findById(id);
            if (StringUtils.equals(request.getOperation(), "view")) {
                count = documentDO.getViews();
            } else {
                count = documentDO.getDownloads();
            }
            redisUtil.set(click, String.valueOf(count + 1));
        }
    }

    private PageResult<DocumentVo> selectDocumentVo(DocumentJpaSpec documentJpaSpec, PageRequest pageRequest) {
        Specification<DocumentDO> specification = QueryConvertUtils.toSpecification(documentJpaSpec);
        Page<DocumentDO> all = documentService.findAll(specification, pageRequest);
        List<Integer> documentIds = all.stream().map(DocumentDO::getId).collect(Collectors.toList());
        List<DocumentFileDO> documentFileDOS = documentFileService.findByDocumentIds(documentIds);
        // 根据 documentId 分组，并找出每组中 createTime 最大的 DocumentFileDO
        Map<Integer, DocumentFileDO> latestFileMap = documentFileDOS.stream()
                .collect(Collectors.groupingBy(
                        DocumentFileDO::getDocumentId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(DocumentFileDO::getCreateTime)),
                                optional -> optional.orElse(null)
                        )
                ));
        List<DocumentVo> documentVos = all.stream().map(data -> {
            DocumentVo documentVo = new DocumentVo();
            BeanUtils.copyProperties(data, documentVo);
            documentVo.setFile(latestFileMap.get(data.getId()).getFile());
            documentVo.setSize(latestFileMap.get(data.getId()).getSize());
            return documentVo;
        }).collect(Collectors.toList());
        //封装结果
        PageResult<DocumentVo> pageResult = new PageResult<>(all);
        pageResult.setData(documentVos);
        return pageResult;
    }
}
