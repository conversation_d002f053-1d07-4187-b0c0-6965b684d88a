package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.persistence.entity.SysPermissionDO;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionListVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:34
 */
public interface PermissionManager {

    /**
     * 查询菜单列表
     */
    List<PermissionListVo> selectPermissionList(PermissionListRequest req);

    /**
     * 新增菜单
     */
    Integer insertPermission(PermissionAddRequest req);

    /**
     * 编辑菜单
     */
    void updatePermission(PermissionEditRequest req);

    /**
     * 删除菜单
     */
    void deletePermission(Integer id);

    /**
     * 同一目录是否同名
     */
    boolean checkSameNameByParentId(String name, Integer parentId, Integer id);

    /**
     * 重新刷新下下标
     */
    void refreshSortNumber(SysPermissionDO permission);

    /**
     * 查询是否有子菜单
     */
    boolean hasChild(Integer id);

    /**
     * 查询菜单是否存在角色
     */
    boolean hasPermissionExistRole(Integer id);
}
