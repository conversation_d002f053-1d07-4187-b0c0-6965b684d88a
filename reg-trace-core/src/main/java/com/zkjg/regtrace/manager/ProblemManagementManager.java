package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementAddRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementEditRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementQueryRequest;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemManagementVO;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemVo;

/**
 * 问题管理Manager接口
 * <AUTHOR>
 */
public interface ProblemManagementManager {

    /**
     * 分页查询问题管理列表
     * @param request 查询请求
     * @return 分页结果
     */
    PageResult<ProblemManagementVO> queryProblemList(ProblemManagementQueryRequest request);

    /**
     * 添加问题
     * @param request 添加请求
     * @return 问题ID
     */
    Long addProblem(ProblemManagementAddRequest request);

    /**
     * 根据ID查询问题详情
     * @param id 问题ID
     * @return 问题详情
     */
    ProblemManagementVO getProblemDetail(Long id);

    /**
     * 编辑问题
     * @param request 编辑请求
     * @return 是否成功
     */
    Boolean editProblem(ProblemManagementEditRequest request);

    /**
     * 删除问题
     * @param id 问题ID
     * @return 是否成功
     */
    Boolean deleteProblem(Long id);

    /**
     * 帮助中心常见问题
     * @param request 查询请求
     * @return 帮助中心常见问题
     */
    PageResult<ProblemVo> selectProblemList(HelpCenterQueryRequest request);
}
