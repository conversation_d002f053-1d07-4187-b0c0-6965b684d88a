package com.zkjg.regtrace.manager;


import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.message.BatchUpdateMessageRequest;
import com.zkjg.regtrace.persistence.vo.request.message.MessageNotifyRequest;
import com.zkjg.regtrace.persistence.vo.response.message.MessageNotifyVo;

import javax.servlet.http.HttpServletResponse;

public interface MessageNotifyManager {

    PageResult<MessageNotifyVo> messages(MessageNotifyRequest req);

    void batchOperate(BatchUpdateMessageRequest req, HttpServletResponse response);

    MessageNotifyVo detail(Integer id);

    Long unReadCount();
}
