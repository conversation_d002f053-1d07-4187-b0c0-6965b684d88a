package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import com.zkjg.regtrace.manager.ExceptionManager;
import com.zkjg.regtrace.persistence.assembler.ExceptionAssembler;
import com.zkjg.regtrace.persistence.dto.exception.ExceptionDistributionDto;
import com.zkjg.regtrace.persistence.dto.exception.ExceptionFrequencyDto;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import com.zkjg.regtrace.persistence.vo.request.exception.ExceptionLogRequest;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionLogVo;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionStatisticsVo;
import com.zkjg.regtrace.service.ExceptionLogService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class ExceptionManagerImpl implements ExceptionManager {

    @Resource
    private ExceptionLogService exceptionLogService;

    @Override
    public PageResult<ExceptionLogVo> queryLogs(ExceptionLogRequest req) {
        Page<ExceptionLogDO> page = exceptionLogService.pageQueryLogs(req);
        PageResult<ExceptionLogVo> pageResult = new PageResult<>(page);
        List<ExceptionLogDO> content = page.getContent();
        List<ExceptionLogVo> exceptionLogVos = content.stream()
                .map(ExceptionAssembler::convertDoToVo)
                .collect(Collectors.toList());
        pageResult.setData(exceptionLogVos);
        return pageResult;
    }

    @Override
    public ExceptionStatisticsVo statistics(Integer days) {
        List<ExceptionDistributionDto> typeDistribution = exceptionLogService.distributionsByDays();
        List<ExceptionFrequencyDto> frequencyList = exceptionLogService.exceptionFrequency(days);
        return ExceptionStatisticsVo.builder()
                .exceptionFrequency(typeDistribution)
                .exception(frequencyList)
                .build();
    }

    @Override
    public void exportLogs(HttpServletResponse response, ExceptionLogRequest req, String type) {
        PageResult<ExceptionLogVo> exceptionLogVoPageResult = queryLogs(req);
        UniversalExportUtil.exportStream(exceptionLogVoPageResult.getData(), ExceptionLogVo.class, "异常日志", type, response);
    }
}
