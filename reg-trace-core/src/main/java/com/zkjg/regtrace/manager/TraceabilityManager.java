package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityAdminQueryRequest;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRecordRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description 溯源管理
 * @create 2025/6/12 8:56
 */
public interface TraceabilityManager {

    TraceabilityResultVo addTraceability(TraceabilityRequest traceabilityRequest);

    PageResult<TraceabilityRecordDto> queryTraceabilityRecords(TraceabilityRecordRequest req);

    TraceabilityDetailVo traceabilityDetail(String traceabilityNumber);

    void exportTraceabilityRecord(TraceabilityRecordRequest req, HttpServletResponse response, String type);

    BatchTraceabilityVo batchAddTraceability(MultipartFile file);

    TraceabilityResultVo conditionTraceability(ConditionalTraceabilityRequest req);

    void batchUpdateTraceabilityLogStatus(List<Long> ids, String status);

    PageResult<TraceabilityAdminQueryResponse> queryTraceabilityAdminList(TraceabilityAdminQueryRequest req);

    StatsVo traceStatistics(Integer days);
}
