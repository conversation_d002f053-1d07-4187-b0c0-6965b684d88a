package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.ContentManagerQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.DataTableRequest;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.TraceRecordRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 创作用户导航
 * @create 2025/6/12 8:56
 */
public interface CreateUserNavigationManager {
    /**
     * 创作登记统计
     * @return
     */

    List<DataTableVo> registerStatisticsList();

    /*
     * 导出数据表格
     */
    void exportRegisterStatistics(HttpServletResponse response, String type);

    /**
     * 获取数据折线图数据
     * @param request
     * @return
     */
    List<DataLineVo> increaseLine(DataTableRequest request);

    PageResult<ContentManagerVo> contentManager(@Valid ContentManagerQueryRequest req);

    void exportContentManager(@Valid ContentManagerQueryRequest req, HttpServletResponse response, String type);

    /**
     * 追踪统计 柱状图

     **/
    List<TraceStatisticsVo> traceStatistics();

    PageResult<TraceaRecordVo> queryTraceRecords(TraceRecordRequest req);


    TraceRecordDetailVo getTraceRecordDetail(Integer id);
}
