package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;

import java.util.Map;

/**
 * 生成结果文件 返回临时文件路径
 * <AUTHOR>
 * @Date 2025/6/13 10:23
 */
public interface GenerateDocumentManager {
    /**
     * 生成结果文件
     * @param data 模板数据
     * @param fileName 文件名
     * @param templatePath 模板路径
     * @return 结果文件路径
     */
    String generateFile(Map<String,Object> data, String fileName, String templatePath);
}
