package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.manager.RoleManager;
import com.zkjg.regtrace.persistence.assembler.PermissionAssembler;
import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import com.zkjg.regtrace.persistence.entity.SysRolePermissionDO;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.SysUserRoleDO;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleQueryUserListForAddVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleQueryUserListVo;
import com.zkjg.regtrace.service.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:34
 */
@Service
public class RoleManagerImpl implements RoleManager {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysRolePermissionService sysRolePermissionService;
    @Resource
    private TokenService tokenService;

    @Override
    @Transactional
    public void addRole(RoleAddRequest req) {
        SysRoleDO sysRoleDO = PermissionAssembler.initInsertRole(req, TokenService.getLoginUser().getUsername());
        sysRoleService.saveRole(sysRoleDO);

        //持久化rolePermission数据
        List<SysRolePermissionDO> collect = PermissionAssembler.initRolePermission(req.getPermissionList(), sysRoleDO.getId(), TokenService.getLoginUser().getUsername());
        sysRolePermissionService.saveRolePermission(collect);
    }

    @Override
    public PageResult<RoleListVo> selectRoleList(RoleListRequest req) {
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Specification<SysRoleDO> specification = QueryConvertUtils.toSpecification(req);
        Page<SysRoleDO> all = sysRoleService.findAll(specification, pageRequest);

        //封装结果
        PageResult<RoleListVo> pageResult = new PageResult<>(all);
        pageResult.setData(BeanUtil.copyToList(all.getContent(), RoleListVo.class));
        return pageResult;
    }

    @Override
    public List<PermissionDetailVo> selectPermissionListByRole(Integer roleId) {
        return sysRolePermissionService.findByRoleId(roleId);
    }

    @Override
    @Transactional
    public void editRole(RoleEditRequest req) {
        if (sysRoleService.findRoleNameUnique(req.getRoleName(), req.getId())) {
            throw new BusinessException("角色名称已存在");
        }
        //保存角色信息
        SysRoleDO role = sysRoleService.findById(req.getId()).orElseThrow(() -> new BusinessException("该角色不存在"));

        SysRoleDO roleDO = PermissionAssembler.initEditRole(req, role, TokenService.getLoginUser().getUsername());
        sysRoleService.saveRole(roleDO);

        //保存权限信息
        sysRolePermissionService.deleteByRoleId(role.getId());
        sysRolePermissionService.flush();

        //持久化rolePermission数据
        List<SysRolePermissionDO> collect = PermissionAssembler.initRolePermission(req.getPermissionList(), roleDO.getId(), TokenService.getLoginUser().getUsername());
        sysRolePermissionService.saveRolePermission(collect);

        //角色改动需要吊销已登录用户token
        List<SysUserDO> userDOList = sysUserService.findByRoleId(role.getId());
        userDOList.forEach(e -> tokenService.delLoginUserByLoginName(e.getUsername()));
    }

    @Override
    @Transactional
    public void deleteRoles(List<Integer> roleIds) {
        roleIds.forEach(e -> {
            SysRoleDO sysRoleDO = sysRoleService.findById(e).orElseThrow(() -> new BusinessException("数据异常"));
            if (StatusEnum.NO.getCode() == sysRoleDO.getCanDelete()) {
                throw new BusinessException("该角色不能删除");
            }
            if (!sysUserRoleService.findByRoleId(e).isEmpty()) {
                throw new BusinessException("角色已分配,无法删除");
            }
        });
        //删除role
        sysRoleService.deleteRoles(roleIds);
        //删除user-role
        sysUserRoleService.deleteByRoles(roleIds);
        //删除role-permission
        sysRolePermissionService.deleteByRoleIds(roleIds);
    }

    @Override
    public PageResult<RoleQueryUserListVo> selectUser(RoleQueryUserListRequest req) {
        checkSysRole(req.getRoleId());
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Page<SysUserDO> roleDOS = sysUserService.findUserByRoleId(req.getRoleId(), req.getUsername(), pageRequest);

        //封装结果
        PageResult<RoleQueryUserListVo> pageResult = new PageResult<>(roleDOS);
        pageResult.setData(BeanUtil.copyToList(roleDOS.getContent(), RoleQueryUserListVo.class));
        return pageResult;
    }

    @Override
    @Transactional
    public void deleteUserRoles(RoleDeleteOrAddUserListRequest req) {
        checkSysRole(req.getRoleId());
        sysUserRoleService.deleteByUserAndRole(req.getRoleId(), req.getUserIds());
    }

    @Override
    public PageResult<RoleQueryUserListForAddVo> selectUserForAdd(RoleQueryUserListForAddRequest req) {
        checkSysRole(req.getRoleId());
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Page<SysUserDO> roleDOS = sysUserService.findUserWithoutRoleId(req.getRoleId(), req.getUsername(), pageRequest);

        //封装结果
        PageResult<RoleQueryUserListForAddVo> pageResult = new PageResult<>(roleDOS);
        pageResult.setData(BeanUtil.copyToList(roleDOS.getContent(), RoleQueryUserListForAddVo.class));
        return pageResult;
    }

    @Override
    @Transactional
    public void addUserRoles(RoleDeleteOrAddUserListRequest req) {
        checkSysRole(req.getRoleId());
        List<SysUserRoleDO> collect = req.getUserIds().stream().map(e -> {
            SysUserRoleDO userRoleDO = SysUserRoleDO.builder().build();
            userRoleDO.setRoleId(req.getRoleId());
            userRoleDO.setUserId(e);
            userRoleDO.setCreateTime(LocalDateTime.now());
            userRoleDO.setCreator(TokenService.getLoginUser().getUsername());
            return userRoleDO;
        }).collect(Collectors.toList());
        sysUserRoleService.saveAll(collect);
    }

    public void checkSysRole(Integer roleId) {
        SysRoleDO sysRoleDO = sysRoleService.findById(roleId).orElseThrow(() -> new BusinessException("参数异常"));
        //超级管理员不可操作
        if (CommonConstant.SUPPER_ADMIN_CODE.equals(sysRoleDO.getRoleName())) {
            throw new BusinessException("不允许编辑该角色");
        }
    }
}
