package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.enums.FileTypeEnum;
import com.zkjg.regtrace.common.enums.TraceabilityStatusEnum;
import com.zkjg.regtrace.common.enums.TraceabilityTypeEnum;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.DateUtil;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import com.zkjg.regtrace.manager.CreateUserNavigationManager;
import com.zkjg.regtrace.persistence.assembler.CreateUserNavigationAssembler;
import com.zkjg.regtrace.persistence.dto.jpa.ListQueryWorkRegistrationApplicationJpaSpec;
import com.zkjg.regtrace.persistence.dto.jpa.QueryTraceabilityJpaSpec;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.TraceabilityDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.ContentManagerQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.DataTableRequest;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.TraceRecordRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.*;
import com.zkjg.regtrace.service.SysUserService;
import com.zkjg.regtrace.service.TraceabilityService;
import com.zkjg.regtrace.service.WorkRegistrationApplicationFileService;
import com.zkjg.regtrace.service.WorkRegistrationApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 8:56
 */
@Service
@Slf4j
public class CreateUserNavigationManagerImpl implements CreateUserNavigationManager {

    @Resource
    private WorkRegistrationApplicationService workRegistrationApplicationService;

    @Resource
    private WorkRegistrationApplicationFileService workRegistrationApplicationFileService;

    @Resource
    private TraceabilityService traceabilityService;

    @Resource
    private SysUserService sysUserService;

    @Override
    public List<DataTableVo> registerStatisticsList() {
        List<DataTableVo> result = new ArrayList<>();
        Map<String, Long> videoCountMap = new HashMap<>();
        Map<String, Long> audioCountMap = new HashMap<>();
        Map<String, Long> imageCountMap = new HashMap<>();
        Integer userId = TokenService.getLoginUser().getUserId();

        for (int i = 0; i < 7; i++) {
            LocalDate day = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = day.atStartOfDay();
            LocalDateTime endOfDay = day.atTime(LocalTime.MAX);
            String dateStr = startOfDay.format(DateUtil.YYYY_MM_DD_PATTERN);

            // 查询某天的申请ID
            ListQueryWorkRegistrationApplicationJpaSpec jpaSpec = ListQueryWorkRegistrationApplicationJpaSpec.builder()
                    .applicantUserIdEq(userId)
                    .deletedEq(0)
                    .auditStatusEq(WorkRegistrationAuditStatusEnum.APPROVED.getCode())
                    .createTimeGtEq(startOfDay)
                    .createTimeLtEq(endOfDay)
                    .build();

            List<Integer> applicationIds = workRegistrationApplicationService.findAll(jpaSpec)
                    .stream()
                    .map(WorkRegistrationApplicationDO::getId)
                    .collect(Collectors.toList());

            if (!applicationIds.isEmpty()) {
                long videoCount = getFileCount(applicationIds, FileTypeEnum.VIDEO);
                long audioCount = getFileCount(applicationIds, FileTypeEnum.AUDIO);
                long imageCount = getFileCount(applicationIds, FileTypeEnum.IMAGE);
                videoCountMap.put(dateStr, videoCount);
                audioCountMap.put(dateStr, audioCount);
                imageCountMap.put(dateStr, imageCount);

                result.add(buildDataTableVo(FileTypeEnum.VIDEO, dateStr, videoCount, videoCountMap));
                result.add(buildDataTableVo(FileTypeEnum.AUDIO, dateStr, audioCount, audioCountMap));
                result.add(buildDataTableVo(FileTypeEnum.IMAGE, dateStr, imageCount, imageCountMap));
            } else {
                result.add(CreateUserNavigationAssembler.buildEmptyDataTableVo(FileTypeEnum.VIDEO, dateStr));
                result.add(CreateUserNavigationAssembler.buildEmptyDataTableVo(FileTypeEnum.AUDIO, dateStr));
                result.add(CreateUserNavigationAssembler.buildEmptyDataTableVo(FileTypeEnum.IMAGE, dateStr));
                videoCountMap.put(dateStr, 0L);
                audioCountMap.put(dateStr, 0L);
            }
        }

        result.sort(Comparator.comparing(DataTableVo::getDataTime).reversed());
        return result;
    }



    private long getFileCount(List<Integer> applicationIds, FileTypeEnum fileType) {
        QueryWorkRegistrationApplicationFileJpaSpec spec = QueryWorkRegistrationApplicationFileJpaSpec.builder()
                .applicationIdIn(applicationIds)
                .fileTypeEq(fileType.getCode())
                .deletedEq(0)
                .build();
        return workRegistrationApplicationFileService.count(spec);
    }

    private long getTraceCount(LocalDateTime startTime, LocalDateTime endTime, Integer status) {
        Integer userId = TokenService.getLoginUser().getUserId();
        QueryTraceabilityJpaSpec queryTraceabilityJpaSpec = QueryTraceabilityJpaSpec.builder()
                .creatorIdEq(userId)
                .createTimeGtEq(startTime)
                .createTimeLtEq(endTime)
                .statusEq(status)
                .deletedEq(0)
                .build();
        return traceabilityService.count(queryTraceabilityJpaSpec);
    }


    private long getFileCount(List<Integer> applicationIds, Integer fileType) {
        QueryWorkRegistrationApplicationFileJpaSpec spec = QueryWorkRegistrationApplicationFileJpaSpec.builder()
                .applicationIdIn(applicationIds)
                .fileTypeEq(fileType)
                .deletedEq(0)
                .build();
        return workRegistrationApplicationFileService.count(spec);
    }

    private DataTableVo buildDataTableVo(FileTypeEnum fileType, String dateStr, long todayCount, Map<String, Long> countMap) {
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String prevDateStr = date.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        long prevCount = countMap.getOrDefault(prevDateStr, 0L);
        // 累计数量
        int accumulateNum = (int) todayCount;
        // 增长率（相对于前一日）
        int sequentialGrowthRate;
        if (prevCount == 0) {
            // 如果前一天为 0，且今天有数据，则增长率为 100%；否则为 0%
            sequentialGrowthRate = (todayCount > 0) ? 100 : 0;
        } else {
            // 可为负数
            sequentialGrowthRate = (int) Math.round((todayCount - prevCount) * 100.0 / prevCount);
        }

        return CreateUserNavigationAssembler.buildDataTableVo(fileType, dateStr, todayCount, accumulateNum, sequentialGrowthRate);
    }

    @Override
    public List<DataLineVo> increaseLine(DataTableRequest request) {
        List<DataLineVo> result = new ArrayList<>();
        int days = request.getDayNum();
        Integer contentType = request.getContentType();
        Integer userId = TokenService.getLoginUser().getUserId();

        for (int i = 0; i < days; i++) {
            LocalDate day = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = day.atStartOfDay();
            LocalDateTime endOfDay = day.atTime(LocalTime.MAX);
            String dateStr = startOfDay.format(DateUtil.YYYY_MM_DD_PATTERN);

            // 查询某天的申请ID
            ListQueryWorkRegistrationApplicationJpaSpec jpaSpec = ListQueryWorkRegistrationApplicationJpaSpec.builder()
                    .applicantUserIdEq(userId)
                    .deletedEq(0)
                    .auditStatusEq(WorkRegistrationAuditStatusEnum.APPROVED.getCode())
                    .createTimeGtEq(startOfDay)
                    .createTimeLtEq(endOfDay)
                    .build();

            List<Integer> applicationIds = workRegistrationApplicationService.findAll(jpaSpec)
                    .stream()
                    .map(WorkRegistrationApplicationDO::getId)
                    .collect(Collectors.toList());

            if (!applicationIds.isEmpty()) {
                long count = getFileCount(applicationIds, contentType);
                result.add(CreateUserNavigationAssembler.buildDataLineVo(dateStr, count, contentType));
            } else {
                result.add(CreateUserNavigationAssembler.buildEmptyDataLineVo(dateStr, contentType));
            }
        }
        result.sort(Comparator.comparing(DataLineVo::getDataTime).reversed());
        return result;
    }

    @Override
    public PageResult<ContentManagerVo> contentManager(ContentManagerQueryRequest req) {
        //查询结果
        Page<ContentManagerVo> contentManagerVos = workRegistrationApplicationService.listContentManager(req);

        //封装结果
        PageResult<ContentManagerVo> pageResult = new PageResult<>(contentManagerVos);
        pageResult.setData(contentManagerVos.getContent());
        return pageResult;
    }

    @Override
    public void exportContentManager(ContentManagerQueryRequest req, HttpServletResponse response, String type) {
        Page<ContentManagerVo> contentManagerVos = workRegistrationApplicationService.listContentManager(req);
        export(contentManagerVos.getContent(), ContentManagerVo.class, "内容管理", type, response);
    }

    @Override
    public List<TraceStatisticsVo> traceStatistics() {
        List<TraceStatisticsVo> result = new ArrayList<>();

        for (int i = 0; i < 7; i++) {
            LocalDate day = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = day.atStartOfDay();
            LocalDateTime endOfDay = day.atTime(LocalTime.MAX);
            String dateStr = startOfDay.format(DateUtil.YYYY_MM_DD_PATTERN);
            long successNum = getTraceCount(startOfDay, endOfDay, TraceabilityStatusEnum.SUCCESS.getType());
            long failNum = getTraceCount(startOfDay, endOfDay, TraceabilityStatusEnum.FAIL.getType());
            TraceStatisticsVo traceStatisticsVo = TraceStatisticsVo.builder()
                    .successNum(successNum)
                    .failNum(failNum)
                    .dataTime(dateStr)
                    .build();
            result.add(traceStatisticsVo);
        }

        return result;
    }

    @Override
    public PageResult<TraceaRecordVo> queryTraceRecords(TraceRecordRequest req) {
        //查询结果
        Page<TraceaRecordVo> traceRecordVos = traceabilityService.queryTraceRecords(req);

        //封装结果
        PageResult<TraceaRecordVo> pageResult = new PageResult<>(traceRecordVos);
        pageResult.setData(traceRecordVos.getContent());
        return pageResult;
    }

    @Override
    public TraceRecordDetailVo getTraceRecordDetail(Integer id) {
        TraceabilityDO traceabilityDO = traceabilityService.queryTraceability(id);
        TraceRecordDetailVo traceRecordDetailVo = TraceRecordDetailVo.builder()
                .traceabilityNumber(traceabilityDO.getTraceabilityNumber())
                .createTime(traceabilityDO.getCreateTime())
                .traceabilityType(traceabilityDO.getTraceabilityType())
                .creator(traceabilityDO.getCreator())
                .build();
        ConditionalTraceabilityRequest req = ConditionalTraceabilityRequest.builder()
                .registrationNumber(traceabilityDO.getRegistrationNumber())
                .build();
        WorkRegistrationApplicationDO workRegistrationApplicationDO = workRegistrationApplicationService.findByRegistrationNumber(traceabilityDO.getRegistrationNumber());
        WorkRegistrationApplicationFileDO workRegistrationApplicationFileDO = workRegistrationApplicationFileService.findByApplicationId(workRegistrationApplicationDO.getId());
        SysUserDO sysUserDO = sysUserService.findUserById(workRegistrationApplicationDO.getApplicantUserId()).orElseThrow(() -> new BusinessException("该用户不存在"));
        RegisterVo registerVo = RegisterVo.builder()
                .registrationNumber(traceabilityDO.getRegistrationNumber())
                .workName(workRegistrationApplicationDO.getWorkName())
                .createUser(sysUserDO.getUsername())
                .originalWorkHash(workRegistrationApplicationFileDO.getOriginalWorkHash())
                .watermarkedWorkHash(workRegistrationApplicationFileDO.getWatermarkedWorkHash())
                .fileName(workRegistrationApplicationFileDO.getFileName())
                .createTime(workRegistrationApplicationDO.getCreateTime())
                .build();
        traceRecordDetailVo.setRegisterVo(registerVo);
        List<PropagationPathVo> propagationPathVoList = new ArrayList<>();
        PropagationPathVo registerPath = PropagationPathVo.builder()
                .createTime(registerVo.getCreateTime())
                .userName(registerVo.getCreateUser())
                .build();
        PropagationPathVo tracePath = PropagationPathVo.builder()
                .createTime(traceabilityDO.getCreateTime())
                .userName(traceabilityDO.getCreator())
                .build();
        propagationPathVoList.add(registerPath);
        propagationPathVoList.add(tracePath);
        traceRecordDetailVo.setPropagationPathVoList(propagationPathVoList);
        if (TraceabilityTypeEnum.FILE_TRACEABILITY.getType().equals(traceabilityDO.getTraceabilityType())) {
            WatermarkExtractRecordVo watermarkExtractRecordVo = WatermarkExtractRecordVo.builder()
                    .createTime(traceabilityDO.getCreateTime())
                    .logContent("提取水印信息")
                    .operator(traceabilityDO.getCreator())
                    .build();
            traceRecordDetailVo.setWatermarkExtractRecordVo(watermarkExtractRecordVo);
        }
        return traceRecordDetailVo;

    }

    @Override
    public void exportRegisterStatistics(HttpServletResponse response, String type) {
        List<DataTableVo> dataTableVos = this.registerStatisticsList();
        export(dataTableVos, DataTableVo.class, "创作登记统计", type, response);
    }

    private <T> void export(List<T> list, Class<T> clazz, String fileNamePrefix, String type, HttpServletResponse response) {
        try {
            // 确定导出格式，默认为EXCEL
            UniversalExportUtil.ExportFormat format = determineExportFormat(type);

            // 生成导出文件
            UniversalExportUtil.ExportFile file = UniversalExportUtil.export(
                    list,
                    clazz,
                    format,
                    fileNamePrefix + DateUtil.format(LocalDateTime.now(), DateUtil.YYYY_MM_DD)
            );

            // 设置响应头
            configureResponse(response, file);

            // 流式传输文件内容
            transferFileContent(file.getInputStream(), response.getOutputStream());
        } catch (Exception e) {
            log.debug("导出{}文件发生异常, error: {}", fileNamePrefix, e.getMessage());
        }
    }

    private UniversalExportUtil.ExportFormat determineExportFormat(String type) {
        try {
            return UniversalExportUtil.ExportFormat.valueOf(type.toUpperCase());
        } catch (BusinessException | NullPointerException e) {
            return UniversalExportUtil.ExportFormat.EXCEL;
        }
    }

    private void configureResponse(HttpServletResponse response, UniversalExportUtil.ExportFile file) throws UnsupportedEncodingException {
        response.setContentType(file.getContentType());
        response.setHeader("Content-Disposition", "attachment; filename=" +
                URLEncoder.encode(file.getFileName(), "UTF-8"));
    }

    private void transferFileContent(InputStream inputStream, OutputStream outputStream) throws IOException {
        byte[] buffer = new byte[8192];
        int len;
        try {
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
        } finally {
            // 手动关闭流，确保资源释放
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.debug("关闭输入流失败, error: {}", e.getMessage());
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.debug("关闭输出流失败, error: {}", e.getMessage());
                }
            }
        }
    }
}
