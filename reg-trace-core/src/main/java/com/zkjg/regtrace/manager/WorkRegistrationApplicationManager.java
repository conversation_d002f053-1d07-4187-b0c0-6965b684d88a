package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationFileRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationAuditRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationAuditDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 作品登记申请管理类
 * <AUTHOR>
 * @Date 2025/6/11 19:07
 */
public interface WorkRegistrationApplicationManager {

    /**
     * 提交音视频登记申请
     */
    Result<Boolean> apply(WorkRegistrationApplicationRequest req);

    /**
     * 审核音视频登记申请
     */
    Result<Boolean> audit(WorkRegistrationApplicationAuditRequest req);

    /**
     * 查询音视频登记申请列表
     */
    Result<PageResult<WorkRegistrationApplicationDetailVo>> list(QueryWorkRegistrationApplicationRequest req);

    /**
     * 回收站列表
     * @param req 查询条件
     * @return 回收站列表
     */
    Result<PageResult<WorkRegistrationApplicationDetailVo>> trashList(QueryWorkRegistrationApplicationRequest req);

    /**
     * 查询音视频登记审核列表
     */
    Result<PageResult<WorkRegistrationApplicationDetailVo>> listAudit(QueryWorkRegistrationApplicationRequest req);

    /**
     * 查询音视频登记申请/审核详情
     */
    Result<WorkRegistrationApplicationDetailVo> detail(Integer id);

    /**
     * 查询登记文件详情
     * @return
     */
    WorkRegistrationApplicationDetailVo queryWorkFileDetail(QueryWorkRegistrationApplicationFileRequest req);

    /**
     * 批量导入作品登记申请（CSV文件）
     * @param file CSV文件
     * @return 导入结果
     */
    Result<String> importWorkRegistrationApplication(MultipartFile file);

    /**
     * 逻辑删除
     * @param id 主键id
     * @return
     */
    Result<Boolean> trashMove(Integer id);

    /**
     * 逻辑删除恢复
     * @param id 主键id
     * @return
     */
    Result<Boolean> trashRestore(Integer id);

    /**
     * 查询审核详情
     * @param id 申请id
     * @return 审核详情
     */
    Result<WorkRegistrationApplicationAuditDetailVo> auditDetail(Integer id);

    /**
     * 导出登记申请详情为PDF
     * @param id 申请id
     * @return PDF文件输入流和文件名
     */
    Result<Map<String, Object>> exportDetailToPdf(Integer id);
}
