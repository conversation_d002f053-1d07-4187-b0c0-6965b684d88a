package com.zkjg.regtrace.manager.impl;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import com.zkjg.regtrace.manager.WorkRegistrationManager;
import com.zkjg.regtrace.persistence.dto.jpa.ListQueryWorkRegistrationApplicationJpaSpec;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationJpaSpec;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationRecordDO;
import com.zkjg.regtrace.persistence.jpa.WorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationFileListRequest;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationGrowthVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationRetraceVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationTrendVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationFileVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationRecordVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationContentDetailVo;
import com.zkjg.regtrace.service.SysUserService;
import com.zkjg.regtrace.service.WorkRegistrationApplicationFileService;
import com.zkjg.regtrace.service.WorkRegistrationApplicationService;
import com.zkjg.regtrace.service.WorkRegistrationRecordService;
import com.zkjg.regtrace.service.WorkRegistrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:45
 */
@Slf4j
@Service
public class WorkRegistrationManagerImpl implements WorkRegistrationManager {

    @Resource
    private WorkRegistrationApplicationFileService workRegistrationApplicationFileService;

    @Resource
    private WorkRegistrationApplicationService workRegistrationApplicationService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private WorkRegistrationRecordService workRegistrationRecordService;

    @Resource
    private WorkRegistrationService workRegistrationService;


    @Override
    public List<UserRegistrationTrendVo> getUserRegistrationTrend(String type, String date) {
        DateTimeFormatter inputFormatter;
        LocalDateTime start;
        LocalDateTime end;
        ChronoUnit unit;
        long stepCount;
        DateTimeFormatter formatter;
        int steps;

        switch (type.toLowerCase()) {
            case "day":
                inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                start = LocalDate.parse(date, inputFormatter).atStartOfDay();
                unit = ChronoUnit.HOURS;
                stepCount = 2;
                steps = 12; // 24小时 / 2小时
                formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
                break;
            case "month":
                inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                YearMonth ym = YearMonth.parse(date, inputFormatter);
                start = ym.atDay(1).atStartOfDay();
                unit = ChronoUnit.DAYS;
                stepCount = 1;
                steps = ym.lengthOfMonth(); // 实际天数
                formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
                break;
            case "year":
                inputFormatter = DateTimeFormatter.ofPattern("yyyy");
                int year = Integer.parseInt(date);
                start = LocalDate.of(year, 1, 1).atStartOfDay();
                unit = ChronoUnit.MONTHS;
                stepCount = 1;
                steps = 12;
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                break;
            default:
                throw new BusinessException("Unsupported type: " + type);
        }

        end = start.plus(stepCount * steps, unit);

        List<WorkRegistrationApplicationFileDO> all = fetchData(start, end);

        List<UserRegistrationTrendVo> result = new ArrayList<>();
        LocalDateTime cursor = start;

        while (cursor.isBefore(end)) {
            LocalDateTime next = cursor.plus(stepCount, unit);
            int image = 0, video = 0, audio = 0;

            for (WorkRegistrationApplicationFileDO file : all) {
                LocalDateTime createTime = file.getCreateTime();
                if (!createTime.isBefore(cursor) && createTime.isBefore(next)) {
                    Integer fileType = file.getFileType();
                    if (fileType != null) {
                        switch (fileType) {
                            case 0: audio++; break;
                            case 1: video++; break;
                            case 2: image++; break;
                        }
                    }
                }
            }

            UserRegistrationTrendVo vo = new UserRegistrationTrendVo();
            vo.setImageTotal(image);
            vo.setVideoTotal(video);
            vo.setAudioTotal(audio);
            String timeLabel;
            switch (type.toLowerCase()) {
                case "day":
                    timeLabel = cursor.format(DateTimeFormatter.ofPattern("HH:mm"))
                            + " - "
                            + next.format(DateTimeFormatter.ofPattern("HH:mm"));
                    break;
                case "month":
                    timeLabel = cursor.format(DateTimeFormatter.ofPattern("M月d日"));
                    break;
                case "year":
                    timeLabel = cursor.format(DateTimeFormatter.ofPattern("M月"));
                    break;
                default:
                    timeLabel = cursor.format(formatter) + " 至 " + next.format(formatter); // fallback
            }

            vo.setTime(timeLabel);
            result.add(vo);
            cursor = next;
        }

        return result;
    }

    @Override
    public List<UserRegistrationGrowthVo> getGrowthRate(String type, String date) {
        ChronoUnit unit;
        long stepCount;
        DateTimeFormatter timeFormat;
        LocalDateTime startCurrent;
        LocalDateTime startPrevious;
        int steps;

        switch (type.toLowerCase()) {
            case "month":
                YearMonth currentMonth = YearMonth.parse(date, DateTimeFormatter.ofPattern("yyyy-MM"));
                YearMonth previousMonth = currentMonth.minusMonths(1);
                steps = currentMonth.lengthOfMonth();
                unit = ChronoUnit.DAYS;
                stepCount = 1;
                timeFormat = DateTimeFormatter.ofPattern("M月d日");
                startCurrent = currentMonth.atDay(1).atStartOfDay();
                startPrevious = previousMonth.atDay(1).atStartOfDay();
                break;
            case "year":
                int year = Integer.parseInt(date);
                steps = 12;
                unit = ChronoUnit.MONTHS;
                stepCount = 1;
                timeFormat = DateTimeFormatter.ofPattern("M月");
                startCurrent = LocalDate.of(year, 1, 1).atStartOfDay();
                startPrevious = startCurrent.minusYears(1);
                break;
            default:
                throw new BusinessException("Unsupported type: " + type);
        }

        LocalDateTime endCurrent = startCurrent.plus(stepCount * steps, unit);
        LocalDateTime endPrevious = startPrevious.plus(stepCount * steps, unit);

        List<WorkRegistrationApplicationFileDO> currentData = fetchData(startCurrent, endCurrent);
        List<WorkRegistrationApplicationFileDO> previousData = fetchData(startPrevious, endPrevious);

        List<UserRegistrationGrowthVo> result = new ArrayList<>();

        for (int i = 0; i < steps; i++) {
            LocalDateTime curStart = startCurrent.plus(i * stepCount, unit);
            LocalDateTime curEnd = curStart.plus(stepCount, unit);
            LocalDateTime prevStart = startPrevious.plus(i * stepCount, unit);
            LocalDateTime prevEnd = prevStart.plus(stepCount, unit);

            int currImage = 0, currVideo = 0, currAudio = 0;
            int prevImage = 0, prevVideo = 0, prevAudio = 0;

            for (WorkRegistrationApplicationFileDO file : currentData) {
                LocalDateTime createTime = file.getCreateTime();
                if (!createTime.isBefore(curStart) && createTime.isBefore(curEnd)) {
                    Integer fileType = file.getFileType();
                    if (fileType != null) {
                        switch (fileType) {
                            case 0: currAudio++; break;
                            case 1: currVideo++; break;
                            case 2: currImage++; break;
                        }
                    }
                }
            }

            // 判断前一个时间段是否存在该日期（用于补0）
            boolean previousPeriodExists =
                    type.equals("month") && i < YearMonth.from(startPrevious).lengthOfMonth() ||
                            type.equals("year");

            if (previousPeriodExists) {
                for (WorkRegistrationApplicationFileDO file : previousData) {
                    LocalDateTime createTime = file.getCreateTime();
                    if (!createTime.isBefore(prevStart) && createTime.isBefore(prevEnd)) {
                        Integer fileType = file.getFileType();
                        if (fileType != null) {
                            switch (fileType) {
                                case 0: prevAudio++; break;
                                case 1: prevVideo++; break;
                                case 2: prevImage++; break;
                            }
                        }
                    }
                }
            }

            UserRegistrationGrowthVo vo = new UserRegistrationGrowthVo();
            vo.setImageGrowth(currImage - prevImage);
            vo.setVideoGrowth(currVideo - prevVideo);
            vo.setAudioGrowth(currAudio - prevAudio);
            vo.setTime(curStart.format(timeFormat));
            result.add(vo);
        }

        return result;
    }

    private List<WorkRegistrationApplicationFileDO> fetchData(LocalDateTime start, LocalDateTime end) {
        Integer userId = TokenService.getLoginUser().getUserId();
        ListQueryWorkRegistrationApplicationJpaSpec jpaSpec = new ListQueryWorkRegistrationApplicationJpaSpec();
        jpaSpec.setApplicantUserIdEq(userId);
        List<WorkRegistrationApplicationDO> applications = workRegistrationApplicationService.findAll(jpaSpec);
        List<Integer> applicationIds = applications.stream().map(WorkRegistrationApplicationDO::getId).collect(Collectors.toList());
        WorkRegistrationApplicationFileJpaSpec fileJpaSpec = new WorkRegistrationApplicationFileJpaSpec();
        fileJpaSpec.setCreateTimeGtEq(start);
        fileJpaSpec.setCreateTimeLt(end);
        fileJpaSpec.setApplicationIdIn(applicationIds);
        fileJpaSpec.setDeletedEq(SqlConstant.UN_DELETED);
        Specification<WorkRegistrationApplicationFileDO> specification = QueryConvertUtils.toSpecification(fileJpaSpec);
        return workRegistrationApplicationFileService.findAll(specification);
    }

    @Override
    public List<UserRegistrationRetraceVo> getRetrace() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        List<WorkRegistrationApplicationFileDO> files = fetchData(thirtyDaysAgo, now);
        return predictNext30DaysWithTimeKeys(files);
    }

    @Override
    public PageResult<WorkRegistrationApplicationFileVo> getContent(QueryWorkRegistrationApplicationFileListRequest req) {
        //构建file的查询条件
        WorkRegistrationApplicationFileJpaSpec fileJpaSpec = new WorkRegistrationApplicationFileJpaSpec();
        fileJpaSpec.setFileNameLike(req.getFileName());
        fileJpaSpec.setOriginalWorkHashEqOrWatermarkedWorkHashEq(req.getFileHash());
        fileJpaSpec.setFileSizeGtEq(req.getFileMinSize());
        fileJpaSpec.setFileSizeLtEq(req.getFileMaxSize());
        fileJpaSpec.setFileTypeEq(req.getFileType());
        fileJpaSpec.setDeletedEq(SqlConstant.UN_DELETED);
        Specification<WorkRegistrationApplicationFileDO> specification = QueryConvertUtils.toSpecification(fileJpaSpec);
        List<WorkRegistrationApplicationFileDO> fileDOS = workRegistrationApplicationFileService.findAll(specification);
        List<Integer> applicationIds = fileDOS.stream().map(WorkRegistrationApplicationFileDO::getApplicationId).collect(Collectors.toList());

        //构建application的查询条件
//        ListQueryWorkRegistrationApplicationJpaSpec jpaSpec = new ListQueryWorkRegistrationApplicationJpaSpec();
//        jpaSpec.setIdIn(ids);
//        Specification<WorkRegistrationApplicationDO> applicationDOSpecification = QueryConvertUtils.toSpecification(jpaSpec);
//        List<WorkRegistrationApplicationDO> fileDOS = workRegistrationApplicationService.findAll(jpaSpec);

        QueryWorkRegistrationJpaSpec jpaSpec = new QueryWorkRegistrationJpaSpec();
        jpaSpec.setApplicationIdIn(applicationIds);
        jpaSpec.setRegistrationNumberEq(req.getRegistrationNumber());
        jpaSpec.setRecycleEq(req.getRecycle());
        jpaSpec.setDeletedEq(0);
        Integer userId = TokenService.getLoginUser().getUserId();
        jpaSpec.setApplicantUserIdEq(userId);
        Specification<WorkRegistrationDO> applicationDOSpecification = QueryConvertUtils.toSpecification(jpaSpec);
        //查询用户名称
//        List<Integer> userIds = applications.stream().map(WorkRegistrationApplicationDO::getApplicantUserId).collect(Collectors.toList());
//        SysUserJpaSpec sysUserJpaSpec = new SysUserJpaSpec();
//        sysUserJpaSpec.setIdIn(userIds);
//        List<SysUserDO> userDOS = sysUserService.findAll(sysUserJpaSpec);

        //分页排序
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Page<WorkRegistrationDO> all = workRegistrationService.findAll(applicationDOSpecification, pageRequest);

        //封装结果
        PageResult<WorkRegistrationApplicationFileVo> pageResult = new PageResult<>(all);
        List<WorkRegistrationApplicationFileVo> fileVos = all.stream().map(data -> {
            WorkRegistrationApplicationFileVo fileVo = new WorkRegistrationApplicationFileVo();
            BeanUtils.copyProperties(data, fileVo);
            fileVo.setDeleteTime(data.getRecycleUpdateTime());
            for (WorkRegistrationApplicationFileDO fileDO : fileDOS) {
                if (Objects.equals(fileDO.getApplicationId(), data.getApplicationId())) {
                    fileVo.setOriginalWorkHash(fileDO.getOriginalWorkHash());
                    fileVo.setFileType(fileDO.getFileType());
                    fileVo.setWatermarkedWorkHash(fileDO.getWatermarkedWorkHash());
                    fileVo.setFileName(fileDO.getFileName());
                    fileVo.setFileSize(fileDO.getFileSize());
                }
            }
            return fileVo;
        }).collect(Collectors.toList());
        pageResult.setData(fileVos);
        return pageResult;
    }

    @Override
    public WorkRegistrationContentDetailVo getContentDetail(Integer id) {
        WorkRegistrationContentDetailVo workRegistrationContentDetailVo = new WorkRegistrationContentDetailVo();
        WorkRegistrationDO byId = workRegistrationService.findById(id);
        BeanUtils.copyProperties(byId, workRegistrationContentDetailVo);
        WorkRegistrationApplicationFileDO byApplicationId = workRegistrationApplicationFileService.findByApplicationId(byId.getApplicationId());
        BeanUtils.copyProperties(byApplicationId, workRegistrationContentDetailVo);
        WorkRegistrationApplicationDO application = workRegistrationApplicationService.findById(byId.getApplicationId());
        workRegistrationContentDetailVo.setApplicationTime(application.getCreateTime());
        workRegistrationContentDetailVo.setRegistrationTime(byId.getCreateTime());
        workRegistrationContentDetailVo.setApplicationReason(application.getApplicationReason());
        workRegistrationContentDetailVo.setStatus(application.getAuditStatus());
        // 补充审核流程信息
        List<WorkRegistrationRecordDO> records = workRegistrationRecordService.listByApplicationId(id);
        List<WorkRegistrationApplicationRecordVo> recordVos = records.stream().map(record -> {
            WorkRegistrationApplicationRecordVo recordVo = new WorkRegistrationApplicationRecordVo();
            recordVo.setOperateTime(record.getCreateTime());
            WorkRegistrationAuditStatusEnum status = WorkRegistrationAuditStatusEnum.getByCode(record.getAuditStatus());
            switch (status) {
                case PENDING:
                    // 待审核
                    recordVo.setOperateContent("提交申请");
                    break;
                case APPROVED:
                    // 审核通过
                    recordVo.setOperateContent("审核通过,审核意见:" + record.getAuditComment());
                    break;
                case REJECTED:
                    // 审核驳回
                    recordVo.setOperateContent("审核驳回,审核意见:" + record.getAuditComment());
                    break;
                default:
                    // 未知状态的处理
                    recordVo.setOperator(null);
                    recordVo.setOperateContent("未知操作");
                    break;
            }
            return recordVo;
        }).collect(Collectors.toList());
        workRegistrationContentDetailVo.setRecord(recordVos);
        return workRegistrationContentDetailVo;
    }

    @Override
    public  Result<Boolean> recycleContent(Integer id, Integer status) {
        WorkRegistrationDO workRegistrationDO = workRegistrationService.findById(id);
        if (Objects.isNull(workRegistrationDO)) {
            return Result.ofFailMsg("内容不存在");
        }
        workRegistrationDO.setRecycle(status);
        workRegistrationDO.setModifyTime(LocalDateTime.now());
        workRegistrationDO.setRecycleUpdateTime(LocalDateTime.now());
        workRegistrationService.update(workRegistrationDO);
        return Result.ofSuccess();
    }

    public List<UserRegistrationRetraceVo> predictNext30DaysWithTimeKeys(List<WorkRegistrationApplicationFileDO> files) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyDaysAgo = now.minusDays(30);

        int segmentDays = 5;
        int segmentCount = 6;

        List<Integer> segmentCounts = new ArrayList<>();
        for (int i = 0; i < segmentCount; i++) {
            LocalDateTime start = thirtyDaysAgo.plusDays(i * segmentDays);
            LocalDateTime end = start.plusDays(segmentDays);

            int count = 0;
            for (WorkRegistrationApplicationFileDO file : files) {
                LocalDateTime createTime = file.getCreateTime();
                if (!createTime.isBefore(start) && createTime.isBefore(end)) {
                    count++;
                }
            }
            segmentCounts.add(count);
        }

        // 预测未来 6 段数量
        List<Integer> predictions = predictNextSegmentsByMovingAverage(segmentCounts);

        // 构造时间段 + 数值的 Map
        List<UserRegistrationRetraceVo> predictionMap = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (int i = 0; i < predictions.size(); i++) {
            LocalDateTime start = now.plusDays((long) i * segmentDays);
            LocalDateTime end = start.plusDays(segmentDays);
            String timeKey = formatter.format(start) + " 至 " + formatter.format(end);
            UserRegistrationRetraceVo vo = new UserRegistrationRetraceVo();
            vo.setTime(timeKey);
            vo.setTotal(predictions.get(i));
            predictionMap.add(vo);
        }

        return predictionMap;
    }

    private List<Integer> predictNextSegmentsByMovingAverage(List<Integer> pastCounts) {
        List<Integer> predictions = new ArrayList<>();
        List<Integer> working = new ArrayList<>(pastCounts); // 复制原始数据
        int n = working.size();

        for (int i = 0; i < 6; i++) {
            int from = Math.max(0, n - 3);
            int to = n;
            int sum = 0;
            for (int j = from; j < to; j++) sum += working.get(j);
            int avg = (to - from) == 0 ? 0 : sum / (to - from);
            predictions.add(avg);
            working.add(avg);
            n++;
        }

        return predictions;
    }

    @Override
    public void getRegistrationTrendExcel(HttpServletResponse response, String type) {
//        List<UserRegistrationTrendVo> voList = getUserRegistrationTrend(type);
//        UniversalExportUtil.exportStream(voList, UserRegistrationTrendVo.class, "用户登记统计", "EXCEL", response);
    }

    @Override
    public void getGrowthRateExcel(HttpServletResponse response, String type) {
//        List<UserRegistrationGrowthVo> voList = getGrowthRate(type);
//        UniversalExportUtil.exportStream(voList, UserRegistrationGrowthVo.class, "用户登记增长率", "EXCEL", response);
    }

    @Override
    public void getRetraceExcel(HttpServletResponse response) {
        List<UserRegistrationRetraceVo> voList = getRetrace();
        UniversalExportUtil.exportStream(voList, UserRegistrationRetraceVo.class, "全景追溯", "EXCEL", response);
    }

}
