package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.enums.EmailTemplateEnum;
import com.zkjg.regtrace.common.enums.OperateTypeEnum;
import com.zkjg.regtrace.common.enums.PermissionTypeEnum;
import com.zkjg.regtrace.common.enums.UserTypeEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.manager.UserManager;
import com.zkjg.regtrace.persistence.assembler.PermissionAssembler;
import com.zkjg.regtrace.persistence.assembler.UserAssembler;
import com.zkjg.regtrace.persistence.entity.*;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserManageDetailVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserMenuListVo;
import com.zkjg.regtrace.service.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:34
 */
@Service
public class UserManagerImpl implements UserManager {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserInfoService sysUserInfoService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysPermissionService sysPermissionService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SysUserChangeHistoryService sysUserChangeHistoryService;
    @Resource
    private EmailService emailService;

    @Override
    public PageResult<UserListVo> selectUserList(UserListRequest req) {
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        //查询结果
        Specification<SysUserDO> specification = QueryConvertUtils.toSpecification(req);
        Page<SysUserDO> all = sysUserService.findAll(specification, pageRequest);

        //封装结果
        PageResult<UserListVo> pageResult = new PageResult<>(all);
        pageResult.setData(BeanUtil.copyToList(all.getContent(), UserListVo.class));
        return pageResult;
    }

    /**
     * 添加系统用户
     */
    @Override
    @Transactional
    public Integer addUser(UserAddRequest req) {
        SysUserDO userDO = PermissionAssembler.initInsertSystemUser(req, TokenService.getLoginUser().getUsername());
        SysUserDO save = sysUserService.saveUser(userDO);
        if (!Objects.isNull(req.getRoleList()) && !req.getRoleList().isEmpty()) {
            sysUserRoleService.saveUserPermission(save.getId(), req.getRoleList(), TokenService.getLoginUser().getUsername());
        }
        return save.getId();
    }

    @Override
    public UserManageDetailVo selectUserDetail(Integer userId) {
        SysUserDO sysUserDO = sysUserService.findUserById(userId).orElseThrow(() -> new BusinessException("该用户不存在"));
        SysUserInfoDO userInfoDO = null;
        if (sysUserDO.getUserType().equals(UserTypeEnum.GOVERNMENT_USER.getCode()) || sysUserDO.getUserType().equals(UserTypeEnum.ENTERPRISE_USER.getCode())) {
            userInfoDO = sysUserInfoService.findByUserId(sysUserDO.getId());
        }
        List<SysRoleDO> roleByUserId = sysRoleService.findByUserId(userId);
        return PermissionAssembler.initUserManageDetail(sysUserDO, userInfoDO, roleByUserId);

    }

    @Override
    @Transactional
    public void editUser(UserManageEditRequest req) {
        //修改信息
        LoginUser loginUser = TokenService.getLoginUser();
        SysUserDO userDO = sysUserService.findUserById(req.getUserId()).orElseThrow(() -> new BusinessException("该用户不存在"));
        if (userDO.getUsername().equals(CommonConstant.SUPPER_ADMIN_CODE)) {
            throw new BusinessException("该用户不可编辑");
        }
        SysUserInfoDO userInfoDO = sysUserInfoService.findByUserId(userDO.getId());
        PermissionAssembler.initUserManageEdit(req, userDO, userInfoDO, loginUser.getUsername());
        sysUserService.saveUser(userDO);
        if (Objects.nonNull(userInfoDO)) {
            sysUserInfoService.saveUserInfo(userInfoDO);
            userDO.setUserInfo(userInfoDO);
        }
        SysUserChangeHistoryDO historyDO = UserAssembler.initUserChangeHistory(req.getUserId(), loginUser.getUsername(),
                JSONObject.toJSONString(userDO),
                OperateTypeEnum.UPDATE);
        sysUserChangeHistoryService.saveUserHistory(historyDO);

        //修改权限
        sysUserRoleService.deleteUserPermission(req.getUserId());
        sysUserRoleService.flush();
        sysUserRoleService.saveUserPermission(req.getUserId(), req.getRoleList(), TokenService.getLoginUser().getUsername());
        if (!userDO.getUserType().equals(UserTypeEnum.SYSTEM_USER.getCode())) {
            Map<String, String> model = new HashMap<>();
            model.put("userName", userDO.getUsername());
            emailService.sendEmail(req.getEmail(), EmailTemplateEnum.CHANGE_USER_INFO, model);
        }
    }

    @Override
    @Transactional
    public void deleteUser(Integer userId) {
        SysUserDO userDO = sysUserService.findUserById(userId).orElseThrow(() -> new BusinessException("该用户不存在"));
        if (userDO.getUsername().equals(CommonConstant.SUPPER_ADMIN_CODE)) {
            throw new BusinessException("不允许编辑该角色");
        }
        if (!userDO.getUserType().equals(UserTypeEnum.SYSTEM_USER.getCode())) {
            throw new BusinessException("不允许删除平台用户");
        }
        sysUserService.deleteUser(userId);
        sysUserRoleService.deleteByUserId(userId);
        tokenService.delLoginUserByLoginName(userDO.getUsername());
    }

    @Override
    public List<RoleListVo> selectRoleList() {
        List<SysRoleDO> all = sysRoleService.findAll();
        return BeanUtil.copyToList(all, RoleListVo.class);
    }

    @Override
    public List<UserMenuListVo> selectMenu(Integer userId) {
        List<SysRoleDO> roles = sysRoleService.findByUserId(userId);

        //超级管理员处理
        List<String> collect = roles.stream().map(SysRoleDO::getRoleName).collect(Collectors.toList());
        if (collect.contains(CommonConstant.SUPPER_ADMIN_CODE)) {
            List<SysPermissionDO> allMenu = sysPermissionService.findAllMenu(Arrays.asList(PermissionTypeEnum.MODULE.getValue(), PermissionTypeEnum.MENU.getValue()));
            return BeanUtil.copyToList(allMenu, UserMenuListVo.class);
        }
        //普通用户
        List<SysPermissionDO> allMenu = sysPermissionService.findAllMenu(roles.stream().map(SysRoleDO::getId).collect(Collectors.toList()),
                Arrays.asList(PermissionTypeEnum.MODULE.getValue(), PermissionTypeEnum.MENU.getValue()));
        return BeanUtil.copyToList(allMenu, UserMenuListVo.class);
    }
}
