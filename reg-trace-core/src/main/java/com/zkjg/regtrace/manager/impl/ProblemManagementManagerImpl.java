package com.zkjg.regtrace.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import org.apache.commons.lang3.StringUtils;
import com.zkjg.regtrace.manager.ProblemManagementManager;
import com.zkjg.regtrace.persistence.entity.ProblemManagementDO;
import com.zkjg.regtrace.persistence.jpa.ProblemJpaSpec;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementAddRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementEditRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementQueryRequest;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemManagementVO;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemVo;
import com.zkjg.regtrace.service.ProblemManagementService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 问题管理Manager实现类
 *
 * <AUTHOR>
 */
@Service
public class ProblemManagementManagerImpl implements ProblemManagementManager {

    @Resource
    private ProblemManagementService problemManagementService;

    @Override
    public PageResult<ProblemManagementVO> queryProblemList(ProblemManagementQueryRequest request) {
        Specification<ProblemManagementDO> specification = buildSpecification(request);
        // 构建分页参数
        Sort sort = Sort.by("modifyTime");
        PageRequest pageRequest = PageRequest.of(
                request.getPage() - CommonConstant.ONE_INT,
                request.getSize(),
                sort
        );

        // 执行查询
        Page<ProblemManagementDO> page = problemManagementService.findAll(specification, pageRequest);

        // 转换为VO
        List<ProblemManagementVO> voList = BeanUtil.copyToList(page.getContent(), ProblemManagementVO.class);

        // 构建分页结果
        PageResult<ProblemManagementVO> result = new PageResult<>(page);
        result.setData(voList);

        return result;
    }

    /**
     * 构建查询条件，包含日期范围查询
     */
    private Specification<ProblemManagementDO> buildSpecification(ProblemManagementQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 基础查询条件
            if (StringUtils.isNotBlank(request.getProblemTypeEq())) {
                predicates.add(criteriaBuilder.equal(root.get("problemType"), request.getProblemTypeEq()));
            }

            if (StringUtils.isNotBlank(request.getProblemContentLike())) {
                predicates.add(criteriaBuilder.like(root.get("problemContent"), "%" + request.getProblemContentLike() + "%"));
            }

            if (StringUtils.isNotBlank(request.getProblemLabelLike())) {
                predicates.add(criteriaBuilder.like(root.get("problemLabel"), "%" + request.getProblemLabelLike() + "%"));
            }

            // 删除状态
            predicates.add(criteriaBuilder.equal(root.get("deleted"), StatusEnum.NO.getCode()));

            // 日期范围查询
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            if (StringUtils.isNotBlank(request.getModifyTimeStart())) {
                LocalDate startDate = LocalDate.parse(request.getModifyTimeStart(), dateFormatter);
                LocalDateTime startDateTime = startDate.atStartOfDay();
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("modifyTime"), startDateTime));
            }

            if (StringUtils.isNotBlank(request.getModifyTimeEnd())) {
                LocalDate endDate = LocalDate.parse(request.getModifyTimeEnd(), dateFormatter);
                LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("modifyTime"), endDateTime));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    @Override
    public Long addProblem(ProblemManagementAddRequest request) {
        // 构建实体对象
        ProblemManagementDO problemManagement = ProblemManagementDO.builder()
                .problemType(request.getProblemType())
                .problemContent(request.getProblemContent())
                .problemLabel(request.getProblemLabel())
                .answer(request.getAnswer())
                .deleted(StatusEnum.NO.getCode())
                .build();

        // 保存
        ProblemManagementDO saved = problemManagementService.save(problemManagement);
        return saved.getId();
    }

    @Override
    public ProblemManagementVO getProblemDetail(Long id) {
        Optional<ProblemManagementDO> optional = problemManagementService.findByIdAndDeleted(id, StatusEnum.NO.getCode());
        if (!optional.isPresent()) {
            throw new BusinessException("问题不存在或已被删除");
        }

        return BeanUtil.copyProperties(optional.get(), ProblemManagementVO.class);
    }

    @Override
    public Boolean editProblem(ProblemManagementEditRequest request) {
        Optional<ProblemManagementDO> optional = problemManagementService.findByIdAndDeleted(request.getId(), StatusEnum.NO.getCode());
        if (!optional.isPresent()) {
            throw new BusinessException("问题不存在或已被删除");
        }
        ProblemManagementDO problemManagement = optional.get();
        problemManagement.setProblemType(request.getProblemType());
        problemManagement.setProblemContent(request.getProblemContent());
        problemManagement.setProblemLabel(request.getProblemLabel());
        problemManagement.setAnswer(request.getAnswer());
        problemManagementService.save(problemManagement);
        return true;
    }

    @Override
    public Boolean deleteProblem(Long id) {
        Optional<ProblemManagementDO> optional = problemManagementService.findByIdAndDeleted(id, StatusEnum.NO.getCode());
        if (!optional.isPresent()) {
            throw new BusinessException("问题不存在或已被删除");
        }
        problemManagementService.deleteById(id);
        return true;
    }

    @Override
    public PageResult<ProblemVo> selectProblemList(HelpCenterQueryRequest req) {
        //参数
        Sort sort = Sort.by(Sort.Direction.fromString(req.getOrderType()), req.getOrderColumn());
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);

        ProblemJpaSpec problemJpaSpec = new ProblemJpaSpec();
        problemJpaSpec.setProblemTypeEq(req.getProblemType());
        problemJpaSpec.setProblemContentLikeOrProblemLabelLikeOrAnswerLike(req.getCondition());
        Specification<ProblemManagementDO> specification = QueryConvertUtils.toSpecification(problemJpaSpec);
        Page<ProblemManagementDO> all = problemManagementService.findAll(specification, pageRequest);

        //封装结果
        PageResult<ProblemVo> pageResult = new PageResult<>(all);
        pageResult.setData(BeanUtil.copyToList(all.getContent(), ProblemVo.class));
        return pageResult;
    }
}
