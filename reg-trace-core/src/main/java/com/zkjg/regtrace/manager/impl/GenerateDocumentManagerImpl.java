package com.zkjg.regtrace.manager.impl;
import com.lowagie.text.pdf.BaseFont;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.enums.WorkRegistrationTemplateEnum;
import com.zkjg.regtrace.manager.GenerateDocumentManager;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.Docx4J;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.springframework.stereotype.Service;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/13 10:23
 */
@Slf4j
@Service
public class GenerateDocumentManagerImpl implements GenerateDocumentManager {

    @Resource
    private Configuration freemarkerConfig;

    /**
     * 生成描述文件
     * @param data 源数据
     * @param fileName 文件名
     * @param templatePath 模板
     * @return 临时文件路径
     */
    @Override
    public String generateFile(Map<String,Object> data, String fileName, String templatePath) {
        try {
            // 1. 填充 FTL 模板
            String htmlContent = processTemplate(data,templatePath);

            // 2. 生成 PDF
            return generatePdfFromHtml(htmlContent,fileName);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String  processTemplate(Map<String,Object> data,String templatePath) throws Exception {
        Template template = freemarkerConfig.getTemplate(templatePath);
        StringWriter writer = new StringWriter();
        template.process(data, writer);
        return writer.toString();
    }


    private String generatePdfFromHtml(String html, String fileName) throws Exception {
        // 获取系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        // 创建完整的文件路径
        Path pdfPath = Paths.get(tempDir, fileName);
        try (OutputStream os = Files.newOutputStream(pdfPath)) {
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(html);
            String fontPath = this.getClass().getResource("/fonts/msyh.ttf").getFile();
            renderer.getFontResolver().addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            renderer.layout();
            renderer.createPDF(os);
            // 返回生成的PDF文件路径
            return pdfPath.toAbsolutePath().toString();
        }
    }


    private String generateWordFromHtml(String html, String fileName) throws Exception {
        // 获取系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");

        // 创建完整的文件路径
        File outputFile = new File(tempDir, fileName);

        // 1. 创建 Word 文档
        WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();

        // 2. 转换 HTML 到 Word
        XHTMLImporterImpl importer = new XHTMLImporterImpl(wordMLPackage);
        wordMLPackage.getMainDocumentPart().getContent().addAll(
                importer.convert(html, null)
        );
        // 3. 保存文件
        Docx4J.save(wordMLPackage, outputFile, Docx4J.FLAG_SAVE_ZIP_FILE);

        // 4. 返回文件路径
        return outputFile.getAbsolutePath();
    }

}
