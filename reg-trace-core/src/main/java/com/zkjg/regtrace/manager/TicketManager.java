package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.ticket.*;
import com.zkjg.regtrace.persistence.vo.response.ticket.*;

import java.util.List;

/**
 * 工单Manager
 * <AUTHOR>
 */
public interface TicketManager {

    /**
     * 创建工单
     * @param request 创建请求
     * @return 工单ID
     */
    Long createTicket(TicketCreateRequest request);

    /**
     * 查询工单列表
     * @param request 查询请求
     * @return 工单列表
     */
    PageResult<TicketListVO> queryTicketList(TicketQueryRequest request);

    /**
     * 查询工单详情
     * @param ticketId 工单ID
     * @return 工单详情
     */
    TicketDetailVO getTicketDetail(Long ticketId);

    /**
     * 客服确认接收工单
     * @param ticketId 工单ID
     */
    void confirmTicket(Long ticketId);

    /**
     * 客服拒绝工单
     * @param ticketId 工单ID
     * @param reason 拒绝原因
     */
    void rejectTicket(Long ticketId, String reason);

    /**
     * 客服回复工单
     * @param ticketId 工单ID
     * @param request 回复请求
     */
    void replyTicket(Long ticketId, TicketReplyRequest request);

    /**
     * 用户确认问题已解决
     * @param ticketId 工单ID
     */
    void resolveTicket(Long ticketId);

    /**
     * 用户追问
     * @param ticketId 工单ID
     * @param request 追问请求
     * @param operatorId 操作人ID
     */
    void followUpTicket(Long ticketId, TicketFollowUpRequest request, Integer operatorId);

    /**
     * 处理超时未确认的工单
     */
    void handleTimeoutTickets();

    /**
     * 处理用户超时未确认的工单（超过7天自动关闭）
     */
    void handleUserTimeoutTickets();

    /**
     * 查询用户的工单列表
     * @param request 查询请求
     * @return 工单列表
     */
    PageResult<TicketListVO> queryUserTickets(TicketQueryRequest request);

    /**
     * 查询客服的工单列表
     * @param csId 客服ID
     * @param request 查询请求
     * @return 工单列表
     */
    PageResult<TicketListVO> queryCustomerServiceTickets(Integer csId, TicketQueryRequest request);

    /**
     * 获取客服绩效统计（近一个月）
     * @return 客服绩效统计列表
     */
    List<CustomerServicePerformanceVO> getCustomerServicePerformance();

    /**
     * 获取问题分类统计
     * @return 分类统计列表
     */
    List<TicketCategoryStatisticsVO> getTicketCategoryStatistics();
}
