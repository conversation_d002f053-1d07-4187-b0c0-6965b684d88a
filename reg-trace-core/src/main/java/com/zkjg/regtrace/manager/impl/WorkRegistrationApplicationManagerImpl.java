package com.zkjg.regtrace.manager.impl;

import com.alibaba.fastjson2.JSON;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.enums.FileFormatEnum;
import com.zkjg.regtrace.common.config.MinioConfig;
import com.zkjg.regtrace.common.constants.MessageCategoryConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.EmailTemplateEnum;
import com.zkjg.regtrace.common.enums.FileTypeEnum;
import com.zkjg.regtrace.common.enums.MessageEnum;
import com.zkjg.regtrace.common.enums.MessageTypeEnum;
import com.zkjg.regtrace.common.enums.RetCodeEnum;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.enums.WorkRegistrationTemplateEnum;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.DateUtil;
import com.zkjg.regtrace.common.utils.FileUtil;
import com.zkjg.regtrace.common.utils.MinioUtils;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.manager.GenerateDocumentManager;
import com.zkjg.regtrace.manager.WorkRegistrationApplicationManager;
import com.zkjg.regtrace.persistence.assembler.WorkRegistrationRecordAssembler;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationApplicationJpaSpec;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.*;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationFileRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationAuditRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationAuditDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationRecordVo;
import com.zkjg.regtrace.service.*;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.core.utils.DesensitizationUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zkjg.regtrace.common.constants.PagingQuerySortConstant.SORT_CREATE_TIME;

/**
 * 作品登记申请管理类实现
 * <AUTHOR>
 * @Date 2025/6/11 19:07
 */
@Service
@Slf4j
public class WorkRegistrationApplicationManagerImpl implements WorkRegistrationApplicationManager {

    @Resource
    private WorkRegistrationApplicationFileService workRegistrationApplicationFileService;

    @Resource
    private WorkRegistrationRecordService workRegistrationRecordService;

    @Resource
    private WorkRegistrationApplicationService workRegistrationApplicationService;

    @Resource
    private GenerateDocumentManager generateDocumentManager;

    @Resource
    private MinioUtils minioUtils;

    @Resource
    private MinioConfig minioConfig;

    @Resource
    private EmailService emailService;

    @Resource
    private MessageNotifyService messageNotifyService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private ContentRegisterMetricsService contentRegisterMetricsService;

    @Resource
    private WorkRegistrationService workRegistrationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> apply(WorkRegistrationApplicationRequest req) {
        try {
            // 校验哈希值是否与数据库中现有数据重复
            Result<String> hashValidationResult = validateSingleRequestHashDuplicates(req);
            if (!hashValidationResult.isSuccess()) {
                return Result.ofFailMsg(hashValidationResult.getMsg());
            }

            // 校验作品名称是否重复
            Result<String> workNameValidationResult = validateSingleRequestWorkNameDuplicates(req);
            if (!workNameValidationResult.isSuccess()) {
                return Result.ofFailMsg(workNameValidationResult.getMsg());
            }

            // 保存申请主表信息
            WorkRegistrationApplicationDO application = workRegistrationApplicationService.saveFromApplicationRequest(req);

            // 保存到关联文件表
            saveApplicationFile(req, application.getId());

            // 保存到记录表
            saveApplicationRecord(application);

            return Result.ofSuccess(true);
        } catch (Exception e) {
            log.error("提交作品登记申请失败", e);
            return Result.ofFailMsg("提交申请失败: " + e.getMessage());
        }
    }

    /**
     * 保存作品登记文件信息
     *
     * @param req 申请请求
     * @param applicationId 申请ID
     */
    private void saveApplicationFile(WorkRegistrationApplicationRequest req, Integer applicationId) {
        WorkRegistrationApplicationFileDO file = WorkRegistrationApplicationFileDO.builder()
                .applicationId(applicationId)
                .fileName(req.getFileName())
                .fileSize(req.getFileSize())
                .fileType(req.getFileType())
                .fileFormat(req.getFileFormat())
                .originalWorkHash(req.getOriginalWorkHash())
                .watermarkedWorkHash(req.getWatermarkedWorkHash())
                .watermarkHash(req.getWatermarkHash())
                .metaData(req.getMetaData())
                .watermarkConfig(req.getWatermarkConfig())
                .qualityMetric(req.getQualityMetric())
                .deleted(SqlConstant.UN_DELETED)
                .build();
        workRegistrationApplicationFileService.save(file);
    }

    /**
     * 保存作品登记记录信息
     *
     * @param application 申请信息
     */
    private void saveApplicationRecord(WorkRegistrationApplicationDO application) {
        WorkRegistrationRecordDO record = WorkRegistrationRecordDO.builder()
                .applicationId(application.getId())
                .registrationNumber(application.getRegistrationNumber())
                .applicantUserId(application.getApplicantUserId())
                .auditStatus(application.getAuditStatus())
                .auditComment(application.getAuditComment())
                .workName(application.getWorkName())
                .deleted(SqlConstant.UN_DELETED)
                .createTime(application.getCreateTime())
                .build();
        workRegistrationRecordService.save(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> audit(WorkRegistrationApplicationAuditRequest req) {
        try {
            // 验证申请状态
            WorkRegistrationApplicationDO application = workRegistrationApplicationService.findById(req.getId());
            Result<Boolean> validationResult = validateApplicationForAudit(application);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 更新申请状态
            updateApplicationAuditStatus(application, req);

            // 保存审核记录
            saveAuditRecord(application);

            // 生成审核结果文件
            generateAndUploadAuditResultFile(application);

            // 发送通知
            sendAuditNotifications(application);
            if (req.getAuditStatus() == 2) {
                recordMetrics(req.getId(), application.getApplicantUserId());
            }
            return Result.ofSuccess(true);
        } catch (Exception e) {
            log.error("审核作品登记申请失败", e);
            return Result.ofFailMsg("审核申请失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> list(QueryWorkRegistrationApplicationRequest req) {
        Integer currentUserId = TokenService.getLoginUser().getUserId();
        req.setApplicantUserId(currentUserId);
        req.setDeleted(SqlConstant.UN_DELETED);
        return listAudit(req);
    }

    @Override
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> listAudit(QueryWorkRegistrationApplicationRequest req) {
        List<Integer> applicationIds = null;
        Map<Integer, WorkRegistrationApplicationFileDO> workRegistrationApplicationFileMap;
        if(Objects.nonNull(req.getFileType())){
            QueryWorkRegistrationApplicationFileJpaSpec queryWorkRegistrationApplicationFileJpaSpec = QueryWorkRegistrationApplicationFileJpaSpec.builder()
                    .fileTypeEq(req.getFileType())
                    .build();
            Specification<WorkRegistrationApplicationFileDO> specification = QueryConvertUtils.toSpecification(queryWorkRegistrationApplicationFileJpaSpec);
            List<WorkRegistrationApplicationFileDO> workRegistrationApplicationFileDOS =  workRegistrationApplicationFileService.findAll(specification);;
            applicationIds = workRegistrationApplicationFileDOS.stream().map(WorkRegistrationApplicationFileDO::getApplicationId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(applicationIds)){
                return Result.ofSuccess(new PageResult<>());
            }
            workRegistrationApplicationFileMap = workRegistrationApplicationFileDOS.stream().collect(Collectors.toMap(WorkRegistrationApplicationFileDO::getApplicationId, data -> data));
        } else {
            workRegistrationApplicationFileMap = new HashMap<>();
        }
        QueryWorkRegistrationApplicationJpaSpec spec = QueryWorkRegistrationApplicationJpaSpec.builder()
                .page(req.getPage())
                .size(req.getSize())
                .auditStatusEq(req.getAuditStatus())
                .applicantUserIdEq(req.getApplicantUserId())
                .createTimeGtEq(DateUtil.strToLocalDateTime(req.getStartCreateTime()))
                .createTimeLtEq(DateUtil.strToLocalDateTime(req.getEndCreateTime()))
                .auditTimeGtEq(DateUtil.strToLocalDateTime(req.getStartAuditTime()))
                .auditTimeLtEq(DateUtil.strToLocalDateTime(req.getEndAuditTime()))
                .deletedEq(req.getDeleted())
                .applicantNameLike(req.getApplicantName())
                .workNameLike(req.getWorkName())
                .registrationNumberLike(req.getRegistrationNumber())
                .idIn(applicationIds)
                .build();
        Specification<WorkRegistrationApplicationDO> specification = QueryConvertUtils.toSpecification(spec);
        Sort sort = Sort.by(Sort.Direction.DESC, SORT_CREATE_TIME);
        Pageable pageable = PageRequest.of(req.getPage() - CommonConstant.ONE_INT, req.getSize(), sort);
        Page<WorkRegistrationApplicationDO> page = workRegistrationApplicationService.findAll(specification, pageable);
        List<WorkRegistrationApplicationDetailVo> records = page.getContent().stream().map(data -> {
            WorkRegistrationApplicationDetailVo workRegistrationApplicationDetailVo = new WorkRegistrationApplicationDetailVo();
            BeanUtils.copyProperties(data, workRegistrationApplicationDetailVo);
            if(workRegistrationApplicationFileMap.isEmpty()){
                workRegistrationApplicationDetailVo.setFile(workRegistrationApplicationFileService.findByApplicationId(data.getId()));
            }else{
                workRegistrationApplicationDetailVo.setFile(workRegistrationApplicationFileMap.get(data.getId()));
            }
            return workRegistrationApplicationDetailVo;
        }).collect(Collectors.toList());
        PageResult<WorkRegistrationApplicationDetailVo> pageResult = new PageResult<>(page);
        pageResult.setData(records);
        return Result.ofSuccess(pageResult);
    }

    @Override
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> trashList(QueryWorkRegistrationApplicationRequest req) {
        Integer currentUserId = TokenService.getLoginUser().getUserId();
        req.setApplicantUserId(currentUserId);
        req.setDeleted(SqlConstant.DELETED);
        return listAudit(req);
    }

    private void recordMetrics(Integer id, Integer applicantUserId) {
        WorkRegistrationApplicationFileDO fileDO = workRegistrationApplicationFileService.findByApplicationId(id);
        ContentRegisterMetricsDO metricsDO = ContentRegisterMetricsDO.builder()
                .applicationId(id)
                .fileType(fileDO.getFileType())
                .applicantUserId(applicantUserId)
                .createTime(LocalDateTime.now())
                .build();
        contentRegisterMetricsService.save(metricsDO);
    }

    /**
     * 验证申请是否可以进行审核
     *
     * @param application 申请信息
     * @return 验证结果
     */
    private Result<Boolean> validateApplicationForAudit(WorkRegistrationApplicationDO application) {
        if (application == null) {
            return Result.ofFail(RetCodeEnum.WORK_REGISTRATION_APPLICATION_NOT_EXISTS);
        }
        if (!Objects.equals(application.getAuditStatus(), WorkRegistrationAuditStatusEnum.PENDING.getCode())) {
            return Result.ofFail(RetCodeEnum.WORK_REGISTRATION_APPLICATION_STATUS_NOT_PENDING);
        }
        return Result.ofSuccess(true);
    }

    /**
     * 更新申请的审核状态
     *
     * @param application 申请信息
     * @param req 审核请求
     */
    private void updateApplicationAuditStatus(WorkRegistrationApplicationDO application, WorkRegistrationApplicationAuditRequest req) {
        application.setAuditComment(req.getAuditComment());
        application.setAuditStatus(req.getAuditStatus());
        Integer currentUserId = TokenService.getLoginUser().getUserId();
        application.setReviewedUserId(currentUserId);
        application.setModifyTime(LocalDateTime.now());
        application.setAuditTime(LocalDateTime.now());
        workRegistrationApplicationService.update(application);
        //是否入主表
        if(Objects.equals(req.getAuditStatus(),WorkRegistrationAuditStatusEnum.APPROVED.getCode())){
            WorkRegistrationApplicationFileDO workRegistrationApplicationFileDO = workRegistrationApplicationFileService.findByApplicationId(application.getId());
            // 审核通过  入主表
            WorkRegistrationDO workRegistrationDO = new WorkRegistrationDO();
            BeanUtils.copyProperties(application,workRegistrationDO,"id","createTime","modifyTime");
            workRegistrationDO.setCreateTime(LocalDateTime.now());
            workRegistrationDO.setApplicationId(application.getId());
            workRegistrationDO.setDeleted(SqlConstant.UN_DELETED);
            workRegistrationDO.setRecycle(SqlConstant.UN_DELETED);
            workRegistrationDO.setFileType(workRegistrationApplicationFileDO.getFileType());
            workRegistrationService.save(workRegistrationDO);
        }
    }

    /**
     * 保存审核记录
     *
     * @param application 申请信息
     */
    private void saveAuditRecord(WorkRegistrationApplicationDO application) {
        WorkRegistrationRecordDO record = WorkRegistrationRecordAssembler.convertApplicationToDO(application);
        workRegistrationRecordService.save(record);
    }

    /**
     * 生成并上传审核结果文件
     *
     * @param application 申请信息
     */
    private void generateAndUploadAuditResultFile(WorkRegistrationApplicationDO application) {
        try {
            Map<String, Object> data = buildDocumentData(application);
            String fileName = application.getRegistrationNumber() + ".pdf";
            String filePath = generateDocumentManager.generateFile(
                    data,
                    fileName,
                    WorkRegistrationTemplateEnum.WORK_REGISTRATION_AUDIT.getTemplate()
            );

            if (StringUtils.isNotEmpty(filePath)) {
                File tempFile = new File(filePath);
                try {
                    // 上传文件到Minio
                    MultipartFile multipartFile = FileUtil.convertFileToMultipartFile(tempFile);
                    String path = minioUtils.uploadFile(multipartFile);
                    path = path.replace(minioConfig.getReadUrl(),"");
                    application.setRegistrationResultFileUrl(path);
                    workRegistrationApplicationService.update(application);
                } finally {
                    // 确保临时文件被删除
                    if (tempFile.exists()) {
                        FileUtils.delete(tempFile);
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成审核结果文件失败", e);
        }
    }

    /**
     * 发送审核通知（邮件和站内信）
     *
     * @param application 申请信息
     */
    private void sendAuditNotifications(WorkRegistrationApplicationDO application) {
        // 发送站内信
        sendInternalMessage(application);
        // 发送邮件通知
        sendAuditEmail(application);
    }

    /**
     * 发送审核结果邮件
     *
     * @param application 申请信息
     */
    private void sendAuditEmail(WorkRegistrationApplicationDO application) {
        SysUserDO sysUserDO = sysUserService.findUserById(application.getApplicantUserId()).get();
        Map<String, String> model = new HashMap<>();
        model.put("userName",sysUserDO.getUsername());
        model.put("name", application.getWorkName());
        model.put("auditStatus", WorkRegistrationAuditStatusEnum.getByCode(application.getAuditStatus()).getDesc());
        if (StringUtils.isNotEmpty(application.getRegistrationResultFileUrl())) {
            model.put("registrationResultFileUrl", minioConfig.getReadUrl()+application.getRegistrationResultFileUrl());
        }else{
            model.put("registrationResultFileUrl", "");
        }
        boolean sendSuccess = true;
        try {
            emailService.sendEmail(application.getApplicantEmail(), EmailTemplateEnum.WORK_REGISTRATION_AUDIT, model);
        } catch (Exception e) {
            // 发送失败 也需要记录
            sendSuccess = false;
        }
        boolean isApproved = Objects.equals(application.getAuditStatus(), WorkRegistrationAuditStatusEnum.APPROVED.getCode());
        MessageEnum messageEnum = isApproved ? MessageEnum.WORK_REGISTRATION_PASS : MessageEnum.WORK_REGISTRATION_REJECT;
        String content = String.format(messageEnum.getContent(), application.getRegistrationNumber(), application.getWorkName());
        messageNotifyService.sendMessageNotify(
                application.getApplicantUserId(),
                application.getReviewedUserId(),
                content,
                messageEnum.getSummary(),
                MessageCategoryConstant.WORK_REGISTRATION_MESSAGE,
                MessageTypeEnum.EMAIL.getType(),
                sendSuccess ? 0 : 1,
                messageEnum.getLevel()
        );
    }

    /**
     * 发送站内信通知
     *
     * @param application 申请信息
     */
    private void sendInternalMessage(WorkRegistrationApplicationDO application) {
        boolean isApproved = Objects.equals(application.getAuditStatus(), WorkRegistrationAuditStatusEnum.APPROVED.getCode());

        MessageEnum messageEnum = isApproved ? MessageEnum.WORK_REGISTRATION_PASS : MessageEnum.WORK_REGISTRATION_REJECT;
        String content = String.format(messageEnum.getContent(), application.getWorkName(), application.getAuditComment());

        messageNotifyService.sendMessageNotify(
                application.getApplicantUserId(),
                application.getReviewedUserId(),
                content,
                messageEnum.getSummary(),
                MessageCategoryConstant.WORK_REGISTRATION_MESSAGE,
                MessageTypeEnum.INTERNAL.getType(),
                0,
                messageEnum.getLevel()
        );
    }

    /**
     * 构建文档数据
     *
     * @param application 申请信息
     * @return 文档数据
     */
    private Map<String, Object> buildDocumentData(WorkRegistrationApplicationDO application) {
        Map<String, Object> data = new HashMap<>();
        data.put("auditStatus", WorkRegistrationAuditStatusEnum.getByCode(application.getAuditStatus()).getDesc());
        data.put("registrationNumber", application.getRegistrationNumber());
        data.put("createTime", DateUtil.format(application.getCreateTime()));
        data.put("auditComment", StringUtils.defaultString(application.getAuditComment(), ""));
        return data;
    }

    @Override
    public Result<WorkRegistrationApplicationDetailVo> detail(Integer id) {
        WorkRegistrationApplicationDetailVo result = new WorkRegistrationApplicationDetailVo();
        WorkRegistrationApplicationDO workRegistrationApplicationDO = workRegistrationApplicationService.findById(id);
        if (workRegistrationApplicationDO == null) {
            return Result.ofSuccess(null);
        }
        // 转换为VO对象
        BeanUtils.copyProperties(workRegistrationApplicationDO, result);

        // 获取关联的文件信息
        WorkRegistrationApplicationFileDO fileInfo = workRegistrationApplicationFileService.findByApplicationId(id);
        if (fileInfo != null) {
            result.setFile(fileInfo);
        }
        List<WorkRegistrationApplicationRecordVo> records = buildAuditRecords(result.getApplicantUserId(), result.getReviewedUserId(), id);
        result.setRecord(records);
        return Result.ofSuccess(result);
    }

    @Override
    public WorkRegistrationApplicationDetailVo queryWorkFileDetail(QueryWorkRegistrationApplicationFileRequest req) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> importWorkRegistrationApplication(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.ofFailMsg("请选择要导入的CSV文件");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename) || !originalFilename.toLowerCase().endsWith(".csv")) {
            return Result.ofFailMsg("请上传CSV格式的文件");
        }

        try {
            // 读取CSV文件内容
            String csvContent = new String(file.getBytes(), StandardCharsets.UTF_8);

            // 解析CSV为对象列表
            List<WorkRegistrationApplicationRequest> requestList;

            try {
                requestList = parseCsvFile(csvContent);
            } catch (Exception e) {
                log.error("CSV格式解析失败", e);
                return Result.ofFailMsg("CSV文件格式错误，请检查文件内容：" + e.getMessage());
            }

            if (requestList == null || requestList.isEmpty()) {
                return Result.ofFailMsg("CSV文件中没有有效的数据");
            }

            // 验证数据并执行导入
            return processBatchImport(requestList);

        } catch (Exception e) {
            log.error("批量导入失败", e);
            return Result.ofFailMsg("导入失败：" + e.getMessage());
        }
    }

    /**
     * 处理批量导入逻辑
     *
     * @param requestList 导入数据列表
     * @return 导入结果
     */
    private Result<String> processBatchImport(List<WorkRegistrationApplicationRequest> requestList) {
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        // 第一步：检查JSON文件内部哈希重复
        Result<String> duplicateCheckResult = validateInternalHashDuplicates(requestList);
        if (!duplicateCheckResult.isSuccess()) {
            return duplicateCheckResult;
        }

        // 存储验证通过的数据
        List<WorkRegistrationApplicationRequest> validRequestList = new ArrayList<>();

        // 第二步：检查与数据库中现有数据的哈希重复
        Result<String> dbDuplicateCheckResult = validateDatabaseHashDuplicates(requestList);
        if (!dbDuplicateCheckResult.isSuccess()) {
            return dbDuplicateCheckResult;
        }

        // 第三步：检查JSON文件内部的作品名称重复
        Result<String> internalWorkNameCheckResult = validateInternalWorkNameDuplicates(requestList);
        if (!internalWorkNameCheckResult.isSuccess()) {
            return internalWorkNameCheckResult;
        }

        // 第四步：检查与数据库中现有数据的作品名称重复
        Result<String> dbWorkNameCheckResult = validateDatabaseWorkNameDuplicates(requestList);
        if (!dbWorkNameCheckResult.isSuccess()) {
            return dbWorkNameCheckResult;
        }

        // 第五步：逐行验证数据
        for (int i = 0; i < requestList.size(); i++) {
            WorkRegistrationApplicationRequest request = requestList.get(i);
            int lineNumber = i + 1;

            try {
                // 验证数据
                Result<String> validationResult = validateImportRequest(request, lineNumber);
                if (!validationResult.isSuccess()) {
                    failCount++;
                    errorMessages.append(validationResult.getMsg()).append("\n");
                    continue;
                }

                // 确保是非草稿模式
                request.setDraft(false);
                validRequestList.add(request);

            } catch (Exception e) {
                failCount++;
                errorMessages.append("第").append(lineNumber).append("行数据验证异常: ")
                           .append(e.getMessage()).append("\n");
                log.error("验证第{}行数据时发生异常", lineNumber, e);
            }
        }

        // 第六步：批量保存验证通过的数据
        if (!validRequestList.isEmpty()) {
            try {
                successCount = batchSaveApplications(validRequestList);
                log.info("批量导入成功，共导入{}条数据", successCount);
            } catch (Exception e) {
                log.error("批量保存数据失败", e);
                failCount += validRequestList.size();
                errorMessages.append("批量保存失败: ").append(e.getMessage()).append("\n");
                successCount = 0;
            }
        }

        // 构建返回结果
        String resultMessage = String.format("导入完成！成功：%d条，失败：%d条", successCount, failCount);

        if (failCount > 0) {
            resultMessage += "\n\n失败详情：\n" + errorMessages;
            // 如果有失败的
            return Result.ofFailMsg(resultMessage);
        }

        // 全部成功
        return Result.ofSuccessMsg(resultMessage);
    }

    /**
     * 批量保存申请数据
     *
     * @param requestList 验证通过的申请数据列表
     * @return 成功保存的条数
     */
    private int batchSaveApplications(List<WorkRegistrationApplicationRequest> requestList) {
        // 准备批量插入的数据
        List<WorkRegistrationApplicationDO> applicationList = new ArrayList<>();
        List<WorkRegistrationApplicationFileDO> fileList = new ArrayList<>();
        List<WorkRegistrationRecordDO> recordList = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();

        for (WorkRegistrationApplicationRequest request : requestList) {
            // 构建申请主表数据
            WorkRegistrationApplicationDO application = buildApplicationDO(request, now);
            applicationList.add(application);
        }

        // 批量保存申请主表
        List<WorkRegistrationApplicationDO> savedApplications = workRegistrationApplicationService.batchSave(applicationList);

        // 构建文件表和记录表数据
        for (int i = 0; i < requestList.size(); i++) {
            WorkRegistrationApplicationRequest request = requestList.get(i);
            WorkRegistrationApplicationDO application = savedApplications.get(i);

            // 构建文件表数据
            WorkRegistrationApplicationFileDO file = buildApplicationFileDO(request, application.getId());
            fileList.add(file);

            // 构建记录表数据
            WorkRegistrationRecordDO record = buildApplicationRecordDO(application);
            recordList.add(record);
        }

        // 批量保存文件表
        workRegistrationApplicationFileService.batchSave(fileList);

        // 批量保存记录表
        workRegistrationRecordService.batchSave(recordList);

        return savedApplications.size();
    }

    /**
     * 构建申请主表DO对象
     *
     * @param request 申请请求
     * @param createTime 创建时间
     * @return 申请主表DO
     */
    private WorkRegistrationApplicationDO buildApplicationDO(WorkRegistrationApplicationRequest request, LocalDateTime createTime) {
        Integer applicantUserId = TokenService.getLoginUser().getUserId();
        SysUserDO applicantUser = sysUserService.findUserById(applicantUserId).orElse(null);
        return WorkRegistrationApplicationDO.builder()
                .workName(request.getWorkName())
                .applicationReason(request.getApplicationReason())
                .registrationNumber(request.getRegistrationNumber())
                .auditStatus(WorkRegistrationAuditStatusEnum.PENDING.getCode())
                .deleted(SqlConstant.UN_DELETED)
                .createTime(createTime)
                .modifyTime(createTime)
                .applicantUserId(applicantUserId)
                .applicantEmail(applicantUser.getEmail())
                .applicantName(applicantUser.getUsername())
                .build();
    }

    /**
     * 构建文件表DO对象
     *
     * @param request 申请请求
     * @param applicationId 申请ID
     * @return 文件表DO
     */
    private WorkRegistrationApplicationFileDO buildApplicationFileDO(WorkRegistrationApplicationRequest request, Integer applicationId) {
        return WorkRegistrationApplicationFileDO.builder()
                .applicationId(applicationId)
                .fileName(request.getFileName())
                .hiddenWaterMarkHash(request.getHiddenWaterMarkHash())
                .fileSize(request.getFileSize())
                .fileType(request.getFileType())
                .fileFormat(request.getFileFormat())
                .originalWorkHash(request.getOriginalWorkHash())
                .watermarkedWorkHash(request.getWatermarkedWorkHash())
                .watermarkHash(request.getWatermarkHash())
                .metaData(request.getMetaData())
                .deleted(SqlConstant.UN_DELETED)
                .build();
    }

    /**
     * 构建记录表DO对象
     *
     * @param application 申请信息
     * @return 记录表DO
     */
    private WorkRegistrationRecordDO buildApplicationRecordDO(WorkRegistrationApplicationDO application) {
        return WorkRegistrationRecordDO.builder()
                .applicationId(application.getId())
                .registrationNumber(application.getRegistrationNumber())
                .applicantUserId(application.getApplicantUserId())
                .auditStatus(application.getAuditStatus())
                .auditComment(application.getAuditComment())
                .workName(application.getWorkName())
                .deleted(SqlConstant.UN_DELETED)
                .createTime(application.getCreateTime())
                .build();
    }

    /**
     * 验证导入的申请请求数据
     *
     * @param request 申请请求
     * @param lineNumber 行号
     * @return 验证结果
     */
    private Result<String> validateImportRequest(WorkRegistrationApplicationRequest request, int lineNumber) {
        StringBuilder errors = new StringBuilder();

        // 验证必填字段
        if (StringUtils.isBlank(request.getWorkName())) {
            errors.append("作品名称不能为空；");
        }
        if (StringUtils.isBlank(request.getRegistrationNumber())) {
            errors.append("登记编号不能为空；");
        }
        if (StringUtils.isBlank(request.getApplicationReason())) {
            errors.append("申请原因不能为空；");
        }
        if (StringUtils.isBlank(request.getFileName())) {
            errors.append("文件名称不能为空；");
        }
        if (request.getFileSize() == null || request.getFileSize() <= 0) {
            errors.append("文件大小必须大于0；");
        }
        if (request.getFileType() == null) {
            errors.append("文件类型不能为空；");
        } else if (request.getFileType() != 0 && !Objects.equals(request.getFileType(), FileTypeEnum.VIDEO.getCode())) {
            errors.append("文件类型必须是0（音频）或1（视频）；");
        }
        if (StringUtils.isBlank(request.getFileFormat())) {
            errors.append("文件格式不能为空；");
        }
        if (StringUtils.isBlank(request.getOriginalWorkHash())) {
            errors.append("原始文件哈希不能为空；");
        }
        if (StringUtils.isBlank(request.getWatermarkedWorkHash())) {
            errors.append("带水印文件哈希不能为空；");
        }
        if (StringUtils.isBlank(request.getWatermarkHash())) {
            errors.append("水印哈希不能为空；");
        }
        // 验证文件格式是否合法
        if (StringUtils.isNotBlank(request.getFileFormat())) {
            if (!FileFormatEnum.isValidFormat(request.getFileFormat())) {
                errors.append("文件格式不支持，支持的格式：").append(String.join(", ", FileFormatEnum.getAllFormats())).append("；");
            } else {
                // 验证文件格式与文件类型是否匹配
                if (request.getFileType() != null && !FileFormatEnum.isFormatMatchFileType(request.getFileFormat(), request.getFileType())) {
                    errors.append("文件格式与文件类型不匹配，音频类型(0)支持：").append(String.join(", ", FileFormatEnum.getFormatsByFileType(0))).append("，视频类型(1)支持：").append(String.join(", ", FileFormatEnum.getFormatsByFileType(1))).append("；");
                }
            }
        }
        if (errors.length() > 0) {
            return Result.ofFailMsg(String.format("第%d行: %s", lineNumber, errors));
        }

        return Result.ofSuccess("验证通过");
    }

    /**
     * 验证JSON文件内部的哈希重复问题
     *
     * @param requestList 导入数据列表
     * @return 验证结果
     */
    private Result<String> validateInternalHashDuplicates(List<WorkRegistrationApplicationRequest> requestList) {
        // 用于存储已出现的哈希值和对应的行号
        Map<String, Integer> originalHashMap = new HashMap<>();
        Map<String, Integer> watermarkedHashMap = new HashMap<>();
        List<String> duplicateErrors = new ArrayList<>();

        for (int i = 0; i < requestList.size(); i++) {
            WorkRegistrationApplicationRequest request = requestList.get(i);
            int lineNumber = i + 1;

            String originalHash = request.getOriginalWorkHash();
            String watermarkedHash = request.getWatermarkedWorkHash();

            // 检查原始文件哈希重复
            if (StringUtils.isNotBlank(originalHash)) {
                if (originalHashMap.containsKey(originalHash)) {
                    duplicateErrors.add(String.format("第%d行的原始文件哈希值与第%d行重复: %s",
                            lineNumber, originalHashMap.get(originalHash), originalHash));
                } else {
                    originalHashMap.put(originalHash, lineNumber);
                }
            }

            // 检查带水印文件哈希重复
            if (StringUtils.isNotBlank(watermarkedHash)) {
                if (watermarkedHashMap.containsKey(watermarkedHash)) {
                    duplicateErrors.add(String.format("第%d行的带水印文件哈希值与第%d行重复: %s",
                            lineNumber, watermarkedHashMap.get(watermarkedHash), watermarkedHash));
                } else {
                    watermarkedHashMap.put(watermarkedHash, lineNumber);
                }
            }
        }

        // 如果发现重复，返回错误信息
        if (!duplicateErrors.isEmpty()) {
            String errorMessage = "JSON文件内部存在哈希重复问题：\n" + String.join("\n", duplicateErrors);
            return Result.ofFailMsg(errorMessage);
        }

        return Result.ofSuccess("内部哈希校验通过");
    }

    /**
     * 验证与数据库中现有数据的哈希重复问题
     *
     * @param requestList 导入数据列表
     * @return 验证结果
     */
    private Result<String> validateDatabaseHashDuplicates(List<WorkRegistrationApplicationRequest> requestList) {
        // 提取所有原始文件哈希和带水印文件哈希
        List<String> originalHashes = requestList.stream()
                .map(WorkRegistrationApplicationRequest::getOriginalWorkHash)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        List<String> watermarkedHashes = requestList.stream()
                .map(WorkRegistrationApplicationRequest::getWatermarkedWorkHash)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        List<String> duplicateErrors = new ArrayList<>();

        // 检查原始文件哈希在数据库中是否存在
        if (!originalHashes.isEmpty()) {
            List<WorkRegistrationApplicationFileDO> existingOriginalFiles =
                    workRegistrationApplicationFileService.findByOriginalWorkHashIn(originalHashes);

            if (!existingOriginalFiles.isEmpty()) {
                Map<String, List<Integer>> hashToLineMap = buildHashToLineMap(requestList, true);
                for (WorkRegistrationApplicationFileDO file : existingOriginalFiles) {
                    String hash = file.getOriginalWorkHash();
                    List<Integer> lineNumbers = hashToLineMap.get(hash);
                    if (lineNumbers != null) {
                        for (Integer lineNumber : lineNumbers) {
                            duplicateErrors.add(String.format("第%d行的原始文件哈希值在数据库中已存在: %s（文件名: %s）",
                                    lineNumber, hash, file.getFileName()));
                        }
                    }
                }
            }
        }

        // 检查带水印文件哈希在数据库中是否存在
        if (!watermarkedHashes.isEmpty()) {
            List<WorkRegistrationApplicationFileDO> existingWatermarkedFiles =
                    workRegistrationApplicationFileService.findByWatermarkedWorkHashIn(watermarkedHashes);

            if (!existingWatermarkedFiles.isEmpty()) {
                Map<String, List<Integer>> hashToLineMap = buildHashToLineMap(requestList, false);
                for (WorkRegistrationApplicationFileDO file : existingWatermarkedFiles) {
                    String hash = file.getWatermarkedWorkHash();
                    List<Integer> lineNumbers = hashToLineMap.get(hash);
                    if (lineNumbers != null) {
                        for (Integer lineNumber : lineNumbers) {
                            duplicateErrors.add(String.format("第%d行的带水印文件哈希值在数据库中已存在: %s（文件名: %s）",
                                    lineNumber, hash, file.getFileName()));
                        }
                    }
                }
            }
        }

        // 如果发现重复，返回错误信息
        if (!duplicateErrors.isEmpty()) {
            String errorMessage = "检测到与数据库中现有数据的哈希重复问题：\n" + String.join("\n", duplicateErrors);
            return Result.ofFailMsg(errorMessage);
        }

        return Result.ofSuccess("数据库哈希校验通过");
    }

    /**
     * 构建哈希值到行号的映射
     *
     * @param requestList 导入数据列表
     * @param isOriginalHash 是否是原始文件哈希（true：原始文件哈希，false：带水印文件哈希）
     * @return 哈希值到行号列表的映射
     */
    private Map<String, List<Integer>> buildHashToLineMap(List<WorkRegistrationApplicationRequest> requestList, boolean isOriginalHash) {
        Map<String, List<Integer>> hashToLineMap = new HashMap<>();

        for (int i = 0; i < requestList.size(); i++) {
            WorkRegistrationApplicationRequest request = requestList.get(i);
            int lineNumber = i + 1;

            String hash = isOriginalHash ? request.getOriginalWorkHash() : request.getWatermarkedWorkHash();

            if (StringUtils.isNotBlank(hash)) {
                hashToLineMap.computeIfAbsent(hash, k -> new ArrayList<>()).add(lineNumber);
            }
        }

        return hashToLineMap;
    }

    /**
     * 验证单个申请请求与数据库中现有数据的哈希重复问题
     *
     * @param request 申请请求
     * @return 验证结果
     */
    private Result<String> validateSingleRequestHashDuplicates(WorkRegistrationApplicationRequest request) {
        List<String> duplicateErrors = new ArrayList<>();

        // 检查原始文件哈希在数据库中是否存在
        if (StringUtils.isNotBlank(request.getOriginalWorkHash())) {
            List<String> originalHashList = new ArrayList<>();
            originalHashList.add(request.getOriginalWorkHash());
            List<WorkRegistrationApplicationFileDO> existingOriginalFiles =
                    workRegistrationApplicationFileService.findByOriginalWorkHashIn(originalHashList);

            if (!existingOriginalFiles.isEmpty()) {
                WorkRegistrationApplicationFileDO existingFile = existingOriginalFiles.get(0);
                duplicateErrors.add(String.format("原始文件哈希值在数据库中已存在: %s（已存在的文件名: %s）",
                        request.getOriginalWorkHash(), existingFile.getFileName()));
            }
        }

        // 检查带水印文件哈希在数据库中是否存在
        if (StringUtils.isNotBlank(request.getWatermarkedWorkHash())) {
            List<String> watermarkedHashList = new ArrayList<>();
            watermarkedHashList.add(request.getWatermarkedWorkHash());
            List<WorkRegistrationApplicationFileDO> existingWatermarkedFiles =
                    workRegistrationApplicationFileService.findByWatermarkedWorkHashIn(watermarkedHashList);

            if (!existingWatermarkedFiles.isEmpty()) {
                WorkRegistrationApplicationFileDO existingFile = existingWatermarkedFiles.get(0);
                duplicateErrors.add(String.format("带水印文件哈希值在数据库中已存在: %s（已存在的文件名: %s）",
                        request.getWatermarkedWorkHash(), existingFile.getFileName()));
            }
        }

        // 如果发现重复，返回错误信息
        if (!duplicateErrors.isEmpty()) {
            String errorMessage = "检测到与数据库中现有数据的哈希重复问题：\n" + String.join("\n", duplicateErrors);
            return Result.ofFailMsg(errorMessage);
        }

        return Result.ofSuccess("哈希校验通过");
    }

    /**
     * 验证单个申请请求的作品名称重复问题
     *
     * @param request 申请请求
     * @return 验证结果
     */
    private Result<String> validateSingleRequestWorkNameDuplicates(WorkRegistrationApplicationRequest request) {
        if (StringUtils.isBlank(request.getWorkName())) {
            return Result.ofSuccess("作品名称为空，跳过校验");
        }

        String workName = request.getWorkName();

        // 1. 检查申请表中是否存在相同作品名称
        List<String> workNames = new ArrayList<>();
        workNames.add(workName);
        List<WorkRegistrationApplicationDO> existingApplications = workRegistrationApplicationService.findByWorkNames(workNames);

        if (!existingApplications.isEmpty()) {
            // 过滤出待审核(1)和审核通过(2)状态的申请
            List<WorkRegistrationApplicationDO> conflictApplications = existingApplications.stream()
                    .filter(app -> Objects.equals(app.getAuditStatus(), WorkRegistrationAuditStatusEnum.PENDING.getCode()) ||
                                   Objects.equals(app.getAuditStatus(), WorkRegistrationAuditStatusEnum.APPROVED.getCode()))
                    .collect(Collectors.toList());

            if (!conflictApplications.isEmpty()) {
                WorkRegistrationApplicationDO conflictApp = conflictApplications.get(0);
                String statusDesc = WorkRegistrationAuditStatusEnum.getByCode(conflictApp.getAuditStatus()).getDesc();
                String errorMessage = String.format("作品名称在申请表中已存在: %s（登记号: %s，状态: %s）",
                        workName, conflictApp.getRegistrationNumber(), statusDesc);
                return Result.ofFailMsg(errorMessage);
            }
        }

        // 2. 检查作品表中是否存在相同作品名称且未删除
        List<WorkRegistrationDO> existingWorkRegistrations = workRegistrationService.findByWorkNameAndDeleted(workName, SqlConstant.UN_DELETED);

        if (!existingWorkRegistrations.isEmpty()) {
            //检查作品表中是否存在相同作品名称
            WorkRegistrationDO existingWork = existingWorkRegistrations.get(0);
            String errorMessage = String.format("作品名称在作品表中已存在且未删除: %s（登记号: %s）",
                    workName, existingWork.getRegistrationNumber());
            return Result.ofFailMsg(errorMessage);
        }

        return Result.ofSuccess("作品名称校验通过");
    }

    /**
     * 验证JSON文件内部的作品名称重复问题
     *
     * @param requestList 导入数据列表
     * @return 验证结果
     */
    private Result<String> validateInternalWorkNameDuplicates(List<WorkRegistrationApplicationRequest> requestList) {
        // 用于存储已出现的作品名称和对应的行号
        Map<String, Integer> workNameMap = new HashMap<>();
        List<String> duplicateErrors = new ArrayList<>();

        for (int i = 0; i < requestList.size(); i++) {
            WorkRegistrationApplicationRequest request = requestList.get(i);
            int lineNumber = i + 1;

            String workName = request.getWorkName();

            // 检查作品名称重复
            if (StringUtils.isNotBlank(workName)) {
                if (workNameMap.containsKey(workName)) {
                    duplicateErrors.add(String.format("第%d行的作品名称与第%d行重复: %s",
                            lineNumber, workNameMap.get(workName), workName));
                } else {
                    workNameMap.put(workName, lineNumber);
                }
            }
        }

        // 如果发现重复，返回错误信息
        if (!duplicateErrors.isEmpty()) {
            String errorMessage = "JSON文件内部存在作品名称重复问题：\n" + String.join("\n", duplicateErrors);
            return Result.ofFailMsg(errorMessage);
        }

        return Result.ofSuccess("内部作品名称校验通过");
    }

    /**
     * 验证与数据库中现有数据的作品名称重复问题
     *
     * @param requestList 导入数据列表
     * @return 验证结果
     */
    private Result<String> validateDatabaseWorkNameDuplicates(List<WorkRegistrationApplicationRequest> requestList) {
        // 提取所有作品名称
        List<String> workNames = requestList.stream()
                .map(WorkRegistrationApplicationRequest::getWorkName)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (workNames.isEmpty()) {
            return Result.ofSuccess("无作品名称需要校验");
        }

        // 检查作品名称在数据库中是否存在
        List<WorkRegistrationApplicationDO> existingApplications = workRegistrationApplicationService.findByWorkNames(workNames);

        if (existingApplications.isEmpty()) {
            return Result.ofSuccess("数据库作品名称校验通过");
        }

        // 构建作品名称到行号的映射
        Map<String, List<Integer>> workNameToLineMap = new HashMap<>();
        for (int i = 0; i < requestList.size(); i++) {
            WorkRegistrationApplicationRequest request = requestList.get(i);
            int lineNumber = i + 1;
            String workName = request.getWorkName();

            if (StringUtils.isNotBlank(workName)) {
                workNameToLineMap.computeIfAbsent(workName, k -> new ArrayList<>()).add(lineNumber);
            }
        }

        List<String> duplicateErrors = new ArrayList<>();
        for (WorkRegistrationApplicationDO existingApp : existingApplications) {
            String workName = existingApp.getWorkName();
            List<Integer> lineNumbers = workNameToLineMap.get(workName);
            if (lineNumbers != null) {
                for (Integer lineNumber : lineNumbers) {
                    duplicateErrors.add(String.format("第%d行的作品名称在数据库中已存在: %s（已存在的登记号: %s）",
                            lineNumber, workName, existingApp.getRegistrationNumber()));
                }
            }
        }

        // 如果发现重复，返回错误信息
        if (!duplicateErrors.isEmpty()) {
            String errorMessage = "检测到与数据库中现有数据的作品名称重复问题：\n" + String.join("\n", duplicateErrors);
            return Result.ofFailMsg(errorMessage);
        }

        return Result.ofSuccess("数据库作品名称校验通过");
    }

    @Override
    public Result<Boolean> trashMove(Integer id) {
        WorkRegistrationApplicationDO applicationDO = workRegistrationApplicationService.findById(id);
        if (applicationDO == null) {
            return Result.ofFailMsg("申请不存在");
        }
        Integer currentUserId = TokenService.getLoginUser().getUserId();
        if (!applicationDO.getApplicantUserId().equals(currentUserId)) {
            return Result.ofFailMsg("无权限操作");
        }
        applicationDO.setDeleted(SqlConstant.DELETED);
        applicationDO.setDeletedTime(LocalDateTime.now());
        applicationDO.setModifyTime(LocalDateTime.now());
        applicationDO.setDeletedUserId(currentUserId);
        workRegistrationApplicationService.update(applicationDO);
        return Result.ofSuccess();
    }

    @Override
    public Result<Boolean> trashRestore(Integer id) {
        WorkRegistrationApplicationDO applicationDO = workRegistrationApplicationService.findById(id);
        if (applicationDO == null) {
            return Result.ofFailMsg("申请不存在");
        }
        applicationDO.setDeleted(SqlConstant.UN_DELETED);
        applicationDO.setModifyTime(LocalDateTime.now());
        workRegistrationApplicationService.update(applicationDO);
        return Result.ofSuccess();
    }

    private List<WorkRegistrationApplicationRecordVo> buildAuditRecords(Integer applicantUserId, Integer reviewedUserId, Integer applicationId){
        // 补充审核流程信息
        List<WorkRegistrationRecordDO> records = workRegistrationRecordService.listByApplicationId(applicationId);
        SysUserDO applicantUser = sysUserService.findUserById(applicantUserId).get();
        SysUserDO reviewedUser;
        if(Objects.nonNull(reviewedUserId)){
            reviewedUser = sysUserService.findUserById(reviewedUserId).get();
        } else {
            reviewedUser = null;
        }
        Integer userId = TokenService.getLoginUser().getUserId();
        List<WorkRegistrationApplicationRecordVo> recordVos = records.stream().map(record -> {
            WorkRegistrationApplicationRecordVo recordVo = new WorkRegistrationApplicationRecordVo();
            recordVo.setOperateTime(record.getCreateTime());
            WorkRegistrationAuditStatusEnum status = WorkRegistrationAuditStatusEnum.getByCode(record.getAuditStatus());
            switch (status) {
                case PENDING:
                    // 待审核
                    recordVo.setOperator(getDesensitizedUsername(applicantUser, userId));
                    recordVo.setOperateContent("提交申请");
                    break;
                case APPROVED:
                    // 审核通过
                    recordVo.setOperator(getDesensitizedUsername(reviewedUser, userId));
                    recordVo.setOperateContent("审核通过,审核意见:" + record.getAuditComment());
                    break;
                case REJECTED:
                    // 审核驳回
                    recordVo.setOperator(getDesensitizedUsername(reviewedUser, userId));
                    recordVo.setOperateContent("审核驳回,审核意见:" + record.getAuditComment());
                    break;
                default:
                    // 未知状态的处理
                    recordVo.setOperator(null);
                    recordVo.setOperateContent("未知操作");
                    break;
            }
            return recordVo;
        }).collect(Collectors.toList());
        return recordVos;
    }

    @Override
    public Result<WorkRegistrationApplicationAuditDetailVo> auditDetail(Integer id) {
        WorkRegistrationApplicationAuditDetailVo result = new WorkRegistrationApplicationAuditDetailVo();
        Result<WorkRegistrationApplicationDetailVo> detail = detail(id);
        BeanUtils.copyProperties(detail.getData(), result);
        result.setRecord(detail.getData().getRecord());
        return Result.ofSuccess(result);
    }

    /**
     * 获取脱敏后的用户名
     * @param user 用户对象
     * @param currentUserId 当前用户ID
     * @return 脱敏后的用户名，如果是当前用户则不脱敏
     */
    private String getDesensitizedUsername(SysUserDO user, Integer currentUserId) {
        if (user == null) {
            return null;
        }
        // 如果是当前用户，不脱敏
        if (Objects.equals(currentUserId, user.getId())) {
            return user.getUsername();
        }

        // 非当前用户，进行脱敏
        return DesensitizationUtil.middle(user.getUsername());
    }

    /**
     * 解析CSV文件内容
     * @param csvContent CSV文件内容
     * @return 解析后的申请请求列表
     */
    private List<WorkRegistrationApplicationRequest> parseCsvFile(String csvContent) {
        List<WorkRegistrationApplicationRequest> requestList = new ArrayList<>();
        String[] lines = csvContent.split("\n");

        if (lines.length <= 1) {
            throw new RuntimeException("CSV文件没有数据行");
        }

        // 解析表头（第一行）
        String headerLine = lines[0].trim();
        String[] headers = parseCsvLine(headerLine);

        // 验证表头格式
        validateCsvHeaders(headers);

        // 解析数据行
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i].trim();
            if (StringUtils.isBlank(line)) {
                continue; // 跳过空行
            }

            try {
                String[] values = parseCsvLine(line);
                WorkRegistrationApplicationRequest request = convertCsvRowToRequest(values, i + 1);
                requestList.add(request);
            } catch (Exception e) {
                throw new RuntimeException(String.format("解析第%d行数据失败: %s", i + 1, e.getMessage()));
            }
        }

        return requestList;
    }

    /**
     * 解析CSV行，处理引号和逗号
     * @param line CSV行内容
     * @return 解析后的字段数组
     */
    private String[] parseCsvLine(String line) {
        List<String> result = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inQuotes = false;

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 双引号转义
                    current.append('"');
                    i++; // 跳过下一个引号
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                // 字段分隔符
                result.add(current.toString());
                current.setLength(0);
            } else {
                current.append(c);
            }
        }

        // 添加最后一个字段
        result.add(current.toString());

        return result.toArray(new String[0]);
    }

    /**
     * 验证CSV表头格式
     * @param headers 表头数组
     */
    private void validateCsvHeaders(String[] headers) {
        String[] expectedHeaders = {
            "作品名称", "登记编号", "申请原因", "文件名称", "文件大小",
            "文件类型", "文件格式", "原始文件哈希", "带水印文件哈希",
            "水印哈希", "暗水印哈希", "元数据", "水印配置", "品质参数"
        };

        if (headers.length != expectedHeaders.length) {
            throw new RuntimeException(String.format("CSV表头列数不正确，期望%d列，实际%d列",
                expectedHeaders.length, headers.length));
        }

        for (int i = 0; i < expectedHeaders.length; i++) {
            if (!expectedHeaders[i].equals(headers[i].trim())) {
                throw new RuntimeException(String.format("CSV表头第%d列不正确，期望'%s'，实际'%s'",
                    i + 1, expectedHeaders[i], headers[i].trim()));
            }
        }
    }

    /**
     * 将CSV行数据转换为申请请求对象
     * @param values CSV行数据
     * @param lineNumber 行号
     * @return 申请请求对象
     */
    private WorkRegistrationApplicationRequest convertCsvRowToRequest(String[] values, int lineNumber) {
        if (values.length != 14) {
            throw new RuntimeException(String.format("第%d行数据列数不正确，期望14列，实际%d列",
                lineNumber, values.length));
        }

        WorkRegistrationApplicationRequest request = new WorkRegistrationApplicationRequest();

        try {
            request.setWorkName(values[0].trim());
            request.setRegistrationNumber(values[1].trim());
            request.setApplicationReason(values[2].trim());
            request.setFileName(values[3].trim());

            // 文件大小
            String fileSizeStr = values[4].trim();
            if (StringUtils.isNotBlank(fileSizeStr)) {
                try {
                    request.setFileSize(Long.parseLong(fileSizeStr));
                } catch (NumberFormatException e) {
                    throw new RuntimeException("文件大小必须是数字");
                }
            }

            // 文件类型
            String fileTypeStr = values[5].trim();
            if (StringUtils.isNotBlank(fileTypeStr)) {
                try {
                    request.setFileType(Integer.parseInt(fileTypeStr));
                } catch (NumberFormatException e) {
                    throw new RuntimeException("文件类型必须是数字（0-音频，1-视频，2-图片）");
                }
            }

            request.setFileFormat(values[6].trim());
            request.setOriginalWorkHash(values[7].trim());
            request.setWatermarkedWorkHash(values[8].trim());
            request.setWatermarkHash(values[9].trim());
            request.setHiddenWaterMarkHash(values[10].trim());
            request.setMetaData(values[11].trim());
            request.setWatermarkConfig(values[12].trim());
            request.setQualityMetric(values[13].trim());

            // 设置为非草稿状态
            request.setDraft(false);

        } catch (NumberFormatException e) {
            throw new RuntimeException(String.format("第%d行数据格式错误: %s", lineNumber, e.getMessage()));
        }

        return request;
    }
}
