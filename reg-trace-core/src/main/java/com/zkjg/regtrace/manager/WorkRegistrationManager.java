package com.zkjg.regtrace.manager;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationFileListRequest;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationGrowthVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationRetraceVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationTrendVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationFileVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationContentDetailVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:45
 */
public interface WorkRegistrationManager {

    List<UserRegistrationTrendVo> getUserRegistrationTrend(String type, String date);

    List<UserRegistrationGrowthVo> getGrowthRate(String type, String date);

    List<UserRegistrationRetraceVo> getRetrace();

    PageResult<WorkRegistrationApplicationFileVo> getContent(QueryWorkRegistrationApplicationFileListRequest request);

    WorkRegistrationContentDetailVo getContentDetail(Integer id);

    void getRetraceExcel(HttpServletResponse response);

    void getRegistrationTrendExcel(HttpServletResponse response, String type);

    void getGrowthRateExcel(HttpServletResponse response, String type);

    Result<Boolean> recycleContent(Integer id, Integer status);
}
