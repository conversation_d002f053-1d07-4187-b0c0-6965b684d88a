package com.zkjg.regtrace.validation;

import com.zkjg.regtrace.common.enums.ExceptionEnum;
import com.zkjg.regtrace.common.enums.FileTypeEnum;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRequest;
import com.zkjg.regtrace.service.ExceptionLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class TraceabilityValidator {

    private final ExceptionLogService exceptionLogService;

    /**
     * 文件类型 -> 支持的格式集合 映射
     */
    private static final Map<Integer, Set<String>> TYPE_FORMAT_MAP;

    static {
        Map<Integer, Set<String>> map = new HashMap<>();
        map.put(FileTypeEnum.AUDIO.getCode(), new HashSet<>(Arrays.asList("mp3", "wav", "aac")));
        map.put(FileTypeEnum.VIDEO.getCode(), new HashSet<>(Arrays.asList("mp4", "avi", "mkv")));
        map.put(FileTypeEnum.IMAGE.getCode(), new HashSet<>(Arrays.asList("jpg", "jpeg", "png")));
        TYPE_FORMAT_MAP = Collections.unmodifiableMap(map);
    }

    public void validate(TraceabilityRequest req) {
        Set<String> errorCategories = new LinkedHashSet<>();

        String format = safeLower(req.getFileFormat());
        Integer type = req.getFileType();
        String traceNumber = (req.getTraceabilityNumber() == null || req.getTraceabilityNumber().trim().isEmpty())
                ? "无溯源编号" : req.getTraceabilityNumber();

        // 信息遗漏
        if (isBlank(req.getFileName()) || isBlank(format) || type == null
                || (isBlank(req.getOriginalWorkHash()) && isBlank(req.getWatermarkedWorkHash()))) {
            errorCategories.add("信息遗漏");
        }

        // 数据格式错误
        boolean isTypeKnown = TYPE_FORMAT_MAP.containsKey(type);
        boolean isFormatKnown = TYPE_FORMAT_MAP.values().stream().anyMatch(set -> set.contains(format));

        if (!isTypeKnown || !isFormatKnown) {
            errorCategories.add("数据格式错误");
        }

        // 数据不匹配
        if (isTypeKnown && isFormatKnown) {
            Set<String> expectedFormats = TYPE_FORMAT_MAP.get(type);
            if (!expectedFormats.contains(format)) {
                errorCategories.add("数据不匹配");
            }
        }

        if (!errorCategories.isEmpty()) {
            String logContent = errorCategories.stream()
                    .map(cat -> cat + "（溯源编号：" + traceNumber + "）")
                    .collect(Collectors.joining("；"));

            exceptionLogService.save(ExceptionLogDO.builder()
                    .traceabilityNumber(traceNumber)
                    .exceptionType(ExceptionEnum.TRACEABILITY_EXCEPTION.getType())
                    .operationTime(LocalDateTime.now())
                    .operator(req.getCreator())
                    .errorMessage(logContent)
                    .build());

            log.warn("参数校验发现错误，已记录异常日志");
        }
    }

    private boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }

    private String safeLower(String s) {
        return s == null ? null : s.toLowerCase();
    }
}
