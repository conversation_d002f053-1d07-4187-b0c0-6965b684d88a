package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.SysUserChangeHistoryDO;
import com.zkjg.regtrace.persistence.repository.SysUserChangeHistoryRepository;
import com.zkjg.regtrace.service.SysUserChangeHistoryService;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class SysUserChangeHistoryServiceImpl implements SysUserChangeHistoryService {

    @Resource
    private SysUserChangeHistoryRepository sysUserChangeHistoryRepository;

    @Override
    public void saveUserHistory(SysUserChangeHistoryDO sysUserChangeHistoryDO) {
        sysUserChangeHistoryRepository.save(sysUserChangeHistoryDO);
    }

    @Override
    public Page<SysUserChangeHistoryDO> findAll(Specification<SysUserChangeHistoryDO> specification, PageRequest pageRequest) {
        return sysUserChangeHistoryRepository.findAll(specification, pageRequest);
    }

    @Override
    public Optional<SysUserChangeHistoryDO> selectUserHistory(Integer id) {
        return sysUserChangeHistoryRepository.findById(id);
    }
}
