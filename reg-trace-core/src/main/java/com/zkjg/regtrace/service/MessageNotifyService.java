package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.MessageNotifyDO;
import com.zkjg.regtrace.persistence.vo.request.message.MessageNotifyRequest;
import org.springframework.data.domain.Page;

import java.util.List;

public interface MessageNotifyService {
    /**
     * 发送站内信
     * @param receiveId 接收者id
     * @param senderId 发送者id
     * @param content 内容
     * @param summary 概述
     * @param category 分类
     * @param sendType 发送类型
     * @param status 发送结果
     */
    void sendMessageNotify(Integer receiveId, Integer senderId, String content, String summary, Integer category,Integer sendType,Integer status, Integer level);

    Page<MessageNotifyDO> messages(MessageNotifyRequest messageNotifyRequest);

    Long unReadCount(Integer userId);

    List<MessageNotifyDO> messageList(MessageNotifyRequest messageNotifyRequest);
}
