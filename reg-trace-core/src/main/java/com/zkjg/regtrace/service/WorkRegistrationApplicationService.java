package com.zkjg.regtrace.service;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.dto.jpa.ListQueryWorkRegistrationApplicationJpaSpec;
import com.zkjg.regtrace.persistence.dto.workregistration.BatchTraceabilityResult;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.ContentManagerQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.ContentManagerVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * 音视频登记服务类
 * <AUTHOR>
 * @description 登记申请服务
 * @create 2025/6/11 16:50
 */
public interface WorkRegistrationApplicationService {

    /**
     * 从申请请求中保存申请信息
     * @param req 申请请求数据
     */
    WorkRegistrationApplicationDO saveFromApplicationRequest(WorkRegistrationApplicationRequest req);

    /**
     * 根据申请ID查询申请信息
     * @param id 申请ID
     * @return 申请信息
     */
    WorkRegistrationApplicationDO findById(Integer id);

    /**
     * 更新申请信息
     * @param application 申请信息
     */
    void update(WorkRegistrationApplicationDO application);

    /**
     * 批量保存申请信息
     * @param applicationList 申请信息列表
     * @return 保存后的申请信息列表（包含生成的ID）
     */
    List<WorkRegistrationApplicationDO> batchSave(List<WorkRegistrationApplicationDO> applicationList);

    /**
     * 查询所有申请信息
     * @param jpaSpec 查询条件
     * @return 申请信息列表
     */
    List<WorkRegistrationApplicationDO> findAll(ListQueryWorkRegistrationApplicationJpaSpec jpaSpec);

    /**
     * 分页查询申请信息
     * @param specification 查询条件
     * @param pageable 分页参数
     * @return 申请信息分页结果
     */
    Page<WorkRegistrationApplicationDO> findAll(Specification<WorkRegistrationApplicationDO> specification, Pageable pageable);

    /**
     * 根据作品名称查询申请信息
     * @param workNames 作品名称列表
     * @return 申请信息列表
     */
    List<WorkRegistrationApplicationDO> findByWorkNames(List<String> workNames);

    /**
     * 根据作品名称模糊查询申请信息
     * @param workName 作品名称（模糊查询）
     * @return 申请信息列表
     */
    Result<List<WorkRegistrationApplicationVo>> findByWorkNameContainingAndAuditStatus(String workName);

    WorkRegistrationApplicationDO findByRegistrationNumber(String registrationNumber);

    Result<PageResult<WorkRegistrationApplicationDetailVo>> listAudit(QueryWorkRegistrationApplicationRequest req);

    Page<ContentManagerVo> listContentManager(ContentManagerQueryRequest req);

    List<BatchTraceabilityResult> batchQueryRegistrationApplication(List<ConditionalTraceabilityRequest> requests);
}
