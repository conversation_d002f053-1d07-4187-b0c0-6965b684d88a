package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import com.zkjg.regtrace.persistence.jpa.WorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.repository.WorkRegistrationApplicationFileRepository;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.service.WorkRegistrationApplicationFileService;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@Service
public class WorkRegistrationApplicationFileServiceImpl implements WorkRegistrationApplicationFileService {

    @Resource
    private WorkRegistrationApplicationFileRepository workRegistrationApplicationFileRepository;

    @PersistenceContext
    private EntityManager entityManager;


    @Override
    public void save(WorkRegistrationApplicationFileDO file) {
        this.workRegistrationApplicationFileRepository.save(file);
    }

    @Override
    public WorkRegistrationApplicationFileDO findByApplicationId(Integer applicationId) {
        return this.workRegistrationApplicationFileRepository.findByApplicationId(applicationId);
    }

    @Override
    public WorkRegistrationApplicationFileDO findByFileOrMarkedHash(String hash) {
        return workRegistrationApplicationFileRepository.findByOriginalWorkHashOrWatermarkedWorkHashAndDeleted(hash, hash, SqlConstant.UN_DELETED);
    }

    @Override
    public List<WorkRegistrationApplicationFileDO> findAll(Specification<WorkRegistrationApplicationFileDO> specification) {
        return workRegistrationApplicationFileRepository.findAll(specification);
    }

    @Override
    public Page<WorkRegistrationApplicationFileDO> findAll(Specification<WorkRegistrationApplicationFileDO> specification, PageRequest pageRequest) {
        return workRegistrationApplicationFileRepository.findAll(specification, pageRequest);
    }

    @Override
    public void batchSave(List<WorkRegistrationApplicationFileDO> fileList) {
        this.workRegistrationApplicationFileRepository.saveAll(fileList);
    }

    @Override
    public List<WorkRegistrationApplicationFileDO> findByOriginalWorkHashIn(List<String> originalWorkHashes) {
        if (CollectionUtils.isEmpty(originalWorkHashes)) {
            return Lists.newArrayList();
        }
        return this.workRegistrationApplicationFileRepository.findByOriginalWorkHashInAndDeleted(originalWorkHashes, SqlConstant.UN_DELETED);
    }

    @Override
    public List<WorkRegistrationApplicationFileDO> findByWatermarkedWorkHashIn(List<String> watermarkedWorkHashes) {
        if (CollectionUtils.isEmpty(watermarkedWorkHashes)) {
            return Lists.newArrayList();
        }
        return this.workRegistrationApplicationFileRepository.findByWatermarkedWorkHashInAndDeleted(watermarkedWorkHashes, SqlConstant.UN_DELETED);
    }
    @Override
    public Long count(QueryWorkRegistrationApplicationFileJpaSpec jpaSpec) {
        Specification<WorkRegistrationApplicationFileDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        return workRegistrationApplicationFileRepository.count(specification);
    }

    @Override
    public WorkFileQueryResultDto findByConditionalTraceabilityRequest(ConditionalTraceabilityRequest req) {
        return workRegistrationApplicationFileRepository.querySingleWorkFileInfo(
                StatusEnum.NO.getCode(), // recycle
                SqlConstant.UN_DELETED,  // deleted
                req.getStartTime(),
                req.getEndTime(),
                req.getRegistrationNumber(),
                req.getFileName(),
                req.getHash());
    }
}
