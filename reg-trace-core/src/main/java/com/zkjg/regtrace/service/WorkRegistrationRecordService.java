package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.WorkRegistrationRecordDO;

import java.util.List;

public interface WorkRegistrationRecordService {
    /**
     * 保存记录
     * @param record 记录数据
     */
    void save(WorkRegistrationRecordDO record);

    /**
     * 批量保存记录
     * @param recordList 记录数据列表
     */
    void batchSave(List<WorkRegistrationRecordDO> recordList);

    /**
     * 根据申请ID查询记录
     * @param id 申请ID
     * @return 记录列表
     */
    List<WorkRegistrationRecordDO> listByApplicationId(Integer id);
}
