package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.MessageEnum;
import com.zkjg.regtrace.common.enums.MessageTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.assembler.MessageNotifyAssembler;
import com.zkjg.regtrace.persistence.dto.jpa.QueryMessageNotifyJpaSpec;
import com.zkjg.regtrace.persistence.entity.MessageNotifyDO;
import com.zkjg.regtrace.persistence.repository.MessageNotifyRepository;
import com.zkjg.regtrace.persistence.vo.request.message.MessageNotifyRequest;
import com.zkjg.regtrace.service.MessageNotifyService;
import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.zkjg.regtrace.common.constants.PagingQuerySortConstant.SORT_CREATE_TIME;

@Service
public class MessageNotifyServiceImpl implements MessageNotifyService {

    @Resource
    private MessageNotifyRepository messageNotifyRepository;
    @Override
    public void sendMessageNotify(Integer receiveId, Integer senderId, String content, String summary, Integer category,Integer sendType,Integer status, Integer level) {
        messageNotifyRepository.save(MessageNotifyDO.builder()
                .receiverId(receiveId)
                .deleted(SqlConstant.UN_DELETED)
                .senderId(senderId)
                .content(content)
                .remark(summary)
                .sendType(sendType)
                .category(category)
                .level(level)
                .isRead(StatusEnum.NO.getCode())
                .status(status)
                .build());
    }

    @Override
    public Page<MessageNotifyDO> messages(MessageNotifyRequest req) {
        Sort sort = Sort.by(Sort.Order.desc("level"))
                .and(Sort.by(Sort.Order.asc("isRead")))
                .and(Sort.by(Sort.Order.desc(SORT_CREATE_TIME)));

        Pageable pageable = PageRequest.of(req.getPage() - CommonConstant.ONE_INT, req.getSize(), sort);
        QueryMessageNotifyJpaSpec jpaSpec = MessageNotifyAssembler.toJpaSpec(req);
        Specification<MessageNotifyDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        return messageNotifyRepository.findAll(specification, pageable);
    }

    @Override
    public Long unReadCount(Integer userId) {
        return messageNotifyRepository.count(Example.of(MessageNotifyDO.builder()
                        .isRead(StatusEnum.NO.getCode())
                        .status(StatusEnum.YES.getCode())
                        .receiverId(userId)
                        .deleted(SqlConstant.UN_DELETED)
                        .sendType(MessageTypeEnum.INTERNAL.getType())
                .build()));
    }

    @Override
    public List<MessageNotifyDO> messageList(MessageNotifyRequest req) {
        QueryMessageNotifyJpaSpec jpaSpec = MessageNotifyAssembler.toJpaSpec(req);
        Specification<MessageNotifyDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        return messageNotifyRepository.findAll(specification);
    }
}
