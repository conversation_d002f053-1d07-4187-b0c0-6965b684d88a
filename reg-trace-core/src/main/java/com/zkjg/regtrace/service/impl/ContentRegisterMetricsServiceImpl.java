package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.ContentRegisterMetricsDO;
import com.zkjg.regtrace.persistence.repository.ContentRegisterMetricsRepository;
import com.zkjg.regtrace.service.ContentRegisterMetricsService;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-06-18 08:58
 */
@Service
public class ContentRegisterMetricsServiceImpl implements ContentRegisterMetricsService {

    @Resource
    private ContentRegisterMetricsRepository contentRegisterMetricsRepository;


    @Override
    public void save(ContentRegisterMetricsDO metricsDO) {
        contentRegisterMetricsRepository.save(metricsDO);
    }
}
