package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.persistence.entity.ProblemManagementDO;
import com.zkjg.regtrace.persistence.repository.ProblemManagementRepository;
import com.zkjg.regtrace.service.ProblemManagementService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 问题管理Service实现类
 * <AUTHOR>
 */
@Service
public class ProblemManagementServiceImpl implements ProblemManagementService {

    @Resource
    private ProblemManagementRepository problemManagementRepository;

    @Override
    public ProblemManagementDO save(ProblemManagementDO problemManagement) {
        return problemManagementRepository.save(problemManagement);
    }

    @Override
    public Optional<ProblemManagementDO> findById(Long id) {
        return problemManagementRepository.findById(id);
    }

    @Override
    public Optional<ProblemManagementDO> findByIdAndDeleted(Long id, Integer deleted) {
        return problemManagementRepository.findByIdAndDeleted(id, deleted);
    }

    @Override
    public Page<ProblemManagementDO> findAll(Specification<ProblemManagementDO> specification, Pageable pageable) {
        return problemManagementRepository.findAll(specification, pageable);
    }

    @Override
    public void deleteById(Long id) {
        Optional<ProblemManagementDO> optional = problemManagementRepository.findById(id);
        if (optional.isPresent()) {
            ProblemManagementDO problemManagement = optional.get();
            problemManagement.setDeleted(StatusEnum.YES.getCode());
            problemManagementRepository.save(problemManagement);
        }
    }
}
