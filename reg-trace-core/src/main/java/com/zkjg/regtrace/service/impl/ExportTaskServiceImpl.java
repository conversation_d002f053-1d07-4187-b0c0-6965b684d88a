package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import com.zkjg.regtrace.persistence.entity.ExportTaskDO;
import com.zkjg.regtrace.persistence.repository.ExportTaskRepository;
import com.zkjg.regtrace.service.ExportTaskService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Service
public class ExportTaskServiceImpl implements ExportTaskService {

    @Resource
    private ExportTaskRepository exportTaskRepository;

    @Override
    public void updateScheduleType(Integer id, ExportTaskScheduleTypeEnum scheduleType) {
        LoginUser loginUser = TokenService.getLoginUser();
        if (Objects.isNull(id)) {
            exportTaskRepository.save(ExportTaskDO.builder()
                    .creatorId(loginUser.getUserId())
                    .scheduleType(scheduleType)
                    .build());
            return;
        }
        Optional<ExportTaskDO> optionalExportTaskDO = exportTaskRepository.findById(id);
        if (optionalExportTaskDO.isPresent()) {
            ExportTaskDO task = optionalExportTaskDO.get();
            task.setScheduleType(scheduleType);
            task.setUpdateTime(LocalDateTime.now());
            exportTaskRepository.save(task);
        }
    }

    @Override
    public ExportTaskDO findByCreatorId(Integer creatorId) {
        return exportTaskRepository.findFirstByCreatorId(creatorId);
    }
}
