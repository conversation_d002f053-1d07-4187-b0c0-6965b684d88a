package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import com.zkjg.regtrace.persistence.repository.WorkRegistrationInfoLogRepository;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationInfoLogRequest;
import com.zkjg.regtrace.service.WorkRegistrationInfoLogService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class WorkRegistrationInfoLogServiceImpl implements WorkRegistrationInfoLogService {

    @Resource
    private WorkRegistrationInfoLogRepository workRegistrationInfoLogRepository;

    @Override
    public void save(WorkRegistrationInfoLogRequest log) {
        Integer operatorUserId = TokenService.getLoginUser().getUserId();
        workRegistrationInfoLogRepository.save(ExceptionLogDO.builder()
                .operatorUserId(operatorUserId)
                .exceptionType(log.getExceptionType())
                .registrationNumber(log.getRegistrationNumber())
                .errorMessage(log.getErrorMessage())
                .operationTime(LocalDateTime.now())
                .build());
    }
}
