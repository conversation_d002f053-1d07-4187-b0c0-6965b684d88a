package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.jpa.WorkRegistrationApplicationFileJpaSpec;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

public interface WorkRegistrationApplicationFileService {
    /**
     * 保存申请文件信息
     * @param file 申请文件信息
     */
    void save(WorkRegistrationApplicationFileDO file);

    /**
     * 根据申请ID查询申请文件信息
     * @param applicationId 申请ID
     * @return 申请文件信息
     */
    WorkRegistrationApplicationFileDO findByApplicationId(Integer applicationId);

    /**
     * 根据文件/添加水印的文件哈希查询登记文件信息
     * @param hash 文件哈希
     * @return 申请文件信息
     */
    WorkRegistrationApplicationFileDO findByFileOrMarkedHash(String hash);

    List<WorkRegistrationApplicationFileDO> findAll(Specification<WorkRegistrationApplicationFileDO> specification);

    Page<WorkRegistrationApplicationFileDO> findAll(Specification<WorkRegistrationApplicationFileDO> specification, PageRequest pageRequest);

    /**
     * 批量保存申请文件信息
     * @param fileList 申请文件信息列表
     */
    void batchSave(List<WorkRegistrationApplicationFileDO> fileList);

    /**
     * 批量查询原始文件哈希是否存在（未删除）
     * @param originalWorkHashes 原始文件哈希列表
     * @return 已存在的记录列表
     */
    List<WorkRegistrationApplicationFileDO> findByOriginalWorkHashIn(List<String> originalWorkHashes);

    /**
     * 批量查询带水印文件哈希是否存在（未删除）
     * @param watermarkedWorkHashes 带水印文件哈希列表
     * @return 已存在的记录列表
     */
    List<WorkRegistrationApplicationFileDO> findByWatermarkedWorkHashIn(List<String> watermarkedWorkHashes);

    /**
     * 根据条件查询记录数
     * @param jpaSpec 查询条件
     * @return 记录数
     */
    Long count(QueryWorkRegistrationApplicationFileJpaSpec jpaSpec);

    WorkFileQueryResultDto findByConditionalTraceabilityRequest(ConditionalTraceabilityRequest req);
}
