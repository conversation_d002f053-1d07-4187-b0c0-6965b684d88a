package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.assembler.ExceptionAssembler;
import com.zkjg.regtrace.persistence.dto.exception.ExceptionDistributionDto;
import com.zkjg.regtrace.persistence.dto.exception.ExceptionFrequencyDto;
import com.zkjg.regtrace.persistence.dto.jpa.PageQueryExceptionLogJpaSpec;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import com.zkjg.regtrace.persistence.repository.ExceptionLogRepository;
import com.zkjg.regtrace.persistence.vo.request.exception.ExceptionLogRequest;
import com.zkjg.regtrace.service.ExceptionLogService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class ExceptionLogServiceImpl implements ExceptionLogService {

    @Resource
    private ExceptionLogRepository exceptionLogRepository;

    @Override
    public Page<ExceptionLogDO> pageQueryLogs(ExceptionLogRequest req) {
        Pageable pageable = PageRequest.of(req.getPage() - 1, req.getSize());
        PageQueryExceptionLogJpaSpec pageQueryExceptionLogJpaSpec = ExceptionAssembler.convertLogReqToJpaSpec(req);
        Specification<ExceptionLogDO> specification = QueryConvertUtils.toSpecification(pageQueryExceptionLogJpaSpec);
        return exceptionLogRepository.findAll(specification, pageable);
    }

    @Override
    public List<ExceptionDistributionDto> distributionsByDays() {
        List<ExceptionDistributionDto> typeCounts = exceptionLogRepository.findExceptionTypeCounts();

        float total = 0f;
        for (ExceptionDistributionDto dto : typeCounts) {
            total += dto.getTotal();
        }

        List<ExceptionDistributionDto> typeDistribution = new ArrayList<>();
        for (ExceptionDistributionDto dto : typeCounts) {
            float percentage = total == 0 ? 0f : dto.getTotal() * 100 / total;
            typeDistribution.add(ExceptionDistributionDto.builder()
                    .exceptionType(dto.getExceptionType())
                    .percentage(percentage)
                    .total(dto.getTotal())
                    .build());
        }
        return typeDistribution;
    }

    @Override
    public List<ExceptionFrequencyDto> exceptionFrequency(Integer days) {
        if (Objects.isNull(days)) {
            days = 7;
        }
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days - 1).with(LocalTime.MIN);
        List<Object[]> rawList = exceptionLogRepository.countDailyFrequency(startTime, endTime);
        List<ExceptionFrequencyDto> frequencyList = new ArrayList<>();
        for (Object[] row : rawList) {
            String dateStr = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            LocalDateTime time = LocalDate.parse(dateStr).atStartOfDay();
            frequencyList.add(new ExceptionFrequencyDto(time, count));
        }
        return frequencyList;
    }

    @Override
    public void save(ExceptionLogDO exceptionLogDO) {
        exceptionLogRepository.save(exceptionLogDO);
    }
}
