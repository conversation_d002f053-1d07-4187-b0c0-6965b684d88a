package com.zkjg.regtrace.service;

import com.zkjg.regtrace.common.utils.AdvancedExportUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 通用导出服务接口
 */
public interface UniversalExportService {

    /**
     * 通用导出接口，通过导出定义ID进行导出
     * @param exportId 导出定义ID
     * @param params 查询参数，用于提取数据
     * @param format 导出格式（EXCEL/CSV）
     * @param response HTTP响应对象
     */
    void export(String exportId, Map<String, Object> params, String format, HttpServletResponse response);
}

