package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.enums.PlatformEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.persistence.entity.SysPermissionDO;
import com.zkjg.regtrace.persistence.repository.SysPermissionRepository;
import com.zkjg.regtrace.service.SysPermissionService;

import javax.annotation.Resource;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class SysPermissionServiceImpl implements SysPermissionService {

    @Resource
    private SysPermissionRepository sysPermissionRepository;

    @Override
    public SysPermissionDO save(SysPermissionDO sysPermissionDO) {
        sysPermissionRepository.save(sysPermissionDO);
        return sysPermissionDO;
    }

    @Override
    public List<SysPermissionDO> findPermission(Specification<SysPermissionDO> specification) {
        return sysPermissionRepository.findAll(specification);
    }

    @Override
    public List<SysPermissionDO> findAll(Specification<SysPermissionDO> specification, Sort sort) {
        return sysPermissionRepository.findAll(specification, sort);
    }

    @Override
    public Optional<SysPermissionDO> findOne(Specification<SysPermissionDO> specification) {
        return sysPermissionRepository.findOne(specification);
    }

    @Override
    public Optional<SysPermissionDO> findById(Integer id) {
        return sysPermissionRepository.findById(id);
    }

    @Override
    public List<SysPermissionDO> findByParentId(Integer parentId) {
        return sysPermissionRepository.findByParentIdAndDeleted(parentId, StatusEnum.NO.getCode());
    }

    @Override
    public List<SysPermissionDO> findAllMenu(List<Integer> roles, List<Integer> permissionTypes) {
        Set<SysPermissionDO> sysPermissionDOS = sysPermissionRepository.selectMenuByPermissionType(roles, permissionTypes, PlatformEnum.PC.getCode());
        return new ArrayList<>(sysPermissionDOS);
    }

    @Override
    public List<SysPermissionDO> findAllMenu(List<Integer> permissionTypes) {
        return sysPermissionRepository.selectMenuByPermissionType(permissionTypes, PlatformEnum.PC.getCode());
    }
}
