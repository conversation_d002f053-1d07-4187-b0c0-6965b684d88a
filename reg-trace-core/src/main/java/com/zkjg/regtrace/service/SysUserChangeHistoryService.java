package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.SysUserChangeHistoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.Optional;

public interface SysUserChangeHistoryService {

    void saveUserHistory(SysUserChangeHistoryDO sysUserChangeHistoryDO);

    Page<SysUserChangeHistoryDO> findAll(Specification<SysUserChangeHistoryDO> specification, PageRequest pageRequest);

    Optional<SysUserChangeHistoryDO> selectUserHistory(Integer id);
}
