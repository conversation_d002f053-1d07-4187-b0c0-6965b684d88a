package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkFavoriteRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkSearchRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkUploadRequest;
import com.zkjg.regtrace.persistence.vo.response.watermark.WatermarkVO;
import com.zkjg.regtrace.common.page.PageResult;
import org.springframework.data.domain.Page;

import java.util.List;

public interface WatermarkService {
    /**
     * 批量上传水印
     *
     * @param userId 用户ID
     * @param reqVOList 上传请求列表
     * @return 水印信息列表
     */
    void uploadWatermarks(Integer userId, List<WatermarkUploadRequest> reqVOList);


    /**
     * 添加收藏
     *
     * @param userId 用户ID
     * @param reqVO  收藏请求
     * @return 是否成功
     */
    Boolean addFavorite(Integer userId, WatermarkFavoriteRequest reqVO);

    /**
     * 取消收藏
     *
     * @param userId      用户ID
     * @param watermarkId 水印ID
     * @return 是否成功
     */
    Boolean cancelFavorite(Integer userId, Integer watermarkId);

    /**
     * 获取用户收藏的水印列表
     *
     * @param userId 用户ID
     * @return 收藏的水印列表
     */
    List<WatermarkVO> getUserFavorites(Integer userId);

    /**
     * 获取水印详情
     *
     * @param userId      当前用户ID
     * @param watermarkId 水印ID
     * @return 水印详情
     */
    WatermarkVO getWatermarkDetail(Integer userId, Integer watermarkId);

    /**
     * 检查水印名称是否已存在
     *
     * @param userId 用户ID
     * @param name 水印名称
     * @return true=已存在，false=不存在
     */
    Boolean checkNameExists(Integer userId, String name);

    /**
     * 删除水印（只能删除自己的水印）
     * @param userId 用户ID
     * @param watermarkId 水印ID
     * @return 是否删除成功
     */
    Boolean deleteWatermark(Integer userId, Integer watermarkId);

    /**
     * 设置水印共享状态（只能设置自己的水印）
     * @param userId 用户ID
     * @param watermarkId 水印ID
     * @param isShared 是否共享 0=否 1=是
     * @return 是否成功
     */
    Boolean setWatermarkShared(Integer userId, Integer watermarkId, Integer isShared);

    /**
     * 分页获取我的水印列表（无搜索条件）
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageResult<WatermarkVO> getMyWatermarks(Integer userId, int pageNum, int pageSize);

    /**
     * 分页搜索平台共享水印，收藏的排前面，返回PageResult
     * @param userId 用户ID
     * @param reqVO 查询条件
     * @return 分页结果
     */
    PageResult<WatermarkVO> searchWatermark(Integer userId, WatermarkSearchRequest reqVO);

    /**
     * 哈希查询水印详情
     * @param hash
     * @return
     */
    WatermarkVO watermarkDetailByHash(String hash);

    /**
     * 获取热门水印排名
     * @param limit 返回数量限制
     * @param timeRange 时间范围（单位：天，null表示所有时间）
     * @return 热门水印列表
     */
    List<WatermarkVO> getPopularWatermarks(Integer userId, Integer limit, Integer timeRange);
}
