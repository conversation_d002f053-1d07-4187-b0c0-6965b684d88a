package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import com.zkjg.regtrace.service.UniversalExportService;
import com.zkjg.regtrace.service.export.ExportDefinition;
import com.zkjg.regtrace.service.export.ExportRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 通用导出服务实现类
 */
@Service
@Slf4j
public class UniversalExportServiceImpl implements UniversalExportService {

    @Resource
    private ExportRegistry exportRegistry;

    /**
     * 自动数据导出处理器 - 单一接口处理所有类型的导出
     */
    @Override
    public void export(String exportId, Map<String, Object> params, String format, HttpServletResponse response) {
        try {
            // 获取导出定义
            ExportDefinition exportDefinition = exportRegistry.getExportDefinition(exportId);
            if (exportDefinition == null) {
                throw new BusinessException("导出定义不存在: " + exportId);
            }
            // 根据查询参数提取数据
            List<?> data = exportDefinition.getDataExtractor().apply(params);
            // 转换字符串为枚举类型，默认EXCEL
            AdvancedExportUtil.ExportFormat exportFormat;
            try {
                exportFormat = AdvancedExportUtil.ExportFormat.valueOf(format.toUpperCase());
            } catch (BusinessException | NullPointerException e) {
                exportFormat = AdvancedExportUtil.ExportFormat.EXCEL; // 默认导出为Excel
            }
            // 根据导出定义选择导出方式
            AdvancedExportUtil.ExportFile file;
            // 使用目标类定义导出
            file = AdvancedExportUtil.export(
                    (List) data,
                    exportDefinition.getTargetClass(),
                    exportFormat,
                    exportDefinition.getExportName()
            );
            writeToResponse(file, response);
        } catch (Exception e) {
            log.error("通用导出失败: {}", exportId, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 将导出文件写入HTTP响应
     */
    private void writeToResponse(AdvancedExportUtil.ExportFile file, HttpServletResponse response) {
        try {
            response.setContentType(file.getContentType());
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    URLEncoder.encode(file.getFileName(), "UTF-8"));

            try (InputStream in = file.getInputStream();
                 OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
        } catch (Exception e) {
            log.error("写入响应数据异常", e);
        }
    }
}

