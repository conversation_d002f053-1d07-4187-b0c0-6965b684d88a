package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.SysPermissionDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

public interface SysPermissionService {

    SysPermissionDO save(SysPermissionDO sysPermissionDO);

    List<SysPermissionDO> findPermission(Specification<SysPermissionDO> specification);

    List<SysPermissionDO> findAll(Specification<SysPermissionDO> specification, Sort sort);

    Optional<SysPermissionDO> findOne(Specification<SysPermissionDO> specification);

    Optional<SysPermissionDO> findById(Integer id);

    List<SysPermissionDO> findByParentId(Integer parentId);

    List<SysPermissionDO> findAllMenu(List<Integer> roles, List<Integer> permissionTypes);

    List<SysPermissionDO> findAllMenu(List<Integer> permissionTypes);
}
