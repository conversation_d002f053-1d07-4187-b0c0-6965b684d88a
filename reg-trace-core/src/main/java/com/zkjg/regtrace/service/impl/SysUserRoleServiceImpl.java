package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.SysUserRoleDO;
import com.zkjg.regtrace.persistence.repository.SysUserRoleRepository;
import com.zkjg.regtrace.service.SysUserRoleService;

import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysUserRoleServiceImpl implements SysUserRoleService {

    @Resource
    private SysUserRoleRepository sysUserRoleRepository;

    @Override
    public void deleteUserPermission(Integer userId) {
        sysUserRoleRepository.deleteByUserId(userId);
    }

    @Override
    public void saveUserPermission(Integer userId, List<Integer> permissions, String operate) {
        LocalDateTime now = LocalDateTime.now();
        List<SysUserRoleDO> list = permissions.stream()
                .map(roleId -> SysUserRoleDO.builder()
                        .userId(userId)
                        .roleId(roleId)
                        .creator(operate)
                        .createTime(now)
                        .build())
                .collect(Collectors.toList());
        sysUserRoleRepository.saveAll(list);

    }
    @Override
    public void deleteByUserId(Integer userId) {
        sysUserRoleRepository.deleteByUserId(userId);
    }

    @Override
    public void deleteByRoles(List<Integer> roles) {
        sysUserRoleRepository.removeAllByRoleIdIn(roles);
    }

    @Override
    public void deleteByUserAndRole(Integer roleId, List<Integer> userIds) {
        sysUserRoleRepository.removeAllByRoleIdAndUserIdIn(roleId, userIds);
    }

    @Override
    public void saveAll(List<SysUserRoleDO> list) {
        sysUserRoleRepository.saveAll(list);
    }

    @Override
    public void flush() {
        sysUserRoleRepository.flush();
    }

    @Override
    public List<SysUserRoleDO> findByRoleId(Integer roleId) {
        return sysUserRoleRepository.findByRoleId(roleId);
    }
}
