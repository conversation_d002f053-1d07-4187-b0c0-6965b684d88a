package com.zkjg.regtrace.service.impl;

import cn.hutool.core.date.DatePattern;
import com.zkjg.regtrace.common.config.MinioConfig;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.persistence.entity.WatermarkDO;
import com.zkjg.regtrace.persistence.entity.WatermarkFavoriteDO;
import com.zkjg.regtrace.persistence.repository.WatermarkFavoriteRepository;
import com.zkjg.regtrace.persistence.repository.WatermarkRepository;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkFavoriteRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkSearchRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkUploadRequest;
import com.zkjg.regtrace.persistence.vo.response.watermark.WatermarkVO;
import com.zkjg.regtrace.service.WatermarkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.zkjg.regtrace.common.page.PageResult;

@Service
@Slf4j
public class WatermarkServiceImpl implements WatermarkService {

    @Resource
    private WatermarkRepository watermarkRepository;
    
    @Resource
    private WatermarkFavoriteRepository watermarkFavoriteRepository;
    
    @Resource
    private MinioConfig minioConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadWatermarks(Integer userId, List<WatermarkUploadRequest> reqVOList) {
        // 查询用户现有的水印数量
        long existingCount = watermarkRepository.countByUserIdAndDeleted(userId, 0);

        // 判断上传后是否超过限制（10个）
        if (existingCount + reqVOList.size() > 10) {
            throw new BusinessException("水印已达到上限，无法继续创建");
        }

        List<WatermarkDO> watermarkDOS = new ArrayList<>();
        for (int i = 0; i < reqVOList.size(); i++) {
            WatermarkUploadRequest e = reqVOList.get(i);
            WatermarkDO watermarkDO = new WatermarkDO();
            BeanUtils.copyProperties(e, watermarkDO);
            watermarkDO.setUserId(userId);
            // 生成唯一水印编号，使用索引确保每个编号不同
            String watermarkNo = generateWatermarkNo(i + 1);
            watermarkDO.setWatermarkNo(watermarkNo);
            watermarkDO.setDeleted(0);
            if (StringUtils.isNotBlank(e.getFileUrl())) {
                // 设置文件路径
                String filePath = e.getFileUrl().replace(minioConfig.getReadUrl(), "");
                watermarkDO.setFilePath(filePath);
            }
            watermarkDO.setCreateTime(LocalDateTime.now());
            watermarkDOS.add(watermarkDO);
        }
        watermarkRepository.saveAll(watermarkDOS);
    }

    private String generateWatermarkNo(int index) {
        LocalDate now = LocalDate.now();
        String dateStr = now.format(DateTimeFormatter.ofPattern("yyMMdd"));
        LocalDateTime start = now.atStartOfDay();
        LocalDateTime end = start.plusDays(1);
        long count = watermarkRepository.countByCreateTimeBetween(start, end);
        String seq = String.format("%04d", count + index);
        return "WM_" + dateStr + seq;
    }

    @Override
    public PageResult<WatermarkVO> getMyWatermarks(Integer userId, int pageNum, int pageSize) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
        Specification<WatermarkDO> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("userId"), userId));
            predicates.add(cb.equal(root.get("deleted"), 0));
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Page<WatermarkDO> page = watermarkRepository.findAll(spec, pageable);
        List<WatermarkDO> watermarkDOList = page.getContent();
        List<WatermarkVO> voList = watermarkDOList.stream().map(watermarkDO -> {
            WatermarkVO vo = WatermarkVO.fromDO(watermarkDO);
            if (StringUtils.isNotBlank(watermarkDO.getFilePath())) {
                vo.setFileUrl(minioConfig.getReadUrl() + watermarkDO.getFilePath());
            }
            return vo;
        }).collect(Collectors.toList());
        PageResult<WatermarkVO> result = new PageResult<>(page);
        result.setData(voList);
        return result;
    }

    @Override
    public PageResult<WatermarkVO> searchWatermark(Integer userId, WatermarkSearchRequest reqVO) {
        Pageable pageable = PageRequest.of(reqVO.getPageNum() - 1, reqVO.getPageSize());
        Specification<WatermarkDO> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isShared"), 1));
            predicates.add(cb.equal(root.get("deleted"), 0));
            if (StringUtils.isNotBlank(reqVO.getName())) {
                predicates.add(cb.like(root.get("name"), "%" + reqVO.getName() + "%"));
            }
            if (reqVO.getFileType() != null) {
                predicates.add(cb.equal(root.get("fileType"), reqVO.getFileType()));
            }
            if (reqVO.getStartTime() != null && reqVO.getEndTime() != null) {
                predicates.add(cb.between(root.get("createTime"), reqVO.getStartTime(), reqVO.getEndTime()));
            } else if (reqVO.getStartTime() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"),
                        LocalDateTime.parse(reqVO.getStartTime(),
                                DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN))));
            } else if (reqVO.getEndTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), LocalDateTime.parse(reqVO.getEndTime(),
                        DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN))));
            }
            if (StringUtils.isNotBlank(reqVO.getWatermarkNo())) {
                predicates.add(cb.equal(root.get("watermarkNo"), reqVO.getWatermarkNo()));
            }
            if (StringUtils.isNotBlank(reqVO.getFileHash())) {
                predicates.add(cb.equal(root.get("fileHash"), reqVO.getFileHash()));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Page<WatermarkDO> page = watermarkRepository.findAll(spec, pageable);
        List<WatermarkDO> watermarkDOList = page.getContent();
        List<WatermarkFavoriteDO> favoriteList = watermarkFavoriteRepository.findByUserIdAndDeleted(userId, 0);
        Set<Integer> favoriteWatermarkIds = favoriteList.stream().map(WatermarkFavoriteDO::getWatermarkId).collect(Collectors.toSet());
        List<WatermarkVO> voList = watermarkDOList.stream().map(watermarkDO -> {
            WatermarkVO vo = WatermarkVO.fromDO(watermarkDO);
            if (StringUtils.isNotBlank(watermarkDO.getFilePath())) {
                vo.setFileUrl(minioConfig.getReadUrl() + watermarkDO.getFilePath());
            }
            vo.setIsFavorite(favoriteWatermarkIds.contains(watermarkDO.getId()));
            return vo;
        }).sorted((a, b) -> Boolean.compare(b.getIsFavorite() != null && b.getIsFavorite(), a.getIsFavorite() != null && a.getIsFavorite())).collect(Collectors.toList());
        PageResult<WatermarkVO> result = new PageResult<>(page);
        result.setData(voList);
        return result;
    }

    @Override
    public WatermarkVO watermarkDetailByHash(String hash) {
        if (StringUtils.isEmpty(hash)) {
            throw new BusinessException("水印哈希值不能为空");
        }
        WatermarkDO watermarkDO = watermarkRepository.findByFileHashAndDeleted(hash, SqlConstant.UN_DELETED);
        if (watermarkDO == null) {
            throw new BusinessException("水印不存在");
        }
        return WatermarkVO.fromDO(watermarkDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addFavorite(Integer userId, WatermarkFavoriteRequest reqVO) {
        // 查询水印是否存在
        Optional<WatermarkDO> watermarkOpt = watermarkRepository.findById(reqVO.getWatermarkId());
        if (!watermarkOpt.isPresent() || watermarkOpt.get().getDeleted() == 1) {
            throw new BusinessException("水印不存在");
        }
        
        // 查询是否已收藏
        WatermarkFavoriteDO existFavorite = watermarkFavoriteRepository.findByUserIdAndWatermarkIdAndDeleted(
                userId, reqVO.getWatermarkId(), 0);
        
        if (existFavorite != null) {
            // 已收藏，直接返回成功
            return true;
        }
        
        // 创建收藏记录
        WatermarkFavoriteDO favoriteDO = WatermarkFavoriteDO.builder()
                .userId(userId)
                .watermarkId(reqVO.getWatermarkId())
                .remark(reqVO.getRemark())
                .createTime(LocalDateTime.now())
                .deleted(0)
                .build();
        
        watermarkFavoriteRepository.save(favoriteDO);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelFavorite(Integer userId, Integer watermarkId) {
        
        // 查询收藏记录
        WatermarkFavoriteDO favoriteDO = watermarkFavoriteRepository.findByUserIdAndWatermarkIdAndDeleted(
                userId, watermarkId, 0);
        
        if (favoriteDO == null) {
            // 未收藏，直接返回成功
            return true;
        }
        
        // 标记为已删除
        favoriteDO.setDeleted(1);
        watermarkFavoriteRepository.save(favoriteDO);
        
        return true;
    }

    @Override
    public List<WatermarkVO> getUserFavorites(Integer userId) {
        // 查询用户收藏的水印
        List<WatermarkFavoriteDO> favoriteList = watermarkFavoriteRepository.findByUserIdAndDeleted(userId, 0);
        if (favoriteList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取收藏的水印ID列表
        List<Integer> watermarkIds = favoriteList.stream()
                .map(WatermarkFavoriteDO::getWatermarkId)
                .collect(Collectors.toList());
        
        // 查询水印详情
        List<WatermarkDO> watermarkDOList = watermarkRepository.findAllById(watermarkIds);
        
        // 转换为VO，并标记为已收藏
        return watermarkDOList.stream()
                .filter(watermarkDO -> watermarkDO.getDeleted() == 0)
                .map(watermarkDO -> {
                    WatermarkVO vo = WatermarkVO.fromDO(watermarkDO);
                    
                    // 设置文件访问URL
                    if (StringUtils.isNotBlank(watermarkDO.getFilePath())) {
                        vo.setFileUrl(minioConfig.getReadUrl() + watermarkDO.getFilePath());
                    }
                    
                    // 设置为已收藏
                    vo.setIsFavorite(true);
                    
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public WatermarkVO getWatermarkDetail(Integer userId, Integer watermarkId) {

        // 查询水印详情
        Optional<WatermarkDO> watermarkOpt = watermarkRepository.findById(watermarkId);
        if (!watermarkOpt.isPresent() || watermarkOpt.get().getDeleted() == 1) {
            throw new BusinessException("水印不存在");
        }

        WatermarkDO watermarkDO = watermarkOpt.get();
        WatermarkVO vo = WatermarkVO.fromDO(watermarkDO);
        // 设置文件访问URL
        if (StringUtils.isNotBlank(watermarkDO.getFilePath())) {
            vo.setFileUrl(minioConfig.getReadUrl() + watermarkDO.getFilePath());
        }

        // 查询是否收藏
        WatermarkFavoriteDO favoriteDO = watermarkFavoriteRepository.findByUserIdAndWatermarkIdAndDeleted(
                userId, watermarkId, 0);
        vo.setIsFavorite(favoriteDO != null);
        // 添加PV
        if (watermarkDO.getIsShared() == 1) {
            // 如果是共享的水印，则增加点击次数
            watermarkDO.setClickCount(watermarkDO.getClickCount() + 1);
            watermarkRepository.save(watermarkDO);
        }
        return vo;
    }

    @Override
    public Boolean checkNameExists(Integer userId, String name) {
        // 查询指定用户下是否存在相同名称的未删除水印
        return watermarkRepository.existsByUserIdAndNameAndDeleted(userId, name, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWatermark(Integer userId, Integer watermarkId) {
        Optional<WatermarkDO> opt = watermarkRepository.findById(watermarkId);
        if (!opt.isPresent() || opt.get().getDeleted() == 1) {
            throw new BusinessException("水印不存在");
        }
        WatermarkDO watermarkDO = opt.get();
        if (!Objects.equals(watermarkDO.getUserId(), userId)) {
            throw new BusinessException("只能删除自己的水印");
        }
        watermarkDO.setDeleted(1);
        watermarkRepository.save(watermarkDO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setWatermarkShared(Integer userId, Integer watermarkId, Integer isShared) {
        Optional<WatermarkDO> opt = watermarkRepository.findById(watermarkId);
        if (!opt.isPresent() || opt.get().getDeleted() == 1) {
            throw new BusinessException("水印不存在");
        }
        WatermarkDO watermarkDO = opt.get();
        if (!Objects.equals(watermarkDO.getUserId(), userId)) {
            throw new BusinessException("只能设置自己的水印");
        }
        watermarkDO.setIsShared(isShared);
        watermarkRepository.save(watermarkDO);
        return true;
    }

    @Override
    public List<WatermarkVO> getPopularWatermarks(Integer userId, Integer limit, Integer timeRange) {
        // 创建查询条件
        Specification<WatermarkDO> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 只查询共享的水印
            predicates.add(cb.equal(root.get("isShared"), 1));

            // 排除已删除的水印
            predicates.add(cb.equal(root.get("deleted"), 0));

            // 如果指定了时间范围
            if (timeRange != null && timeRange > 0) {
                LocalDateTime startTime = LocalDateTime.now().minusDays(timeRange);
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), startTime));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 创建排序条件：按点击次数降序
        Sort sort = Sort.by(Sort.Direction.DESC, "clickCount");

        // 设置分页（使用limit参数）
        Pageable pageable = PageRequest.of(0, limit != null ? limit : 10, sort);

        // 查询数据
        Page<WatermarkDO> page = watermarkRepository.findAll(spec, pageable);
        List<WatermarkDO> watermarkDOList = page.getContent();

        // 查询当前用户的收藏记录
        List<WatermarkFavoriteDO> favoriteList = watermarkFavoriteRepository.findByUserIdAndDeleted(userId, 0);
        Set<Integer> favoriteWatermarkIds = favoriteList.stream()
                .map(WatermarkFavoriteDO::getWatermarkId)
                .collect(Collectors.toSet());

        // 转换为VO
        return watermarkDOList.stream().map(watermarkDO -> {
            WatermarkVO vo = WatermarkVO.fromDO(watermarkDO);

            // 设置文件访问URL
            if (StringUtils.isNotBlank(watermarkDO.getFilePath())) {
                vo.setFileUrl(minioConfig.getReadUrl() + watermarkDO.getFilePath());
            }

            // 设置是否已收藏
            vo.setIsFavorite(favoriteWatermarkIds.contains(watermarkDO.getId()));

            return vo;
        }).collect(Collectors.toList());
    }
}
