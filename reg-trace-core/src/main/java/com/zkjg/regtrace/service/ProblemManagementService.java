package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.ProblemManagementDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Optional;

/**
 * 问题管理Service接口
 * <AUTHOR>
 */
public interface ProblemManagementService {

    /**
     * 保存问题管理
     * @param problemManagement 问题管理对象
     * @return 保存后的对象
     */
    ProblemManagementDO save(ProblemManagementDO problemManagement);

    /**
     * 根据ID查询
     * @param id 问题ID
     * @return 问题管理对象
     */
    Optional<ProblemManagementDO> findById(Long id);

    /**
     * 根据ID和删除状态查询
     * @param id 问题ID
     * @param deleted 删除状态
     * @return 问题管理对象
     */
    Optional<ProblemManagementDO> findByIdAndDeleted(Long id, Integer deleted);

    /**
     * 分页查询
     * @param specification 查询条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ProblemManagementDO> findAll(Specification<ProblemManagementDO> specification, Pageable pageable);

    /**
     * 逻辑删除
     * @param id 问题ID
     */
    void deleteById(Long id);
}
