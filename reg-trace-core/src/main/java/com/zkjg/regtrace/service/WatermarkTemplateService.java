package com.zkjg.regtrace.service;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkTemplateQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkTemplateRequest;
import com.zkjg.regtrace.persistence.vo.response.watermark.TemplateChangeHistoryVO;
import com.zkjg.regtrace.persistence.vo.response.watermark.WatermarkTemplateVO;

import java.util.List;

public interface WatermarkTemplateService {
    /**
     * 创建水印模板
     * @param userId 用户ID
     * @param reqVO 模板请求
     */
    void createTemplate(Integer userId, WatermarkTemplateRequest reqVO);
    
    /**
     * 更新水印模板
     * @param userId 用户ID
     * @param reqVO 模板请求
     * @return 更新后的模板
     */
    void updateTemplate(Integer userId, WatermarkTemplateRequest reqVO);
    
    /**
     * 查询水印模板列表
     * @param userId 用户ID
     * @param queryVO 查询条件
     * @return 模板列表
     */
    List<WatermarkTemplateVO> queryTemplates(Integer userId, WatermarkTemplateQueryRequest queryVO);
    
    /**
     * 获取水印模板详情
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 模板详情
     */
    WatermarkTemplateVO getTemplateDetail(Integer userId, Integer templateId);
    
    /**
     * 删除水印模板
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 是否删除成功
     */
    Boolean deleteTemplate(Integer userId, Integer templateId);
    
    /**
     * 分页获取模板变更记录（最近30天）
     * @param userId 用户ID
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量
     * @return 分页的变更记录
     */
    PageResult<TemplateChangeHistoryVO> getTemplateChangeHistory(Integer userId, int pageNum, int pageSize);

    
    /**
     * 回滚模板变更
     * @param userId 用户ID
     * @param historyId 历史记录ID
     * @return 回滚结果
     * @throws com.zkjg.regtrace.common.exceptions.BusinessException 当回滚失败时抛出
     */
    boolean rollbackTemplateChange(Integer userId, Long historyId) throws com.zkjg.regtrace.common.exceptions.BusinessException;
}
