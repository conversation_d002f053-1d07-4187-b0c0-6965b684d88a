package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.TicketAttachmentDO;
import com.zkjg.regtrace.persistence.repository.TicketAttachmentRepository;
import com.zkjg.regtrace.service.TicketAttachmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工单附件Service实现类
 * <AUTHOR>
 */
@Service
public class TicketAttachmentServiceImpl implements TicketAttachmentService {

    @Resource
    private TicketAttachmentRepository ticketAttachmentRepository;

    @Override
    public TicketAttachmentDO save(TicketAttachmentDO attachment) {
        return ticketAttachmentRepository.save(attachment);
    }

    @Override
    public List<TicketAttachmentDO> saveAll(List<TicketAttachmentDO> attachments) {
        return ticketAttachmentRepository.saveAll(attachments);
    }

    @Override
    public List<TicketAttachmentDO> findByCommunicationId(Long communicationId) {
        return ticketAttachmentRepository.findByCommunicationId(communicationId);
    }
}
