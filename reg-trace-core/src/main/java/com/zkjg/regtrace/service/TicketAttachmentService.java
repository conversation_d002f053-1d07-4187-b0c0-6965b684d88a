package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.TicketAttachmentDO;

import java.util.List;

/**
 * 工单附件Service
 * <AUTHOR>
 */
public interface TicketAttachmentService {

    /**
     * 保存附件
     */
    TicketAttachmentDO save(TicketAttachmentDO attachment);

    /**
     * 批量保存附件
     */
    List<TicketAttachmentDO> saveAll(List<TicketAttachmentDO> attachments);

    /**
     * 根据交流记录ID查询附件
     */
    List<TicketAttachmentDO> findByCommunicationId(Long communicationId);
}
