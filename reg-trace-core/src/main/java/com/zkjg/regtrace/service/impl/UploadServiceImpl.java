package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.config.MinioConfig;
import com.zkjg.regtrace.common.utils.MinioUtils;
import com.zkjg.regtrace.service.UploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Service
@Slf4j
public class UploadServiceImpl implements UploadService {

    @Resource
    private MinioUtils minioUtils;

    @Resource
    private MinioConfig minioConfig;

    @Override
    public String upload(MultipartFile file) {
        return minioUtils.uploadFile(file).replace(minioConfig.getReadUrl(),"");
    }
}
