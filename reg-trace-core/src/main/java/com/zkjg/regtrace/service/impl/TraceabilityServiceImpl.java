package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.dto.jpa.QueryTraceabilityJpaSpec;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.entity.TraceabilityDO;
import com.zkjg.regtrace.persistence.repository.TraceabilityRepository;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.TraceRecordRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityAdminQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRecordRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.TraceaRecordVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityAdminQueryResponse;
import com.zkjg.regtrace.service.TraceabilityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zkjg.regtrace.common.constants.PagingQuerySortConstant.SORT_CREATE_TIME;

@Service
@Slf4j
public class TraceabilityServiceImpl implements TraceabilityService {

    @Resource
    private TraceabilityRepository traceabilityRepository;

    public static final Integer BATCH_SIZE = 500;

    @Override
    public TraceabilityDO saveTraceabilityDO(TraceabilityDO traceabilityDO) {
        return traceabilityRepository.save(traceabilityDO);
    }

    @Override
    public Page<TraceabilityRecordDto> pageQueryTraceabilityRecord(TraceabilityRecordRequest req) {
        Pageable pageable = PageRequest.of(req.getPage() - 1, req.getSize());
        return traceabilityRepository.pageQueryTraceabilityRecord(
                req.getUserId(),
                req.getTraceabilityNumber(),
                req.getTraceabilityType(),
                req.getTraceabilityStatus(),
                req.getRegistrationNumber(),
                req.getFileType(),
                req.getLocation(),
                req.getStartTime(),
                req.getEndTime(),
                pageable
        );
    }

    @Override
    public List<TraceabilityDO> traceabilityList(String registrationNumber) {
        return traceabilityRepository.findAllByRegistrationNumberAndDeleted(registrationNumber,0);
    }

    @Override
    public TraceabilityDO queryTraceability(Integer id) {
        return traceabilityRepository.findById(id).orElse(null);
    }

    @Override
    public TraceabilityDO queryTraceability(String traceabilityNumber) {
        return traceabilityRepository.findByTraceabilityNumberAndDeleted(traceabilityNumber, SqlConstant.UN_DELETED);
    }

    @Override
    public void batchSave(List<TraceabilityDO> traceabilityDOS) {
        for (int i = 0; i < traceabilityDOS.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, traceabilityDOS.size());
            List<TraceabilityDO> batchList = traceabilityDOS.subList(i, end);
            traceabilityRepository.saveAll(batchList);
            traceabilityRepository.flush();
        }
    }

    @Override
    public Long count(QueryTraceabilityJpaSpec jpaSpec) {
        Specification<TraceabilityDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        return traceabilityRepository.count(specification);
    }

    @Override
    public void batchUpdateTraceabilityLogStatus(List<Integer> ids, String status) {
        traceabilityRepository.updateRecordStatusByIds(ids, status);
    }

    @Override
    public PageResult<TraceabilityAdminQueryResponse> queryTraceabilityAdminList(TraceabilityAdminQueryRequest req) {
        Pageable pageable = PageRequest.of(req.getPageNum() - 1, req.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));

        Specification<TraceabilityDO> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (req.getStartTime() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), req.getStartTime()));
            }
            if (req.getEndTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), req.getEndTime()));
            }
            if (StringUtils.hasText(req.getTraceabilityNumber())) {
                predicates.add(cb.like(root.get("traceabilityNumber"), "%" + req.getTraceabilityNumber() + "%"));
            }
            if (StringUtils.hasText(req.getTraceabilityType())) {
                predicates.add(cb.equal(root.get("traceabilityType"), req.getTraceabilityType()));
            }
            if (req.getStatus() != null) {
                predicates.add(cb.equal(root.get("status"), req.getStatus()));
            }
            if (StringUtils.hasText(req.getRecordStatus())) {
                predicates.add(cb.equal(root.get("recordStatus"), req.getRecordStatus()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<TraceabilityDO> page = traceabilityRepository.findAll(spec, pageable);
        Page<TraceabilityAdminQueryResponse> responsePage = page.map(this::convertToAdminQueryResponse);
        PageResult<TraceabilityAdminQueryResponse> result = new PageResult<>(responsePage);
        result.setData(responsePage.getContent());
        return result;
    }

    @Override
    public Page<TraceaRecordVo> queryTraceRecords(TraceRecordRequest req) {
        //参数
        Integer userId = TokenService.getLoginUser().getUserId();
        if (Objects.nonNull(req.getFileName())) {
            req.setFileName("%" + req.getFileName() + "%");
        }
        Sort sort = Sort.by(Sort.Direction.DESC, SORT_CREATE_TIME);
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);
        return traceabilityRepository.queryTraceRecords(userId, req.getTraceabilityNumber(), req.getFileName(), req.getTraceabilityNumber(), req.getTraceabilityType(),
                req.getStartTime(), req.getEndTime(), pageRequest);
    }

    private TraceabilityAdminQueryResponse convertToAdminQueryResponse(TraceabilityDO traceabilityDO) {
        TraceabilityAdminQueryResponse response = new TraceabilityAdminQueryResponse();
        response.setId(traceabilityDO.getId());
        response.setCreateTime(traceabilityDO.getCreateTime());
        response.setTraceabilityNumber(traceabilityDO.getTraceabilityNumber());
        response.setCreator(traceabilityDO.getCreator());
        response.setTraceabilityType(traceabilityDO.getTraceabilityType());
        response.setStatus(traceabilityDO.getStatus());
        response.setRecordStatus(traceabilityDO.getRecordStatus());
        return response;
    }
}
