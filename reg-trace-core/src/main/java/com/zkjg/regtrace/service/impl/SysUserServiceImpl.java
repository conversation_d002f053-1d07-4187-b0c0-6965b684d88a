package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.constants.TicketConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.jpa.SysUserJpaSpec;
import com.zkjg.regtrace.persistence.repository.SysUserRepository;
import com.zkjg.regtrace.service.SysUserService;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SysUserServiceImpl implements SysUserService {

    @Resource
    private SysUserRepository sysUserRepository;


    @Override
    public SysUserDO saveUser(SysUserDO user) {
        return sysUserRepository.save(user);
    }

    @Override
    public SysUserDO findUserByEmail(String email) {
        return sysUserRepository.findByEmailAndDeleted(email, StatusEnum.NO.getCode());
    }

    @Override
    public SysUserDO findUserByUsername(String username) {
        return sysUserRepository.findByUsernameAndDeleted(username, StatusEnum.NO.getCode());
    }

    @Override
    public List<SysUserDO> findByRoleId(Integer roleId) {
        return sysUserRepository.findUserByRoleId(roleId, StatusEnum.NO.getCode());
    }

    @Override
    public Optional<SysUserDO> findUserById(Integer userId) {
        return sysUserRepository.findById(userId);
    }

    @Override
    public Page<SysUserDO> findAll(Specification<SysUserDO> specification, PageRequest pageRequest) {
        return sysUserRepository.findAll(specification, pageRequest);
    }

    @Override
    public List<SysUserDO> findAll(SysUserJpaSpec jpaSpec) {
        Specification<SysUserDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        return sysUserRepository.findAll(specification);
    }

    @Override
    public void deleteUser(Integer userId) {
        sysUserRepository.deleteById(userId);
    }

    @Override
    public Page<SysUserDO> findUserByRoleId(Integer roleId, String username, PageRequest pageRequest) {
        return sysUserRepository.findUserByRoleId(roleId, username, StatusEnum.NO.getCode(), pageRequest);
    }

    @Override
    public Page<SysUserDO> findUserWithoutRoleId(Integer roleId, String username, PageRequest pageRequest) {
        return sysUserRepository.findUserWithoutRoleId(roleId, username, StatusEnum.NO.getCode(), pageRequest);
    }

    @Override
    public List<SysUserDO> findByRoleName(String roleName) {
        return sysUserRepository.findUsersByRoleName(roleName, StatusEnum.NO.getCode());
    }

    @Override
    public List<SysUserDO> findAllCustomerServiceUsers() {
        return findByRoleName(TicketConstant.CUSTOMER_SERVICE_ROLE_NAME);
    }

    @Override
    public List<SysUserDO> findUsersByUserNameContaining(String userName) {
        return sysUserRepository.findByUsernameContainingAndDeleted(userName, StatusEnum.NO.getCode());
    }

    @Override
    public Map<Integer, SysUserDO> findUsersByIds(List<Integer> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        // 查询用户时过滤已删除的用户
        List<SysUserDO> users = sysUserRepository.findAllById(userIds).stream()
                .filter(user -> Objects.equals(StatusEnum.NO.getCode(),user.getDeleted()))
                .collect(Collectors.toList());
        return users.stream()
                .collect(Collectors.toMap(SysUserDO::getId, Function.identity()));
    }
}
