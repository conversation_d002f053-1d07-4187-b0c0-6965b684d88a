package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.DocumentDO;
import com.zkjg.regtrace.persistence.repository.DocumentRepository;
import com.zkjg.regtrace.service.DocumentService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:33
 */
@Service
public class DocumentServiceImpl implements DocumentService {

    @Resource
    private DocumentRepository documentRepository;

    @Override
    public List<DocumentDO> findAll(Specification<DocumentDO> specification) {
        return documentRepository.findAll(specification);
    }

    @Override
    public Page<DocumentDO> findAll(Specification<DocumentDO> specification, PageRequest pageRequest) {
        return documentRepository.findAll(specification, pageRequest);
    }

    @Override
    public DocumentDO findById(Integer id) {
        return documentRepository.findByIdAndDeleted(id, 0);
    }

    @Override
    public void update(DocumentDO documentDO) {
        documentRepository.save(documentDO);
    }

    @Override
    public void save(DocumentDO documentDO) {
        documentRepository.save(documentDO);
    }

    @Override
    public void saveAll(List<DocumentDO> documentDOs) {
        documentRepository.saveAll(documentDOs);
    }
}
