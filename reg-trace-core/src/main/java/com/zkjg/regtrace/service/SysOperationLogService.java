package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.SysOperationLogDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

public interface SysOperationLogService {
    void save(SysOperationLogDO sysOperationLogDO);

    Page<SysOperationLogDO> findAll(Specification<SysOperationLogDO> specification, PageRequest pageRequest);

    List<SysOperationLogDO> findAll(Specification<SysOperationLogDO> specification);
}
