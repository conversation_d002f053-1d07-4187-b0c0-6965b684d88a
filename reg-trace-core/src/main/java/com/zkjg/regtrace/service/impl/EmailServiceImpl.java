package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.EmailTemplateEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.service.EmailService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 10:01
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

    @Resource
    private JavaMailSender mailSender;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private Configuration freemarkerConfig;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${spring.profiles.active:prod}")
    private String activeProfile;

    @Override
    public void sendEmail(String to, EmailTemplateEnum emailTemplateEnum, Map<String, String> model) {
        String lastSendKy = String.format(RedisKeyConstant.EMAIL_SEND_HISTORY, to);
        if (redisUtil.hasKey(lastSendKy)) {
            throw new BusinessException("邮件频繁发送");
        }
        if (!activeProfile.equals("dev")) {
            MimeMessage message = mailSender.createMimeMessage();
            try {
                MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
                helper.setTo(to);
                helper.setSubject(emailTemplateEnum.getSubject());
                helper.setFrom(fromEmail);

                // 加载模板
                Template emailTemplate = freemarkerConfig.getTemplate(emailTemplateEnum.getTemplate());
                StringWriter writer = new StringWriter();
                emailTemplate.process(model, writer);
                helper.setText(writer.toString(), true);
            } catch (Exception e) {
                throw new RuntimeException("邮件模板渲染失败", e);
            }
            try {
                mailSender.send(message);
            } catch (MailException e) {
                log.error("邮件发送异常:" + e.getMessage(), e);
                throw new BusinessException("邮件发送异常,请检查邮箱是否正确");
            }
        }
        redisUtil.setEx(lastSendKy, LocalDateTime.now().toString(), 1, TimeUnit.MINUTES);
    }
}
