package com.zkjg.regtrace.service;


import com.zkjg.regtrace.common.enums.RegisterPermissionEnum;
import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;

public interface SysRoleService {
    void saveRole(SysRoleDO sysRoleDO);

    Page<SysRoleDO> findAll(Specification<SysRoleDO> specification, PageRequest pageRequest);

    Optional<SysRoleDO> findById(Integer id);

    Optional<SysRoleDO> findByRoleName(String roleName);

    boolean findRoleNameUnique(String roleName, Integer roleId);

    List<SysRoleDO> findByUserId(Integer userId);

    List<Integer> findRoleIdByRoleName(List<RegisterPermissionEnum> roleName);

    void deleteRoles(List<Integer> roles);

    List<SysRoleDO> findAll();
}
