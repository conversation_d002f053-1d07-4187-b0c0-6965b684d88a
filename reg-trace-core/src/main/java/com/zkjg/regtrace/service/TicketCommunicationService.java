package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.TicketCommunicationDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单交流记录Service
 * <AUTHOR>
 */
public interface TicketCommunicationService {

    /**
     * 保存交流记录
     */
    TicketCommunicationDO save(TicketCommunicationDO communication);

    /**
     * 根据工单ID查询交流记录
     */
    List<TicketCommunicationDO> findByTicketId(Long ticketId);

    /**
     *
     * @param operatorId 操作者
     * @param type 类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数量
     */
    List<Long> findTicketIdsByOperatorAndTypeAndTimeRange(Integer operatorId, Integer type, LocalDateTime startTime, LocalDateTime endTime);
}
