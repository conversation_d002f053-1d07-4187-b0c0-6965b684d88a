package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.SysRolePermissionDO;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo;

import java.util.List;

public interface SysRolePermissionService {
    void saveRolePermission(List<SysRolePermissionDO> list);

    List<PermissionDetailVo> findByRoleId(Integer roleId);

    void deleteByRoleId(Integer roleId);

    void deleteByRoleIds(List<Integer> roleIds);

    void flush();
}
