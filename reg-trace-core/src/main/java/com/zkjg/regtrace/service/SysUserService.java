package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.jpa.SysUserJpaSpec;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SysUserService {

    SysUserDO saveUser(SysUserDO user);

    SysUserDO findUserByEmail(String email);

    SysUserDO findUserByUsername(String username);

    List<SysUserDO> findByRoleId(Integer roleId);

    Optional<SysUserDO> findUserById(Integer userId);

    Page<SysUserDO> findAll(Specification<SysUserDO> specification, PageRequest pageRequest);

    List<SysUserDO> findAll(SysUserJpaSpec jpaSpec);

    void deleteUser(Integer userId);

    Page<SysUserDO> findUserByRoleId(Integer roleId,String username,PageRequest pageRequest);

    Page<SysUserDO> findUserWithoutRoleId(Integer roleId, String username, PageRequest pageRequest);

    /**
     * 根据角色名称查询用户列表
     * @param roleName 角色名称
     * @return 用户列表
     */
    List<SysUserDO> findByRoleName(String roleName);

    /**
     * 获取所有客服用户
     * @return 客服用户列表
     */
    List<SysUserDO> findAllCustomerServiceUsers();

    /**
     * 根据真实姓名模糊查询用户
     * @param personName 用户名（支持部分匹配）
     * @return 匹配的用户列表
     */
    List<SysUserDO> findUsersByUserNameContaining(String personName);

    /**
     * 批量查询用户信息
     * @param userIds 用户ID列表
     * @return 用户ID到用户信息的映射
     */
    Map<Integer, SysUserDO> findUsersByIds(List<Integer> userIds);
}
