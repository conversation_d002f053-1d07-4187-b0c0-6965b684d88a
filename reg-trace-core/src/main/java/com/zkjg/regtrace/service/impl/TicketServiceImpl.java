package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.enums.CommunicationTypeEnum;
import com.zkjg.regtrace.common.enums.TicketStatusEnum;
import com.zkjg.regtrace.persistence.entity.TicketDO;
import com.zkjg.regtrace.persistence.repository.TicketRepository;
import com.zkjg.regtrace.service.TicketService;
import com.zkjg.regtrace.service.TicketCommunicationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 工单Service实现类
 * <AUTHOR>
 */
@Service
public class TicketServiceImpl implements TicketService {

    @Resource
    private TicketRepository ticketRepository;

    @Resource
    private TicketCommunicationService ticketCommunicationService;

    @Override
    public TicketDO save(TicketDO ticket) {
        return ticketRepository.save(ticket);
    }

    @Override
    public Optional<TicketDO> findById(Long id) {
        return ticketRepository.findById(id);
    }

    @Override
    public Page<TicketDO> findAll(Specification<TicketDO> specification, Pageable pageable) {
        return ticketRepository.findAll(specification, pageable);
    }

    @Override
    public int countWorkload(Integer assignedToId, List<Integer> statusList) {
        return ticketRepository.countByAssignedToIdAndStatusIn(assignedToId, statusList);
    }

    @Override
    public List<TicketDO> findTimeoutTickets(LocalDateTime timeThreshold) {
        return ticketRepository.findPendingConfirmationTicketsExceedingTime(timeThreshold);
    }

    @Override
    public List<TicketDO> findUserTimeoutTickets(LocalDateTime timeThreshold) {
        return ticketRepository.findUserPendingConfirmationTicketsExceedingTime(timeThreshold);
    }

    @Override
    public List<TicketDO> saveAll(List<TicketDO> tickets) {
        return ticketRepository.saveAll(tickets);
    }

    @Override
    public long countConfirmedTickets(Integer assignedToId, LocalDateTime startTime, LocalDateTime endTime) {
        List<Integer> statusList = Arrays.asList(TicketStatusEnum.IN_PROGRESS.getCode(), TicketStatusEnum.PENDING_USER_CONFIRMATION.getCode());
        return ticketRepository.countConfirmedTickets(assignedToId, statusList, startTime, endTime);
    }

    @Override
    public long countRejectedTickets(Integer assignedToId, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取客服拒绝的工单ID列表（type=3表示客服拒绝）
        List<Long> rejectedTicketIds = ticketCommunicationService.findTicketIdsByOperatorAndTypeAndTimeRange(
                assignedToId, CommunicationTypeEnum.CS_REJECT.getCode(), startTime, endTime);

        // 如果没有拒绝的工单，返回0
        if (rejectedTicketIds.isEmpty()) {
            return 0;
        }

        // 统计工单数量
        return ticketRepository.countByIdIn(rejectedTicketIds);
    }

    @Override
    public long countCompletedTickets(Integer assignedToId, LocalDateTime startTime, LocalDateTime endTime) {
        return ticketRepository.countCompletedTickets(assignedToId, startTime, endTime);
    }

    @Override
    public List<Object[]> countTicketsByCategory(LocalDateTime startTime, LocalDateTime endTime) {
        return ticketRepository.countTicketsByCategory(startTime, endTime);
    }
}
