package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

public interface WorkRegistrationService {

    /**
     * 保存作品登记信息
     * @param workRegistrationDO 作品登记参数
     * @return 作品登记信息
     */
    WorkRegistrationDO save(WorkRegistrationDO workRegistrationDO);

    /**
     * 根据作品名称精确查询且按删除状态过滤
     * @param workName 作品名称
     * @param deleted 是否删除
     * @return 作品登记信息列表
     */
    List<WorkRegistrationDO> findByWorkNameAndDeleted(String workName, Integer deleted);

    List<WorkRegistrationDO> findAll(Specification<WorkRegistrationDO> specification);

    Page<WorkRegistrationDO> findAll(Specification<WorkRegistrationDO> applicationDOSpecification, PageRequest pageRequest);

    WorkRegistrationDO findById(Integer id);

    void update(WorkRegistrationDO workRegistrationDO);

    void updateBatch(List<WorkRegistrationDO> all);

    WorkRegistrationDO findOne(Specification<WorkRegistrationDO> specification);
}
