package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.TicketCommunicationDO;
import com.zkjg.regtrace.persistence.repository.TicketCommunicationRepository;
import com.zkjg.regtrace.service.TicketCommunicationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单交流记录Service实现类
 * <AUTHOR>
 */
@Service
public class TicketCommunicationServiceImpl implements TicketCommunicationService {

    @Resource
    private TicketCommunicationRepository ticketCommunicationRepository;

    @Override
    public TicketCommunicationDO save(TicketCommunicationDO communication) {
        return ticketCommunicationRepository.save(communication);
    }

    @Override
    public List<TicketCommunicationDO> findByTicketId(Long ticketId) {
        return ticketCommunicationRepository.findByTicketIdOrderByCreateTimeAsc(ticketId);
    }

    @Override
    public List<Long> findTicketIdsByOperatorAndTypeAndTimeRange(Integer operatorId, Integer type, LocalDateTime startTime, LocalDateTime endTime) {
        return ticketCommunicationRepository.findTicketIdsByOperatorAndTypeAndTimeRange(operatorId, type, startTime, endTime);
    }
}
