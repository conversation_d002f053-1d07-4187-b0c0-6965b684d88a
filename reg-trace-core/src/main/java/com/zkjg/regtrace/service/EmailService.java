package com.zkjg.regtrace.service;

import com.zkjg.regtrace.common.enums.EmailTemplateEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 10:00
 */
public interface EmailService {

    /**
     * 发送邮件
     *
     * @param to            收件者
     * @param emailTemplateEnum 模板枚举
     * @param model         数据
     */
    void sendEmail(String to, EmailTemplateEnum emailTemplateEnum, Map<String, String> model);
}
