package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.DocumentDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:33
 */
public interface DocumentService {
    Page<DocumentDO> findAll(Specification<DocumentDO> specification, PageRequest pageRequest);

    List<DocumentDO> findAll(Specification<DocumentDO> specification);

    DocumentDO findById(Integer id);

    void update(DocumentDO documentDO);

    void save(DocumentDO documentDO);

    void saveAll(List<DocumentDO> documentDOs);
}
