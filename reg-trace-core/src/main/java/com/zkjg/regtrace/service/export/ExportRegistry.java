package com.zkjg.regtrace.service.export;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出定义注册管理器
 * 负责注册和管理所有导出定义
 */
@Component
@Slf4j
public class ExportRegistry {

    /**
     * 存储所有已注册的导出定义
     */
    private final Map<String, ExportDefinition> exportDefinitions = new HashMap<>();

    /**
     * 注册一个导出定义
     * @param definition 导出定义
     */
    public void registerExportDefinition(ExportDefinition definition) {
        if (exportDefinitions.containsKey(definition.getExportId())) {
            log.warn("导出定义ID: {} 已存在，将被覆盖", definition.getExportId());
        }
        exportDefinitions.put(definition.getExportId(), definition);
        log.info("已注册导出定义: {}", definition.getExportId());
    }

    /**
     * 检查指定的导出定义是否存在
     * @param exportId 导出ID
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasExportDefinition(String exportId) {
        return exportDefinitions.containsKey(exportId);
    }

    /**
     * 获取指定的导出定义
     * @param exportId 导出ID
     * @return 导出定义，如果不存在返回null
     */
    public ExportDefinition getExportDefinition(String exportId) {
        return exportDefinitions.get(exportId);
    }

    /**
     * 获取已注册的导出定义数量
     * @return 导出定义数量
     */
    public int getExportDefinitionCount() {
        return exportDefinitions.size();
    }

    /**
     * 清空所有导出定义
     */
    public void clearAllExportDefinitions() {
        exportDefinitions.clear();
        log.info("已清空所有导出定义");
    }
}

