package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.TraceMetricsDO;
import com.zkjg.regtrace.persistence.repository.TraceMetricsRepository;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;
import com.zkjg.regtrace.service.TraceMetricsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class TraceMetricsServiceImpl implements TraceMetricsService {

    @Resource
    private TraceMetricsRepository traceStatisticsRepository;

    @Override
    public StatsVo traceStatistics(Integer days) {
        if (Objects.isNull(days)) {
            days = 7;
        }
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days - 1).with(LocalTime.MIN);
        // 格式化为 YYYYMM
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        String formattedStartTime = startTime.format(formatter);
        String formattedEndTime = endTime.format(formatter);
        List<Map<String, Object>> monthlyStatsForMonth = traceStatisticsRepository.findMonthlyStatsForMonth(null, formattedStartTime, formattedEndTime);
        return formatMonthlyStats(monthlyStatsForMonth);
    }

    @Override
    public void batchSave(List<TraceMetricsDO> traceMetricsDOS) {
        traceStatisticsRepository.saveAll(traceMetricsDOS);
    }


    private static StatsVo formatMonthlyStats(List<Map<String, Object>> results) {
        StatsVo dto = new StatsVo();
        List<String> labels = new ArrayList<>();
        List<Long> values = new ArrayList<>();
        for (int i = 1; i <= 31; i++) {
            labels.add(String.valueOf(i));
            values.add(0L);
        }
        for (Map<String, Object> result : results) {
            int day = (int) result.get("day");
            long count = (long) result.get("count");
            values.set(day - 1, count);
        }
        dto.setLabels(labels);
        dto.setValues(values);
        return dto;
    }
}
