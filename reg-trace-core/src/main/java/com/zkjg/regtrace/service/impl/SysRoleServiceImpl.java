package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.enums.RegisterPermissionEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import com.zkjg.regtrace.persistence.jpa.RoleJpaSpec;
import com.zkjg.regtrace.persistence.repository.SysRoleRepository;
import com.zkjg.regtrace.service.SysRoleService;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class SysRoleServiceImpl implements SysRoleService {

    @Resource
    private SysRoleRepository sysRoleRepository;

    @Override
    public void saveRole(SysRoleDO sysRoleDO) {
        sysRoleRepository.save(sysRoleDO);
    }

    @Override
    public Page<SysRoleDO> findAll(Specification<SysRoleDO> specification, PageRequest pageRequest) {
        return sysRoleRepository.findAll(specification, pageRequest);
    }

    @Override
    public Optional<SysRoleDO> findById(Integer id) {
        return sysRoleRepository.findById(id);
    }

    @Override
    public Optional<SysRoleDO> findByRoleName(String roleName) {
        return sysRoleRepository.findByRoleName(roleName);
    }

    @Override
    public boolean findRoleNameUnique(String roleName, Integer roleId) {
        RoleJpaSpec jpaSpec = RoleJpaSpec.builder().deletedEq(StatusEnum.NO.getCode()).roleNameEq(roleName).build();
        if (Objects.nonNull(roleId)) {
            jpaSpec.setIdNotEq(roleId);
        }
        Specification<SysRoleDO> spec = QueryConvertUtils.toSpecification(jpaSpec);
        return sysRoleRepository.findOne(spec).isPresent();
    }

    @Override
    public List<SysRoleDO> findByUserId(Integer userId) {
        return sysRoleRepository.findRoleByUserId(userId);
    }

    @Override
    public List<Integer> findRoleIdByRoleName(List<RegisterPermissionEnum> roleName) {
        List<Integer> list = new ArrayList<>();
        roleName.forEach(e -> {
            Optional<SysRoleDO> byRoleName = sysRoleRepository.findByRoleName(e.getType());
            byRoleName.ifPresent(sysRoleDO -> list.add(sysRoleDO.getId()));
        });
        return list;
    }

    @Override
    public void deleteRoles(List<Integer> roles) {
        sysRoleRepository.deleteAllById(roles);
    }

    @Override
    public List<SysRoleDO> findAll() {
        return sysRoleRepository.findAllByRoleStatusAndDeleted(StatusEnum.YES.getCode(), StatusEnum.NO.getCode());
    }
}
