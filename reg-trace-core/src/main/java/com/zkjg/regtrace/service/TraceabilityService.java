package com.zkjg.regtrace.service;


import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.dto.jpa.QueryTraceabilityJpaSpec;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.entity.TraceabilityDO;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.TraceRecordRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityAdminQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRecordRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.TraceaRecordVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityAdminQueryResponse;
import org.springframework.data.domain.Page;

import java.util.List;

public interface TraceabilityService {
    TraceabilityDO saveTraceabilityDO(TraceabilityDO traceabilityDO);

    Page<TraceabilityRecordDto> pageQueryTraceabilityRecord(TraceabilityRecordRequest req);

    List<TraceabilityDO> traceabilityList(String registrationNumber);

    TraceabilityDO queryTraceability(Integer id);

    TraceabilityDO queryTraceability(String traceabilityNumber);

    void batchSave(List<TraceabilityDO> traceabilityDOS);

    Long count(QueryTraceabilityJpaSpec jpaSpec);

    void batchUpdateTraceabilityLogStatus(List<Integer> ids, String status);

    PageResult<TraceabilityAdminQueryResponse> queryTraceabilityAdminList(TraceabilityAdminQueryRequest req);

    Page<TraceaRecordVo> queryTraceRecords(TraceRecordRequest req);
}
