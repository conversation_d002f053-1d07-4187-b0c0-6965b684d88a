package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.DocumentFileDO;
import com.zkjg.regtrace.persistence.repository.DocumentFileRepository;
import com.zkjg.regtrace.service.DocumentFileService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/17 9:02
 */
@Service
public class DocumentFileServiceImpl implements DocumentFileService {

    @Resource
    private DocumentFileRepository documentFileRepository;

    @Override
    public List<DocumentFileDO> findAll(Specification<DocumentFileDO> specification) {
        return documentFileRepository.findAll(specification);
    }

    @Override
    public List<DocumentFileDO> findByDocumentId(Integer documentId) {
        return documentFileRepository.findByDocumentId(documentId);
    }

    @Override
    public List<DocumentFileDO> findByDocumentIds(List<Integer> documentIds) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return new ArrayList<>();
        }
        return documentFileRepository.findByDocumentIdInAndDeleted(documentIds, 0);
    }

    @Override
    public void save(DocumentFileDO documentFileDO) {
        documentFileRepository.save(documentFileDO);
    }
}
