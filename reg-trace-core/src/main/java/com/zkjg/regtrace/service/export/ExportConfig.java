package com.zkjg.regtrace.service.export;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.enums.FileTypeEnum;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import com.zkjg.regtrace.manager.WorkRegistrationApplicationManager;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import com.zkjg.regtrace.service.WorkRegistrationApplicationService;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 导出定义配置类
 * 用于注册所有导出定义
 */
@Component
@Slf4j
public class ExportConfig {

    @Resource
    private ExportRegistry exportRegistry;

    @Resource
    private WorkRegistrationApplicationManager workRegistrationApplicationManager;

    /**
     * 系统启动时注册所有导出定义
     */
    @PostConstruct
    public void init() {
        // 先注册状态转换器
        registerStatusConverters();

        //注册音视频登记申请导出定义
        registerWorkRegistrationApplicationExport();
        //注册音视频登记审核导出定义
        registerWorkRegistrationApplicationAuditExport();
        log.info("已注册 {} 个导出定义", exportRegistry.getExportDefinitionCount());
    }

    private void registerWorkRegistrationApplicationAuditExport() {
        ExportDefinition workRegistrationExport = ExportDefinition.builder()
                .exportId("work-registration-application-audit")
                .exportName("作品登记审核")
                .targetClass(WorkRegistrationApplicationDetailVo.class)
                .dataExtractor(params -> {
                    try {
                        // 构建查询条件
                        QueryWorkRegistrationApplicationRequest request = JSON.parseObject(JSON.toJSONString(params), QueryWorkRegistrationApplicationRequest.class);
                        log.info("作品登记审核导出查询参数: {}", request);

                        // 调用服务查询数据
                        Result<PageResult<WorkRegistrationApplicationDetailVo>> result = workRegistrationApplicationManager.listAudit(request);

                        if (result.getData() != null && result.getData().getData() != null) {
                            List<WorkRegistrationApplicationDetailVo> data = result.getData().getData();
                            log.info("查询到数据条数: {}", data.size());
                            return data;
                        } else {
                            log.warn("查询结果为空或数据为null");
                            return Collections.emptyList();
                        }
                    } catch (Exception e) {
                        log.error("数据查询异常", e);
                        return Collections.emptyList();
                    }
                })
                .build();
        exportRegistry.registerExportDefinition(workRegistrationExport);
    }

    /**
     * 注册状态转换器
     */
    private void registerStatusConverters() {
        // 注册审核状态转换器
        Map<Object, String> auditStatusMap =
                WorkRegistrationAuditStatusEnum.toMap();
        AdvancedExportUtil.registerStatusConverter("auditStatusConverter", auditStatusMap);

        // 注册文件类型转换器
        Map<Object, String> fileTypeMap = FileTypeEnum.toMap();
        AdvancedExportUtil.registerStatusConverter("fileTypeConverter", fileTypeMap);

        // 注册其他状态转换器
        Map<Object, String> statusMap = new HashMap<>();
        statusMap.put(0, "禁用");
        statusMap.put(1, "启用");
        AdvancedExportUtil.registerStatusConverter("statusConverter", statusMap);

        // 注册文件大小转换器
        AdvancedExportUtil.registerConverter("fileSizeConverter", value -> {
            if (value == null) return "";

            long bytes;
            try {
                if (value instanceof Long) {
                    bytes = (Long) value;
                } else if (value instanceof Integer) {
                    bytes = ((Integer) value).longValue();
                } else if (value instanceof String) {
                    bytes = Long.parseLong((String) value);
                } else {
                    return value.toString();
                }
            } catch (NumberFormatException e) {
                return value.toString();
            }

            if (bytes < 0) return "0 B";

            String[] units = {"B", "KB", "MB", "GB", "TB"};
            int unitIndex = 0;
            double size = bytes;

            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }

            if (unitIndex == 0) {
                return String.format("%.0f %s", size, units[unitIndex]);
            } else {
                return String.format("%.2f %s", size, units[unitIndex]);
            }
        });

        log.info("已注册状态转换器");
    }

    /**
     * 注册作品登记申请导出
     */
    private void registerWorkRegistrationApplicationExport() {
        ExportDefinition workRegistrationExport = ExportDefinition.builder()
                .exportId("work-registration-application")
                .exportName("作品登记申请")
                .targetClass(WorkRegistrationApplicationDetailVo.class)
                .dataExtractor(params -> {
                    try {
                        // 构建查询条件
                        QueryWorkRegistrationApplicationRequest request = JSON.parseObject(JSON.toJSONString(params), QueryWorkRegistrationApplicationRequest.class);
                        log.info("作品登记申请导出查询参数: {}", request);

                        // 调用服务查询数据
                        Result<PageResult<WorkRegistrationApplicationDetailVo>> result = workRegistrationApplicationManager.list(request);

                        if (result.getData() != null && result.getData().getData() != null) {
                            List<WorkRegistrationApplicationDetailVo> data = result.getData().getData();
                            log.info("查询到数据条数: {}", data.size());
                            return data;
                        } else {
                            log.warn("查询结果为空或数据为null");
                            return Collections.emptyList();
                        }
                    } catch (Exception e) {
                        log.error("数据查询异常", e);
                        return Collections.emptyList();
                    }
                })
                .build();
        exportRegistry.registerExportDefinition(workRegistrationExport);
    }
}

