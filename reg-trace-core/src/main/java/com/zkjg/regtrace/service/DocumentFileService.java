package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.DocumentFileDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:33
 */
public interface DocumentFileService {
    List<DocumentFileDO> findAll(Specification<DocumentFileDO> specification);
    List<DocumentFileDO> findByDocumentId(Integer documentId);
    List<DocumentFileDO> findByDocumentIds(List<Integer> documentId);
    void save(DocumentFileDO documentFileDO);
}
