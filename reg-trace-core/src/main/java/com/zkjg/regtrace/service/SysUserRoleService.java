package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.entity.SysUserRoleDO;

import java.util.List;

public interface SysUserRoleService {
    void deleteUserPermission(Integer userId);

    void saveUserPermission(Integer userId, List<Integer> permissions, String operate);

    void deleteByUserId(Integer userId);

    void deleteByRoles(List<Integer> roles);

    void deleteByUserAndRole(Integer roleId, List<Integer> userIds);

    void saveAll(List<SysUserRoleDO> list);

    void flush();

    List<SysUserRoleDO> findByRoleId(Integer roleId);
}
