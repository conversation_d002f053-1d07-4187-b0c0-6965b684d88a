package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.WorkRegistrationRecordDO;
import com.zkjg.regtrace.persistence.repository.WorkRegistrationRecordRepository;
import com.zkjg.regtrace.service.WorkRegistrationRecordService;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WorkRegistrationRecordServiceImpl implements WorkRegistrationRecordService {

    @Resource
    private WorkRegistrationRecordRepository workRegistrationRecordRepository;

    /**
     * 保存记录
     * @param record 记录数据
     */
    @Override
    public void save(WorkRegistrationRecordDO record) {
        workRegistrationRecordRepository.save(record);
    }


    @Override
    public void batchSave(List<WorkRegistrationRecordDO> recordList) {
        workRegistrationRecordRepository.saveAll(recordList);
    }

    @Override
    public List<WorkRegistrationRecordDO> listByApplicationId(Integer id) {
        return workRegistrationRecordRepository.findByApplicationId(id);
    }
}
