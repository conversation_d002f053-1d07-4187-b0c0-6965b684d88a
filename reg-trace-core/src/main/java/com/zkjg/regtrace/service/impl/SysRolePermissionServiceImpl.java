package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.persistence.entity.SysRolePermissionDO;
import com.zkjg.regtrace.persistence.repository.SysRolePermissionRepository;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo;
import com.zkjg.regtrace.service.SysRolePermissionService;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysRolePermissionServiceImpl implements SysRolePermissionService {

    @Resource
    private SysRolePermissionRepository sysRolePermissionRepository;

    @Override
    public void saveRolePermission(List<SysRolePermissionDO> list) {
        sysRolePermissionRepository.saveAll(list);
    }

    @Override
    public List<PermissionDetailVo> findByRoleId(Integer roleId) {
        return sysRolePermissionRepository.selectRolePermissionListByRoleId(roleId, StatusEnum.NO.getCode());
    }

    @Override
    public void deleteByRoleId(Integer roleId) {
        sysRolePermissionRepository.deleteByRoleId(roleId);
    }

    @Override
    public void deleteByRoleIds(List<Integer> roleIds) {
        sysRolePermissionRepository.deleteAllByRoleIdIn(roleIds);
    }

    @Override
    public void flush() {
        sysRolePermissionRepository.flush();
    }
}
