package com.zkjg.regtrace.service;

import com.zkjg.regtrace.persistence.entity.TicketDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 工单Service
 * <AUTHOR>
 */
public interface TicketService {

    /**
     * 保存工单
     */
    TicketDO save(TicketDO ticket);

    /**
     * 根据ID查询工单
     */
    Optional<TicketDO> findById(Long id);

    /**
     * 分页查询工单
     */
    Page<TicketDO> findAll(Specification<TicketDO> specification, Pageable pageable);


    /**
     * 计算客服工作量
     */
    int countWorkload(Integer assignedToId, List<Integer> statusList);

    /**
     * 查询超时未确认的工单
     */
    List<TicketDO> findTimeoutTickets(LocalDateTime timeThreshold);

    /**
     * 查询用户超时未确认的工单
     */
    List<TicketDO> findUserTimeoutTickets(LocalDateTime timeThreshold);

    /**
     * 批量保存工单
     */
    List<TicketDO> saveAll(List<TicketDO> tickets);

    /**
     * 统计客服确认接收的工单数
     */
    long countConfirmedTickets(Integer assignedToId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计客服拒绝接收的工单数
     */
    long countRejectedTickets(Integer assignedToId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计客服完成的工单数
     */
    long countCompletedTickets(Integer assignedToId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 按分类统计工单数量
     */
    List<Object[]> countTicketsByCategory(LocalDateTime startTime, LocalDateTime endTime);
}
