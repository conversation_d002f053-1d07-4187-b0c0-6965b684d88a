package com.zkjg.regtrace.service.export;

import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 导出定义类，用于描述一个导出配置
 */
@Data
@Builder
public class ExportDefinition {
    /**
     * 导出ID，唯一标识一种导出类型
     */
    private String exportId;

    /**
     * 导出名称（用于文件名）
     */
    private String exportName;

    /**
     * 数据提取器，用于从数据源获取要导出的数据
     */
    private Function<Map<String, Object>, List<?>> dataExtractor;

    /**
     * 目标类，用于基于注解的导出
     */
    private Class<?> targetClass;

}

