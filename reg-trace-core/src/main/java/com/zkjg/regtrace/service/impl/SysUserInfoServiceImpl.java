package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.SysUserInfoDO;
import com.zkjg.regtrace.persistence.repository.SysUserInfoRepository;
import com.zkjg.regtrace.service.SysUserInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SysUserInfoServiceImpl implements SysUserInfoService {

    @Resource
    private SysUserInfoRepository sysUserInfoRepository;


    @Override
    public void saveUserInfo(SysUserInfoDO userInfo) {
        sysUserInfoRepository.save(userInfo);
    }

    @Override
    public SysUserInfoDO findByUserId(Integer userId) {
        return sysUserInfoRepository.findByUserId(userId);
    }
}
