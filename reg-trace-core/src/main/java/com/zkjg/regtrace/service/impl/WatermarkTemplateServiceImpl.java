package com.zkjg.regtrace.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.entity.WatermarkTemplateDO;
import com.zkjg.regtrace.persistence.entity.WatermarkTemplateHistoryDO;
import com.zkjg.regtrace.persistence.repository.WatermarkTemplateHistoryRepository;
import com.zkjg.regtrace.persistence.repository.WatermarkTemplateRepository;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkTemplateQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkTemplateRequest;
import com.zkjg.regtrace.persistence.vo.response.watermark.TemplateChangeHistoryVO;
import com.zkjg.regtrace.persistence.vo.response.watermark.WatermarkTemplateVO;
import com.zkjg.regtrace.service.WatermarkTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WatermarkTemplateServiceImpl implements WatermarkTemplateService {

    @Resource
    private WatermarkTemplateRepository watermarkTemplateRepository;

    @Resource
    private WatermarkTemplateHistoryRepository watermarkTemplateHistoryRepository;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTemplate(Integer userId, WatermarkTemplateRequest request) {
        // 检查用户有效模板数量是否达到上限
        long validTemplateCount = watermarkTemplateRepository.countByUserIdAndDeleted(userId, 0);
        if (validTemplateCount >= 2) {
            throw new BusinessException("水印模板已达到上限，无法创建新模板");
        }

        // 检查模板名称是否已存在
        if (watermarkTemplateRepository.existsByUserIdAndTemplateNameAndDeleted(userId, request.getTemplateName(), 0)) {
            throw new BusinessException("模板名称已存在");
        }
        WatermarkTemplateDO templateDO = BeanUtil.copyProperties(request, WatermarkTemplateDO.class);
        templateDO.setUserId(userId);
        templateDO.setTemplateNo(generateTemplateNo());
        templateDO.setDeleted(0);
        templateDO.setCreateTime(LocalDateTime.now());
        templateDO.setModifyTime(LocalDateTime.now());
        watermarkTemplateRepository.save(templateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplate(Integer userId, WatermarkTemplateRequest request) {
        if (request.getId() == null) {
            throw new BusinessException("模板ID不能为空");
        }

        // 检查模板是否存在
        WatermarkTemplateDO templateDO = watermarkTemplateRepository.findByIdAndDeleted(request.getId(), 0);
        if (templateDO == null) {
            throw new BusinessException("模板不存在");
        }

        // 检查是否为模板所有者
        if (!userId.equals(templateDO.getUserId())) {
            throw new BusinessException("无权修改此模板");
        }

        // 如果修改了模板名称，检查新名称是否已存在
        if (!request.getTemplateName().equals(templateDO.getTemplateName()) &&
            watermarkTemplateRepository.existsByUserIdAndTemplateNameAndDeleted(userId, request.getTemplateName(), 0)) {
            throw new BusinessException("模板名称已存在");
        }

        // 保存更新前的模板快照
        String templateSnapshot = JSONUtil.toJsonStr(templateDO);
        // 更新基本信息
        templateDO.setTemplateName(request.getTemplateName());
        templateDO.setWatermarkFileType(request.getWatermarkFileType());
        templateDO.setFontStyle(request.getFontStyle());
        templateDO.setColorSpace(request.getColorSpace());
        templateDO.setLocation(request.getLocation());
        templateDO.setOffsetX(request.getOffsetX());
        templateDO.setOffsetXUnit(request.getOffsetXUnit());
        templateDO.setOffsetY(request.getOffsetY());
        templateDO.setOffsetYUnit(request.getOffsetYUnit());
        templateDO.setRotation(request.getRotation());
        templateDO.setOpacity(request.getOpacity());
        templateDO.setWidth(request.getWidth());
        templateDO.setStartTime(request.getStartTime());
        templateDO.setLoopCount(request.getLoopCount());
        templateDO.setDuration(request.getDuration());
        templateDO.setLoopInterval(request.getLoopInterval());
        templateDO.setModifier(userId.toString());
        templateDO.setModifyTime(LocalDateTime.now());

        watermarkTemplateRepository.save(templateDO);

        // 记录变更操作历史，保存删除前的完整状态
        recordTemplateHistory(
                userId,
                templateDO.getTemplateNo(),
                templateDO.getTemplateName(),
                1, // 更新操作
                "更新模板: " + templateDO.getTemplateName(),
                templateSnapshot // 保存删除前的完整状态
        );
    }

    @Override
    public List<WatermarkTemplateVO> queryTemplates(Integer userId, WatermarkTemplateQueryRequest queryVO) {
        // 使用Specification构建复杂查询条件
        Specification<WatermarkTemplateDO> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 添加未删除条件
            predicates.add(cb.equal(root.get("deleted"), 0));
            // 添加用户ID条件
            predicates.add(cb.equal(root.get("userId"), userId));
            // 按模板名称模糊查询
            if (StringUtils.isNotBlank(queryVO.getTemplateName())) {
                predicates.add(cb.like(root.get("templateName"), "%" + queryVO.getTemplateName() + "%"));
            }
            // 按水印文件类型查询
            if (queryVO.getWatermarkFileType() != null) {
                predicates.add(cb.equal(root.get("watermarkFileType"), queryVO.getWatermarkFileType()));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        
        List<WatermarkTemplateDO> templateDOList = watermarkTemplateRepository.findAll(spec);
        
        // 转换为VO列表返回
        return templateDOList.stream()
                .map(WatermarkTemplateVO::fromDO)
                .collect(Collectors.toList());
    }

    @Override
    public WatermarkTemplateVO getTemplateDetail(Integer userId, Integer templateId) {
        // 查询模板是否存在
        Optional<WatermarkTemplateDO> templateOpt = watermarkTemplateRepository.findById(templateId);
        if (!templateOpt.isPresent() || templateOpt.get().getDeleted() == 1) {
            throw new BusinessException("模板不存在");
        }

        WatermarkTemplateDO templateDO = templateOpt.get();

        // 验证是否是当前用户的模板
        if (!templateDO.getUserId().equals(userId)) {
            throw new BusinessException("无权限查看此模板");
        }

        // 转换为VO返回
        return WatermarkTemplateVO.fromDO(templateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTemplate(Integer userId, Integer templateId) {
        // 查询模板是否存在
        Optional<WatermarkTemplateDO> templateOpt = watermarkTemplateRepository.findById(templateId);
        if (!templateOpt.isPresent() || templateOpt.get().getDeleted() == 1) {
            return true;
        }

        WatermarkTemplateDO templateDO = templateOpt.get();

        // 验证是否是当前用户的模板
        if (!templateDO.getUserId().equals(userId)) {
            throw new BusinessException("无权限删除此模板");
        }

        // 记录删除操作历史，保存删除前的完整状态
        recordTemplateHistory(
                userId,
                templateDO.getTemplateNo(),
                templateDO.getTemplateName(),
                2, // 删除操作
                "删除模板: " + templateDO.getTemplateName(),
                JSONUtil.toJsonStr(templateDO) // 保存删除前的完整状态
        );

        // 逻辑删除
        templateDO.setDeleted(1);
        watermarkTemplateRepository.save(templateDO);

        return true;
    }

    @Override
    public PageResult<TemplateChangeHistoryVO> getTemplateChangeHistory(Integer userId, int pageNum, int pageSize) {
        // 计算30天前的时间
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);

        // 创建分页请求，页码从0开始
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);

        // 创建查询条件
        Specification<WatermarkTemplateHistoryDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("operatorId"), userId));
            predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("operationTime"), thirtyDaysAgo));
            query.orderBy(criteriaBuilder.desc(root.get("operationTime")));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // 分页查询
        Page<WatermarkTemplateHistoryDO> historyPage = watermarkTemplateHistoryRepository
                .findAll(spec, pageable);
        List<WatermarkTemplateHistoryDO> content = historyPage.getContent();
        if (content.isEmpty()) {
            return new PageResult<>(historyPage);
        }
        List<TemplateChangeHistoryVO> list = content.stream().map(this::convertToVO).collect(Collectors.toList());
        PageResult<TemplateChangeHistoryVO> result = new PageResult<>(historyPage);
        result.setData(list);
        return result;
    }

    /**
     * 记录模板操作历史
     *
     * @param userId           操作人ID
     * @param templateNo       模板ID
     * @param operationType    操作类型：1=创建，2=更新，3=删除
     * @param operationContent 操作内容
     * @param templateSnapshot 模板快照（JSON格式）
     */
    private void recordTemplateHistory(Integer userId, String templateNo,String templateName, Integer operationType,
                                       String operationContent, String templateSnapshot) {
        WatermarkTemplateHistoryDO historyDO = new WatermarkTemplateHistoryDO();
        historyDO.setTemplateNo(templateNo);
        historyDO.setTemplateName(templateName);
        historyDO.setOperationType(operationType);
        historyDO.setOperatorId(userId);
        historyDO.setOperationContent(operationContent);
        historyDO.setTemplateSnapshot(templateSnapshot);
        watermarkTemplateHistoryRepository.save(historyDO);
    }


    /**
     * 将DO转换为VO
     */
    private TemplateChangeHistoryVO convertToVO(WatermarkTemplateHistoryDO historyDO) {
        // 获取模板名称
        String templateName = "";
        Integer templateId = null;
        try {
            Optional<WatermarkTemplateDO> templateOpt =
                    watermarkTemplateRepository.findByTemplateNo((historyDO.getTemplateNo()));
            if (templateOpt.isPresent()) {
                templateName = templateOpt.get().getTemplateName();
                templateId = templateOpt.get().getId();
            }
        } catch (Exception e) {
            log.error("获取模板名称失败: {}", e.getMessage(), e);
        }

        TemplateChangeHistoryVO vo = new TemplateChangeHistoryVO();
        vo.setId(historyDO.getId().intValue());
        vo.setTemplateId(templateId);
        vo.setTemplateNo(historyDO.getTemplateNo());
        vo.setTemplateName(templateName);
        vo.setOperationType(historyDO.getOperationType());
        vo.setOperationTypeDesc(getOperationTypeDesc(historyDO.getOperationType()));
        vo.setOperationTime(historyDO.getOperationTime());
        vo.setRolledBack(historyDO.getRolledBack());
        vo.setOperationContent(historyDO.getOperationContent());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackTemplateChange(Integer userId, Long historyId) {
        // 查询历史记录
        WatermarkTemplateHistoryDO history = watermarkTemplateHistoryRepository.findById(historyId)
                .orElseThrow(() -> new BusinessException("历史记录不存在"));


        // 验证历史记录是否属于当前用户
        Optional<WatermarkTemplateDO> templateOpt = watermarkTemplateRepository.findByTemplateNo(history.getTemplateNo());
        if (templateOpt.isPresent() && !templateOpt.get().getUserId().equals(userId)) {
            throw new BusinessException("无权限回滚此记录");
        }

        // 检查是否已回滚
        if (history.getRolledBack()) {
            throw new BusinessException("该记录已回滚，不能重复操作");
        }

        // 根据操作类型执行回滚
        if (history.getOperationType() == 1) { // 更新操作回滚
            rollbackUpdateOperation(userId, history);
        } else if (history.getOperationType() == 2) { // 删除操作回滚
            rollbackDeleteOperation(userId, history);
        } else {
            throw new BusinessException("不支持回滚此类型的操作");
        }
        WatermarkTemplateDO templateDO = templateOpt.get();
        // 记录删除操作历史，保存删除前的完整状态
        recordTemplateHistory(
                userId,
                templateDO.getTemplateNo(),
                templateDO.getTemplateName(),
                3, // 删除操作
                "回滚模板: " + templateDO.getTemplateName(),
                JSONUtil.toJsonStr(templateDO) // 保存删除前的完整状态
        );
        return true;
    }

    /**
     * 回滚更新操作
     */
    private boolean rollbackUpdateOperation(Integer userId, WatermarkTemplateHistoryDO history) {
        // 解析历史快照
        WatermarkTemplateDO oldState = JSONUtil.toBean(history.getTemplateSnapshot(), WatermarkTemplateDO.class);

        // 获取当前模板
        WatermarkTemplateDO template = watermarkTemplateRepository.findByTemplateNo(history.getTemplateNo())
                .orElseThrow(() -> new BusinessException("模板不存在或已被删除"));

        // 验证用户权限
        if (!template.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此模板");
        }

        // 恢复模板状态
        BeanUtil.copyProperties(oldState, template, "id", "createTime", "creator", "modifyTime", "modifier");
        template.setModifier(userId.toString());

        // 保存回滚后的状态
        watermarkTemplateRepository.save(template);

        // 标记历史记录为已回滚
        history.setRolledBack(true);
        watermarkTemplateHistoryRepository.save(history);
        return true;
    }

    /**
     * 回滚删除操作
     */
    private boolean rollbackDeleteOperation(Integer userId, WatermarkTemplateHistoryDO history) {
        // 检查用户当前模板数量是否已达上限
        long validTemplateCount = watermarkTemplateRepository.countByUserIdAndDeleted(userId, 0);
        if (validTemplateCount >= 2) {
            throw new BusinessException("水印模板已达到上限，请删除现有模板后再进行回滚操作");
        }

        // 解析历史快照
        WatermarkTemplateDO template = JSONUtil.toBean(history.getTemplateSnapshot(), WatermarkTemplateDO.class);

        // 验证用户权限
        if (!template.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此模板");
        }

        // 恢复已删除的模板
        template.setDeleted(0);
        template.setModifier(userId.toString());
        template.setModifyTime(LocalDateTime.now());

        // 保存恢复的模板
        watermarkTemplateRepository.save(template);

        // 标记历史记录为已回滚
        history.setRolledBack(true);
        watermarkTemplateHistoryRepository.save(history);

        return true;
    }

    /**
     * 生成模板编号，格式：wt_YYMMDDNNNN
     * @return 生成的模板编号
     */
    private String generateTemplateNo() {
        // 生成日期部分：YYMMDD
        LocalDate now = java.time.LocalDate.now();
        String dateStr = now.format(java.time.format.DateTimeFormatter.ofPattern("yyMMdd"));
        LocalDateTime start = now.atStartOfDay();
        LocalDateTime end = start.plusDays(1);
        long count = watermarkTemplateRepository.countByCreateTimeBetween(start, end);
        String seq = String.format("%04d", count + 1);
        return "WT_" + dateStr + seq;
    }

    /**
     * 获取操作类型描述
     */
    private String getOperationTypeDesc(Integer operationType) {
        if (operationType == null) {
            return "未知操作";
        }
        switch (operationType) {
            case 1:
                return "更新";
            case 2:
                return "删除";
            default:
                return "未知操作";
        }
    }
}
