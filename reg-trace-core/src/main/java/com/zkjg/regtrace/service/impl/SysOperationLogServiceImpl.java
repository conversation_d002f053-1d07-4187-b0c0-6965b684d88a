package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.persistence.entity.SysOperationLogDO;
import com.zkjg.regtrace.persistence.repository.SysOperationLogRepository;
import com.zkjg.regtrace.service.SysOperationLogService;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysOperationLogServiceImpl implements SysOperationLogService {

    @Resource
    private SysOperationLogRepository sysOperationLogRepository;

    @Override
    public void save(SysOperationLogDO sysOperationLogDO) {
        sysOperationLogRepository.save(sysOperationLogDO);
    }

    @Override
    public Page<SysOperationLogDO> findAll(Specification<SysOperationLogDO> specification, PageRequest pageRequest) {
        return sysOperationLogRepository.findAll(specification, pageRequest);
    }

    @Override
    public List<SysOperationLogDO> findAll(Specification<SysOperationLogDO> specification) {
        return sysOperationLogRepository.findAll(specification);
    }
}
