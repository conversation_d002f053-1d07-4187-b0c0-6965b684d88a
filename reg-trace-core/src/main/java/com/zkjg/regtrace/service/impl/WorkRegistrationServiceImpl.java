package com.zkjg.regtrace.service.impl;

import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.assembler.WorkRegistrationRecordAssembler;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import com.zkjg.regtrace.persistence.jpa.WorkRegistrationJpaSpec;
import com.zkjg.regtrace.persistence.repository.WorkRegistrationRepository;
import com.zkjg.regtrace.service.WorkRegistrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class WorkRegistrationServiceImpl implements WorkRegistrationService {

    @Resource
    private WorkRegistrationRepository workRegistrationRepository;

    @Override
    public WorkRegistrationDO save(WorkRegistrationDO workRegistrationDO) {
        log.info("保存作品登记信息: {}", workRegistrationDO.getWorkName());
        return workRegistrationRepository.save(workRegistrationDO);
    }

    @Override
    public List<WorkRegistrationDO> findByWorkNameAndDeleted(String workName, Integer deleted) {
        log.info("根据作品名称精确查询作品登记信息: {}, 删除状态: {}", workName, deleted);
        return workRegistrationRepository.findByWorkNameAndDeleted(workName, deleted);
    }

    @Override
    public Page<WorkRegistrationDO> findAll(Specification<WorkRegistrationDO> specification, PageRequest pageRequest) {
        return workRegistrationRepository.findAll(specification, pageRequest);
    }

    @Override
    public List<WorkRegistrationDO> findAll(Specification<WorkRegistrationDO> specification) {
        return workRegistrationRepository.findAll(specification);
    }

    @Override
    public WorkRegistrationDO findById(Integer id) {
        return workRegistrationRepository.findByIdAndDeleted(id, 0);
    }

    @Override
    public void update(WorkRegistrationDO workRegistrationDO) {
        workRegistrationRepository.save(workRegistrationDO);
    }

    @Override
    public WorkRegistrationDO findOne(Specification<WorkRegistrationDO> specification) {
        Optional<WorkRegistrationDO> one = workRegistrationRepository.findOne(specification);
        return one.orElse(null);
    }

    @Override
    public void updateBatch(List<WorkRegistrationDO> all) {
        workRegistrationRepository.saveAll(all);
    }
}
