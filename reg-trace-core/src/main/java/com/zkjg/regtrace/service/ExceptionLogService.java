package com.zkjg.regtrace.service;


import com.zkjg.regtrace.persistence.dto.exception.ExceptionDistributionDto;
import com.zkjg.regtrace.persistence.dto.exception.ExceptionFrequencyDto;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import com.zkjg.regtrace.persistence.vo.request.exception.ExceptionLogRequest;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ExceptionLogService {

    Page<ExceptionLogDO> pageQueryLogs(ExceptionLogRequest req);

    List<ExceptionDistributionDto> distributionsByDays();

    List<ExceptionFrequencyDto> exceptionFrequency(Integer days);

    void save(ExceptionLogDO exceptionLogDO);
}
