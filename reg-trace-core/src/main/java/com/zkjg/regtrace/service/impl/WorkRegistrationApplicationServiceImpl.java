package com.zkjg.regtrace.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.common.utils.IpLocationUtil;
import com.zkjg.regtrace.common.utils.IpUtil;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.dto.jpa.ListQueryWorkRegistrationApplicationJpaSpec;
import com.zkjg.regtrace.persistence.dto.workregistration.BatchTraceabilityResult;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.repository.WorkRegistrationApplicationRepository;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.ContentManagerQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.ContentManagerVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;
import com.zkjg.regtrace.service.SysUserService;
import com.zkjg.regtrace.service.WorkRegistrationApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static com.zkjg.regtrace.common.constants.PagingQuerySortConstant.SORT_CREATE_TIME;

@Service
@Slf4j
public class WorkRegistrationApplicationServiceImpl implements WorkRegistrationApplicationService {

    @Resource
    private WorkRegistrationApplicationRepository workRegistrationApplicationRepository;

    @Resource
    private SysUserService sysUserService;

    @Resource
    IpLocationUtil ipLocationUtil;

    @PersistenceContext
    private EntityManager entityManager;

    private static final Integer BATCH_SIZE = 1000;

    @Override
    public Page<WorkRegistrationApplicationDO> findAll(Specification<WorkRegistrationApplicationDO> specification,Pageable pageable){
        return workRegistrationApplicationRepository.findAll(specification, pageable);
    }

    @Override
    public WorkRegistrationApplicationDO saveFromApplicationRequest(WorkRegistrationApplicationRequest req) {
        Integer auditStatus = req.isDraft() ? WorkRegistrationAuditStatusEnum.DRAFT.getCode() : WorkRegistrationAuditStatusEnum.PENDING.getCode();
        Integer applicantUserId = TokenService.getLoginUser().getUserId();
        SysUserDO applicantUser = sysUserService.findUserById(applicantUserId).orElse(null);
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String ip = IpUtil.getIp(httpServletRequest);
        String location = ipLocationUtil.getIpLocation(ip);
        WorkRegistrationApplicationDO application = WorkRegistrationApplicationDO.builder()
                .registrationNumber(req.getRegistrationNumber())
                .applicantUserId(applicantUserId)
                .workName(req.getWorkName())
                .ip(ip)
                .location(location)
                .applicationReason(req.getApplicationReason())
                .applicantName(applicantUser.getUsername())
                .applicantEmail(applicantUser.getEmail())
                .auditStatus(auditStatus)
                .description(req.getDescription())
                .deleted(SqlConstant.UN_DELETED)
                .createTime(LocalDateTime.now())
                .build();
        // 保存申请
        workRegistrationApplicationRepository.save(application);
        return application;
    }

    @Override
    public WorkRegistrationApplicationDO findById(Integer id) {
        return workRegistrationApplicationRepository.findById(id).orElse(null);
    }

    @Override
    public void update(WorkRegistrationApplicationDO application) {
        workRegistrationApplicationRepository.save(application);
    }

    @Override
    public List<WorkRegistrationApplicationDO> batchSave(List<WorkRegistrationApplicationDO> applicationList) {
        return workRegistrationApplicationRepository.saveAll(applicationList);
    }


    @Override
    public List<WorkRegistrationApplicationDO> findAll(ListQueryWorkRegistrationApplicationJpaSpec jpaSpec) {
        Specification<WorkRegistrationApplicationDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        return workRegistrationApplicationRepository.findAll(specification);
    }

    @Override
    public List<WorkRegistrationApplicationDO> findByWorkNames(List<String> workNames) {
        if (workNames == null || workNames.isEmpty()) {
            return new ArrayList<>();
        }
        return workRegistrationApplicationRepository.findByWorkNameIn(workNames);
    }

    @Override
    public Result<List<WorkRegistrationApplicationVo>> findByWorkNameContainingAndAuditStatus(String workName) {
        List<WorkRegistrationApplicationDO> workRegistrationApplicationDos = workRegistrationApplicationRepository.findByWorkNameContainingAndAuditStatus(workName, WorkRegistrationAuditStatusEnum.APPROVED.getCode());
        if(CollectionUtils.isEmpty(workRegistrationApplicationDos)){
            return Result.ofSuccess(new ArrayList<>());
        }
        return Result.ofSuccess(BeanUtil.copyToList(workRegistrationApplicationDos,WorkRegistrationApplicationVo.class));
    }

    @Override
    public WorkRegistrationApplicationDO findByRegistrationNumber(String registrationNumber) {
        return workRegistrationApplicationRepository.findByRegistrationNumberAndAuditStatusAndDeleted(registrationNumber, WorkRegistrationAuditStatusEnum.APPROVED.getCode(), SqlConstant.UN_DELETED);
    }

    @Override
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> listAudit(QueryWorkRegistrationApplicationRequest req) {
        return null;
    }

    @Override
    public Page<ContentManagerVo> listContentManager(ContentManagerQueryRequest req) {
        //参数
        if (Objects.nonNull(req.getFileName())) {
            req.setFileName("%" + req.getFileName() + "%");
        }
        Sort sort = Sort.by(Sort.Direction.DESC, SORT_CREATE_TIME);
        PageRequest pageRequest = PageRequest.of(req.getPage() - 1, Objects.isNull(req.getSize()) ? CommonConstant.PAGE_SIZE : req.getSize(), sort);
        return workRegistrationApplicationRepository.findRegisterContent(req.getContentType(),req.getFileName(), req.getAuditStatus(), req.getStartTime(), req.getEndTime(), pageRequest);
    }

    public List<BatchTraceabilityResult> batchQueryRegistrationApplication(List<ConditionalTraceabilityRequest> requests) {
        List<BatchTraceabilityResult> allResults = new ArrayList<>();
        int total = requests.size();

        for (int i = 0; i < total; i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, total);
            List<ConditionalTraceabilityRequest> batch = requests.subList(i, end);
            allResults.addAll(processSingleBatch(batch));
        }
        return allResults;
    }

    private List<BatchTraceabilityResult> processSingleBatch(List<ConditionalTraceabilityRequest> batchRequests) {
        // 构建临时表 SQL
        String tempTableSql = buildDmCompatibleTempTableSql(batchRequests);

        // 构建主 SQL
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ")
                .append("t.row_index AS rowIndex, ")
                .append("f.file_name AS fileName, ")
                .append("f.file_size AS fileSize, ")
                .append("f.original_work_hash AS originalFileHash, ")
                .append("f.watermarked_work_hash AS watermarkedFileHash, ")
                .append("f.create_time AS uploadTime, ")
                .append("w.registration_number AS registrationNumber, ")
                .append("w.work_name AS workName, ")
                .append("w.file_type AS workType, ")
                .append("w.applicant_name AS author, ")
                .append("w.location AS publishLocation, ")
                .append("w.description AS description, ")
                .append("w.create_time AS registrationTime ")
                .append("FROM (").append(tempTableSql).append(") t ")
                .append("LEFT JOIN work_registration_application_file f ON ")
                .append("(t.file_name IS NULL OR f.file_name = t.file_name) ")
                .append("AND (t.hash IS NULL OR f.original_work_hash = t.hash OR f.watermarked_work_hash = t.hash) ")
                .append("LEFT JOIN work_registration w ON w.application_id = f.application_id ")
                .append("WHERE w.recycle = 0 AND w.deleted = 0");

        // 查询
        List<Object[]> rawList = entityManager.createNativeQuery(sql.toString()).getResultList();

        // 转换结果
        Map<Integer, WorkRegistrationSourceVo> voMap = new HashMap<>();
        for (Object[] row : rawList) {
            if (row[0] == null) continue;
            Integer rowIndex = ((Number) row[0]).intValue();
            WorkRegistrationSourceVo vo = new WorkRegistrationSourceVo(
                    ((Number) row[0]).intValue(),
                    (String) row[1],
                    row[2] == null ? null : ((Number) row[2]).longValue(),
                    (String) row[3],
                    (String) row[4],
                    (Timestamp) row[5],
                    (String) row[6],
                    (String) row[7],
                    row[8] == null ? null : ((Number) row[8]).intValue(),
                    (String) row[9],
                    (String) row[10],
                    (String) row[11],
                    (Timestamp) row[12],
                    2
            );

            voMap.put(rowIndex, vo);
        }

        // 每个请求都要构造结果
        List<BatchTraceabilityResult> results = new ArrayList<>();
        for (ConditionalTraceabilityRequest req : batchRequests) {
            WorkRegistrationSourceVo vo = voMap.get(req.getRowIndex());
            results.add(BatchTraceabilityResult.builder()
                    .request(req)
                    .matchedRecord(vo) // 未命中则为 null
                    .build());
        }

        return results;
    }

    private String buildDmCompatibleTempTableSql(List<ConditionalTraceabilityRequest> batchRequests) {
        StringJoiner sj = new StringJoiner(" UNION ALL ");
        for (ConditionalTraceabilityRequest req : batchRequests) {
            String rowIndex = String.valueOf(req.getRowIndex());
            String fileName = safeSqlString(req.getFileName());
            String hash = safeSqlString(req.getHash());

            sj.add(String.format("SELECT %s AS row_index, %s AS file_name, %s AS hash FROM dual", rowIndex, fileName, hash));
        }
        return sj.toString();
    }

    private String safeSqlString(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "NULL";
        }
        return "'" + input.replace("'", "''") + "'";
    }

}
