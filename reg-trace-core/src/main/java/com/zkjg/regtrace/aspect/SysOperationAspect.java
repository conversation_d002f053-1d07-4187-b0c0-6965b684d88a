package com.zkjg.regtrace.aspect;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.constants.MessageOperationContentConstant;
import com.zkjg.regtrace.common.enums.MessageOperationTypeEnum;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.enums.UserTypeEnum;
import com.zkjg.regtrace.common.utils.IpUtil;
import com.zkjg.regtrace.persistence.entity.SysOperationLogDO;
import com.zkjg.regtrace.persistence.repository.MessageNotifyRepository;
import com.zkjg.regtrace.persistence.vo.request.message.BatchUpdateMessageRequest;
import com.zkjg.regtrace.service.SysOperationLogService;
import io.swagger.models.auth.In;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.zkjg.regtrace.common.enums.OperationLogType.MESSAGE_DETAIL;

/**
 * <AUTHOR>
 * @Desc 系统日志切面
 * @date 2023-05-15 09:19
 */
@Aspect
@Component
public class SysOperationAspect {
    @Resource
    private HttpServletRequest request;
    @Resource
    private SysOperationLogService sysOperationLogService;

    @Pointcut("@annotation(com.zkjg.regtrace.common.annotation.OperationLog)")
    private void operationLogPointcut() {
    }

    /**
     * 系统操作日志 切面
     */
    @Around("operationLogPointcut()")
    public Object operationLogAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed = null;
        SysOperationLogDO log = new SysOperationLogDO();
        log.setOperatorTime(LocalDateTime.now());
        long currentTimeMillis = System.currentTimeMillis();
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            /*OperationLog operationLogAnnotation = method.getAnnotation(OperationLog.class);
            OperationLogType type = operationLogAnnotation.type();*/

            OperationLog operationLog = method.getAnnotation(OperationLog.class);
            OperationLogType type = operationLog.type();
            String resolvedContent = parseSpEL(operationLog.content(), joinPoint);
            log.setContent(resolvedContent);
            proceed = joinPoint.proceed();
            if (MESSAGE_DETAIL.getCode().equals(type.getCode())) {
                if (StatusEnum.NO.getCode() == Integer.parseInt(resolvedContent)) {
                    log.setContent(MessageOperationContentConstant.DETAIL);
                } else {
                    return proceed;
                }
            }
            if (type.equals(OperationLogType.MESSAGE_OPERATE)) {
                Object[] args = joinPoint.getArgs();
                BatchUpdateMessageRequest request = null;
                for (Object arg : args) {
                    if (arg instanceof BatchUpdateMessageRequest) {
                        request = (BatchUpdateMessageRequest) arg;
                        break;
                    }
                }
                if (request != null) {
                    MessageOperationTypeEnum opEnum = request.getOperationTypeEnum();
                    if (opEnum != null) {
                        log.setOperateLogType(opEnum.getType());
                        Integer messageSize = request.getMessageSize();
                        switch (opEnum) {
                            case MESSAGE_DELETE:
                                String suffix = String.format(MessageOperationContentConstant.DELETE, messageSize);
                                String content = messageSize > 1 ? "批量" + suffix : suffix;
                                log.setContent(content);
                                break;
                            case MESSAGE_EXPORT:
                                log.setContent(String.format(MessageOperationContentConstant.BATCH_EXPORT, messageSize));
                                break;
                            case MESSAGE_MARK:
                                log.setContent(String.format(MessageOperationContentConstant.BATCH_MARK_READ, messageSize));
                                break;
                            default:
                                break;
                        }
                    }
                }
            } else {
                log.setOperateLogType(type.getCode());
            }
            log.setOperationStatus(StatusEnum.YES.getCode());
        } catch (Throwable throwable) {
            log.setOperationStatus(StatusEnum.NO.getCode());
            log.setErrorMsg(throwable.getMessage());
            throw throwable;
        } finally {
            if (Objects.nonNull(TokenService.getLoginUser())) {
                log.setOperatorUserId(TokenService.getLoginUser().getUserId());
                log.setOperatorUsername(TokenService.getLoginUser().getUsername());
            } else {
                log.setOperatorUsername(UserTypeEnum.GUEST_USER.getDescription());
            }
            log.setIpAddress(IpUtil.getIp(request));
            // 记录日志
            log.setConsumeTime(System.currentTimeMillis() - currentTimeMillis);
            log.setDeleted(StatusEnum.NO.getCode());
            sysOperationLogService.save(log);
        }
        return proceed;
    }

    private String parseSpEL(String content, ProceedingJoinPoint joinPoint) {
        // 正则匹配 #req.name
        Pattern pattern = Pattern.compile("#[a-zA-Z0-9_.()]+");
        Matcher matcher = pattern.matcher(content);

        //SpEL 表达式
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 设置方法参数到 SpEL 上下文
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = signature.getParameterNames();
        for (int i = 0; i < args.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;

        while (matcher.find()) {
            // 拼接匹配到的文本和前面的部分
            result.append(content, lastEnd, matcher.start());
            // 获取SpEL表达式并解析
            String expression = matcher.group();
            String value = parser.parseExpression(expression).getValue(context, String.class);
            // 拼接解析结果
            result.append(value);
            // 更新匹配到的结束位置
            lastEnd = matcher.end();
        }
        // 拼接内容中的剩余部分
        result.append(content.substring(lastEnd));
        return result.toString();
    }

}
