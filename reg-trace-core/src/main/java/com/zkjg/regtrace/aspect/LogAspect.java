package com.zkjg.regtrace.aspect;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

@Aspect
@Component
@Slf4j
public class LogAspect {

    @Resource
    private HttpServletRequest request;

    /**
     * 切面，切的是controller层的方法
     */
    @Pointcut("execution(public * com.zkjg..*controller..*(..))")
    public void logPointcut() {
    }

    @Around("logPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestURI = request.getRequestURI();
        String interFaceAndMethod = joinPoint.getTarget().getClass().getSimpleName() + "." + joinPoint.getSignature().getName();

        // 过滤敏感接口，不记录详细参数
        boolean isSensitive = isSensitiveApi(requestURI);
        String args = isSensitive ? "[SENSITIVE]" : getSafeArgs(joinPoint.getArgs());

        long startTime = System.currentTimeMillis();
        log.info("请求开始 - 接口:{}, 路径:{}, 参数:{}", interFaceAndMethod, requestURI, args);

        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;

            // 敏感接口不记录返回值
            String result = isSensitive ? "[SENSITIVE]" : getSafeResult(proceed);
            log.info("请求完成 - 接口:{}, 耗时:{}ms, 返回值:{}", interFaceAndMethod, duration, result);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("请求异常 - 接口:{}, 耗时:{}ms, 异常:{}", interFaceAndMethod, duration, e.getMessage());
            throw e;
        }
        return proceed;
    }

    /**
     * 判断是否为敏感API
     */
    private boolean isSensitiveApi(String uri) {
        return uri.contains("/login") || uri.contains("/register") ||
               uri.contains("/password") || uri.contains("/user/info");
    }

    /**
     * 获取安全的参数字符串
     */
    private String getSafeArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < args.length; i++) {
            if (i > 0) sb.append(", ");

            Object arg = args[i];
            if (arg == null) {
                sb.append("null");
            } else if (arg instanceof HttpServletRequest || arg instanceof HttpServletResponse) {
                sb.append(arg.getClass().getSimpleName());
            } else {
                String argStr = arg.toString();
                // 限制参数长度，避免日志过长
                sb.append(argStr.length() > 200 ? argStr.substring(0, 200) + "..." : argStr);
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 获取安全的返回值字符串
     */
    private String getSafeResult(Object result) {
        if (result == null) {
            return "null";
        }

        try {
            String resultStr = JSON.toJSONString(result);
            // 限制返回值长度，避免日志过长
            return resultStr.length() > 500 ? resultStr.substring(0, 500) + "..." : resultStr;
        } catch (Exception e) {
            return result.getClass().getSimpleName() + "@" + result.hashCode();
        }
    }

}
