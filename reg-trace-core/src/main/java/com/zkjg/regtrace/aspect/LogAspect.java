package com.zkjg.regtrace.aspect;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

@Aspect
@Component
@Slf4j
public class LogAspect {

    @Resource
    private HttpServletRequest request;

    /**
     * 切面，切的是controller层的方法
     */
    @Pointcut("execution(public * com.zkjg..*controller..*(..))")
    public void logPointcut() {
    }

    @Around("logPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestURI = request.getRequestURI();
        String interFaceAndMethod = joinPoint.getTarget().getClass() + "/" + joinPoint.getSignature().getName();
        String args = Arrays.toString(joinPoint.getArgs());
        log.info(interFaceAndMethod + " 方法请求路径:{},参数是:{}", requestURI, args);
        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
        } finally {
            log.info("方法:{}, 返回值:{}", interFaceAndMethod, JSON.toJSONString(proceed));
        }
        return proceed;
    }

}
