package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出任务调度类型枚举
 */
@Getter
@AllArgsConstructor
public enum ExportTaskScheduleTypeEnum {

    NONE("NONE", "不启用"),
    WEEKLY("WEEKLY", "每周"),
    MONTHLY("MONTHLY", "每月");

    private final String type;
    private final String description;

    public static ExportTaskScheduleTypeEnum fromType(String type) {
        for (ExportTaskScheduleTypeEnum value : values()) {
            if (value.type.equalsIgnoreCase(type)) {
                return value;
            }
        }
        return null;
    }
}
