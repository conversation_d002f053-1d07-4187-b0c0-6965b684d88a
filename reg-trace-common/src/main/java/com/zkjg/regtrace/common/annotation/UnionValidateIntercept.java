package com.zkjg.regtrace.common.annotation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 联合字段校验
 * @create 2024/6/18 9:09
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UnionValidator.class)
@Documented
public @interface UnionValidateIntercept {
    String message() default "Invalid value";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String dependentField() default "";

    String currentField() default "";

    String[] dependentFieldValue() default {};

    int max() default 500;

    String regexp() default "";

    @Target({ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @interface List {
        UnionValidateIntercept[] value();
    }
}

class UnionValidator implements ConstraintValidator<UnionValidateIntercept, Object> {
    private int max;
    private String regexp;
    private String dependentField;
    private String currentField;
    private String[] dependentFieldValue;

    @Override
    public void initialize(UnionValidateIntercept constraintAnnotation) {
        max = constraintAnnotation.max();
        regexp = constraintAnnotation.regexp();
        dependentField = constraintAnnotation.dependentField();
        currentField = constraintAnnotation.currentField();
        dependentFieldValue = constraintAnnotation.dependentFieldValue();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext constraintValidatorContext) {
        try {
            Field declaredField = value.getClass().getDeclaredField(dependentField);
            Field declaredField1 = value.getClass().getDeclaredField(currentField);
            declaredField.setAccessible(true);
            declaredField1.setAccessible(true);
            String factValue = (String) declaredField.get(value);
            String currentFieldValue = (String) declaredField1.get(value);

            if (Objects.nonNull(factValue) && Arrays.stream(this.dependentFieldValue).collect(Collectors.toList()).contains(factValue)) {
                if (StringUtils.isEmpty(currentFieldValue)) {
                    return false;
                } else if (currentFieldValue.length() > max) {
                    return false;
                } else if (StringUtils.isNotEmpty(regexp)) {
                    return Pattern.matches(regexp, currentFieldValue);
                }

            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}