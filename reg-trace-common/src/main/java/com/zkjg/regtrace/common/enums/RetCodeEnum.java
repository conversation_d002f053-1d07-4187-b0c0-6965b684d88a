package com.zkjg.regtrace.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/10 15:13
 */
@Getter
public enum RetCodeEnum {
    SUCCESS(200, "成功"),
    FAIL(400, "操作失败"),

    /**
     * minio
     */
    BUCKET_NAME_NOT_NULL(300_1_000, "桶名称不能为空"),
    FILE_NAME_NOT_NULL(300_1_001, "上传文件名不能为空"),
    FILE_NOT_EXIST(300_1_002, "文件不存在"),
    BUCKET_NOT_EXIST(300_1_003, "桶不存在"),
    BUCKET_NAME_NOT_EXIST(300_1_004, "桶不存在，需要先创建桶在创建文件夹"),
    BUCKET_IMAGE_TOO_FREQUENTLY(300_1_005, "频繁上传图片,请稍后"),


    /**
     * 音频登记
     */
    WORK_REGISTRATION_APPLICATION_NOT_EXISTS(400_1_000, "申请信息不存在"),
    WORK_REGISTRATION_APPLICATION_STATUS_NOT_PENDING(400_1_001, "申请状态不是待审核，不能进行审核")
    ;
    private final Integer code;
    private final String msg;
    RetCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
