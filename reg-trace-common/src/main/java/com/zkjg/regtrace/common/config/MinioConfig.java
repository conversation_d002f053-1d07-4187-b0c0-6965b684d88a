package com.zkjg.regtrace.common.config;


import com.zkjg.regtrace.common.utils.SslUtils;
import io.minio.MinioClient;
import lombok.Data;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.KeyManagementException;

/**
 * <AUTHOR>
 * @Date 2024/1/5 9:27
 */
@Configuration
@Data
public class MinioConfig {
    @Value("${minio.endpoint:}")
    private String endpoint;

    @Value("${minio.accessKey:}")
    private String accessKey;

    @Value("${minio.secretKey:}")
    private String secretKey;

    @Value("${minio.bucket:}")
    private String bucketName;

    @Value("${minio.read-url:}")
    private String readUrl;

    @Value("${minio.object:}")
    private String objectName;

    @Bean
    public MinioClient getMinioClient() {
        OkHttpClient okHttpClient = null;
        try {
             okHttpClient = SslUtils.getUnsafeOkHttpClent();
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        return MinioClient.builder().endpoint(endpoint)
                .credentials(accessKey, secretKey).httpClient(okHttpClient).build();
    }
}
