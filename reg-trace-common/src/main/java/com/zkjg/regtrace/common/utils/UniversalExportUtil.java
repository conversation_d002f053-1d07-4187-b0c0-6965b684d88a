package com.zkjg.regtrace.common.utils;

import com.zkjg.regtrace.common.exceptions.BusinessException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class UniversalExportUtil {

    public enum ExportFormat {
        EXCEL, CSV
    }

    /**
     * 字段值转换器接口
     */
    @FunctionalInterface
    public interface ValueConverter<T> {
        /**
         * 转换字段值
         * @param value 原始值
         * @return 转换后的值
         */
        String convert(T value);
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.FIELD)
    public @interface ExportField {
        /**
         * 导出列标题
         */
        String value();

        /**
         * 转换器类型，指定使用哪种转换器
         * 为空时不进行转换
         */
        String converter() default "";
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.FIELD)
    public @interface ExportIgnore {}

    /**
     * 存储已注册的值转换器
     */
    private static final Map<String, ValueConverter<?>> VALUE_CONVERTERS = new HashMap<>();

    /**
     * 注册一个值转换器
     * @param name 转换器名称
     * @param converter 转换器实现
     */
    public static void registerConverter(String name, ValueConverter<?> converter) {
        VALUE_CONVERTERS.put(name, converter);
    }

    /**
     * 注册状态转换器，方便将数字状态码转为文本描述
     * @param name 转换器名称
     * @param valueMap 状态映射表
     */
    public static void registerStatusConverter(String name, Map<Object, String> valueMap) {
        registerConverter(name, value -> {
            if (value == null) return "";
            String result = valueMap.get(value);
            return result != null ? result : value.toString();
        });
    }

    public static <T> ExportFile export(List<T> data, Class<T> clazz, ExportFormat format, String fileBaseName) throws Exception {
        switch (format) {
            case EXCEL:
                return exportExcel(data, clazz, fileBaseName + ".xlsx");
            case CSV:
                return exportCSV(data, clazz, fileBaseName + ".csv");
            default:
                throw new BusinessException("Unsupported export format");
        }
    }

    public static <T> void exportStream(List<T> list, Class<T> clazz, String fileName, String type, HttpServletResponse response) {
        try{
            ExportFormat format;
            try {
                format = ExportFormat.valueOf(type.toUpperCase());
            } catch (BusinessException | NullPointerException e) {
                format = ExportFormat.EXCEL; // 默认导出为Excel
            }
            ExportFile file = export(
                    list,
                    clazz,
                    format,
                    fileName
            );
            response.setContentType(file.getContentType());
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    URLEncoder.encode(file.getFileName(), "UTF-8"));
            InputStream in = file.getInputStream();
            OutputStream out = response.getOutputStream();
            byte[] buffer = new byte[8192];
            int len;
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            log.debug("导出文件发生异常,error:{}", e.getMessage());
        }
    }

    private static <T> ExportFile exportExcel(List<T> data, Class<T> clazz, String fileName) throws Exception {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        List<Field> fields = getExportableFields(clazz);

        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < fields.size(); i++) {
            headerRow.createCell(i).setCellValue(getFieldTitle(fields.get(i)));
        }

        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            T item = data.get(i);
            for (int j = 0; j < fields.size(); j++) {
                Field field = fields.get(j);
                Object value = field.get(item);
                String displayValue = convertFieldValue(field, value);
                row.createCell(j).setCellValue(displayValue != null ? safeExcelValue(displayValue) : "");
            }
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        workbook.write(out);
        workbook.close();

        return new ExportFile(fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                new ByteArrayInputStream(out.toByteArray()));
    }

    private static <T> ExportFile exportCSV(List<T> data, Class<T> clazz, String fileName) throws Exception {
        List<Field> fields = getExportableFields(clazz);
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < fields.size(); i++) {
            sb.append(escapeCsv(getFieldTitle(fields.get(i))));
            if (i != fields.size() - 1) sb.append(",");
        }
        sb.append("\n");

        for (T item : data) {
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                Object value = field.get(item);
                String displayValue = convertFieldValue(field, value);
                sb.append(escapeCsv(displayValue != null ? displayValue : ""));
                if (i != fields.size() - 1) sb.append(",");
            }
            sb.append("\n");
        }

        byte[] bytes = sb.toString().getBytes(StandardCharsets.UTF_8);
        return new ExportFile(fileName, "text/csv;charset=UTF-8", new ByteArrayInputStream(bytes));
    }

    /**
     * 转换字段值，根据字段上的注解和转换器配置进行转换
     * @param field 字段
     * @param value 原始值
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    private static String convertFieldValue(Field field, Object value) {
        if (value == null) {
            return "";
        }

        ExportField annotation = field.getAnnotation(ExportField.class);
        if (annotation != null && !annotation.converter().isEmpty()) {
            String converterName = annotation.converter();
            ValueConverter<Object> converter = (ValueConverter<Object>) VALUE_CONVERTERS.get(converterName);
            if (converter != null) {
                return converter.convert(value);
            }
        }

        return value.toString();
    }

    private static List<Field> getExportableFields(Class<?> clazz) {
        Field[] allFields = clazz.getDeclaredFields();
        List<Field> exportFields = new ArrayList<>();
        for (Field field : allFields) {
            if (!field.isAnnotationPresent(ExportIgnore.class)) {
                field.setAccessible(true);
                exportFields.add(field);
            }
        }
        return exportFields;
    }

    private static String getFieldTitle(Field field) {
        ExportField annotation = field.getAnnotation(ExportField.class);
        return annotation != null ? annotation.value() : field.getName();
    }

    private static String safeExcelValue(String value) {
        return value.matches("^[=\\-+@].*") ? "'" + value : value;
    }

    private static String escapeCsv(String value) {
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    @Getter
    @RequiredArgsConstructor
    public static class ExportFile {
        private final String fileName;
        private final String contentType;
        private final InputStream inputStream;
    }
}
