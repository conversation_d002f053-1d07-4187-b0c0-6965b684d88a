package com.zkjg.regtrace.common.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @create 2024/6/17 14:18
 */
@AllArgsConstructor
@Getter
public enum PermissionTypeEnum {
    MODULE(1, "模块"),
    MENU(2, "菜单"),
    BUTTON(3, "按钮");

    private final Integer value;

    @JsonFormat
    private final String description;
}
