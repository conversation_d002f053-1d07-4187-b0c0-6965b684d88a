package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/13 15:39
 */
@AllArgsConstructor
@Getter
public enum WorkRegistrationTemplateEnum {
    WORK_REGISTRATION_AUDIT("音视频登记审核结果通知", "workRegistration/work_registration_audit_result.ftl"),
    WORK_REGISTRATION_DETAIL("音视频登记详情", "workRegistration/work_registration_detail.ftl");

    private final String subject;
    private final String template;
}
