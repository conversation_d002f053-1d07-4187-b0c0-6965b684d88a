package com.zkjg.regtrace.common.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import net.dreamlu.mica.ip2region.core.IpInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.zkjg.regtrace.common.utils.IpUtil.isInternalIp;

/**
 * <AUTHOR>
 * @Date 2025/6/19 13:52
 */
@Component
@Slf4j
public class IpLocationUtil {

    @Resource
    private Ip2regionSearcher searcher;

    /**
     * 获取IP地理位置信息（字符串格式）
     */
    public  String getIpLocation(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return "未知";
        }
        if (isInternalIp(ip)) {
            return "内网本地";
        }
        try {
            IpInfo ipInfo = searcher.memorySearch(ip);
            if (ipInfo != null) {
                return String.join("",
                        ObjectUtil.defaultIfNull(ipInfo.getCountry(), "未知"),
                        //ObjectUtil.defaultIfNull(ipInfo.getRegion(), "未知"),
                        ObjectUtil.defaultIfNull(ipInfo.getProvince(), "未知"),
                        ObjectUtil.defaultIfNull(ipInfo.getCity(), "未知"));
                        //ObjectUtil.defaultIfNull(ipInfo.getIsp(), "未知"))

            }
        } catch (Exception e) {
            log.error("IP地理位置查询失败: {}", ip, e);
        }
        return "未知";
    }




}
