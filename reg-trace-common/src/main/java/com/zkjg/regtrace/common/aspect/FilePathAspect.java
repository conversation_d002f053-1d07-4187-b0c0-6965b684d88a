package com.zkjg.regtrace.common.aspect;

import com.zkjg.regtrace.common.annotation.FullFilePath;
import com.zkjg.regtrace.common.config.MinioConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Collection;

/**
 * 文件路径处理切面
 * 自动为标记了 @FullFilePath 注解的字段添加完整的文件访问路径前缀
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class FilePathAspect {

    @Resource
    private MinioConfig minioConfig;

    /**
     * 拦截所有 Controller 方法
     */
    @Around("execution(* com.zkjg.regtrace.*.controller..*(..))")
    public Object processFilePath(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();

        if (result != null) {
            try {
                processFilePathFields(result);
            } catch (Exception e) {
                log.warn("处理文件路径时发生异常，方法: {}, 异常: {}",
                    joinPoint.getSignature().toShortString(), e.getMessage());
            }
        }

        return result;
    }

    /**
     * 处理对象中的文件路径字段
     */
    private void processFilePathFields(Object obj) {
        if (obj == null) {
            return;
        }

        try {
            // 处理集合类型
            if (obj instanceof Collection) {
                Collection<?> collection = (Collection<?>) obj;
                for (Object item : collection) {
                    processFilePathFields(item);
                }
                return;
            }

            // 处理数组类型
            if (obj.getClass().isArray()) {
                Object[] array = (Object[]) obj;
                for (Object item : array) {
                    processFilePathFields(item);
                }
                return;
            }

            // 跳过基本类型和包装类型
            if (isBasicType(obj.getClass())) {
                return;
            }

            // 跳过 Spring 框架类和第三方库类
            String className = obj.getClass().getName();
            if (className.startsWith("org.springframework.")
                || className.startsWith("java.")
                || className.startsWith("javax.")) {
                return;
            }

            // 处理对象的字段
            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);

                // 检查字段是否标记了 @FullFilePath 注解
                FullFilePath annotation = field.getAnnotation(FullFilePath.class);
                if (annotation != null && annotation.enabled()) {
                    processFilePathField(obj, field, annotation);
                } else {
                    // 递归处理嵌套对象
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null && !isBasicType(fieldValue.getClass())) {
                        processFilePathFields(fieldValue);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理文件路径字段时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 处理单个文件路径字段
     */
    private void processFilePathField(Object obj, Field field, FullFilePath annotation) throws IllegalAccessException {
        Object value = field.get(obj);
        if (value instanceof String) {
            String filePath = (String) value;
            if (StringUtils.isNotBlank(filePath)) {
                String fullPath = buildFullPath(filePath, annotation);
                field.set(obj, fullPath);
            }
        }
    }

    /**
     * 构建完整的文件路径
     */
    private String buildFullPath(String filePath, FullFilePath annotation) {
        // 如果已经是完整路径，则不处理
        if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
            return filePath;
        }

        // 使用自定义前缀或默认的MinIO配置
        String prefix = StringUtils.isNotBlank(annotation.prefix()) 
            ? annotation.prefix() 
            : minioConfig.getReadUrl();

        if (StringUtils.isBlank(prefix)) {
            return filePath;
        }

        // 确保前缀以 / 结尾，文件路径不以 / 开头
        if (!prefix.endsWith("/")) {
            prefix += "/";
        }
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }

        return prefix + filePath;
    }

    /**
     * 判断是否为基本类型或包装类型
     */
    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() 
            || clazz == String.class
            || clazz == Integer.class
            || clazz == Long.class
            || clazz == Double.class
            || clazz == Float.class
            || clazz == Boolean.class
            || clazz == Character.class
            || clazz == Byte.class
            || clazz == Short.class
            || Number.class.isAssignableFrom(clazz)
            || clazz.getName().startsWith("java.time.");
    }
}
