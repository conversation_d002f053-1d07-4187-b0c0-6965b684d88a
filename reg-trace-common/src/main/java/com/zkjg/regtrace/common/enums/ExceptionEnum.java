package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ExceptionEnum implements ExportableEnum {

    REGISTRATION_EXCEPTION("REGISTRATION_EXCEPTION","创作登记异常"),
    TRACEABILITY_EXCEPTION("TRACEABILITY_EXCEPTION", "溯源认证异常"),
    USER_ACTIVITY_EXCEPTION("USER_ACTIVITY_EXCEPTION","用户活动异常");

    private final String type;
    private final String description;
}
