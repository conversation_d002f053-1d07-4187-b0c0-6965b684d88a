package com.zkjg.regtrace.common.constants;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 9:24
 */
public class RegexConstant {
    /**
     * 11位手机号码
     * 1开头 二位3-9 11位
     */
    public static final String PHONE_REGEX = "^1[3-9]\\d{9}$";
    /**
     * 身份证
     */
    public static final String ID_CARD_REGEX = "^([1-9]\\d{5})(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])\\d{3}(\\d|X|x)$";
    /**
     * 用户名校验 4-20位，支持英文、数字、“-”、“_”
     */
    public static final String USERNAME_REGEX = "^[a-zA-Z0-9_-]{4,20}$";
}
