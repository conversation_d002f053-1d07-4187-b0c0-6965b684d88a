package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/16 8:49
 */
@Getter
@AllArgsConstructor
public enum MessageEnum {
    WORK_REGISTRATION_PASS("您提交的登记申请（登记编号：%s，作品名称：%s）已审核通过，登记成功，请前往【登记记录】页面查看详情","登记结果通知", 1),
    WORK_REGISTRATION_REJECT("您提交的登记申请（登记编号：%s，作品名称：%s）未通过审核，登记失败，请前往【登记记录】页面查看详情","登记结果通知", 1),
    TRACEABILITY_USER("您提交的溯源认证（溯源编号：%s，文件名称：%s）已完成，溯源结果为【%s】，请前往【作品溯源】页面查看详情", "溯源通知", 1),
    CONDITION_TRACEABILITY_USER("您提交的溯源认证（溯源编号：%s）已完成，溯源结果为【%s】，请前往【溯源记录】页面查看详情", "溯源通知",1),
    BATCH_TRACEABILITY_USER("您提交的批量溯源认证已完成，请前往【溯源结果】页面查看详情", "溯源通知",1),
    TRACEABILITY_SCHEDULE_EXPORT("【溯源记录自动导出通知】\n" +
            "已自动导出%s至%s期间的溯源记录，<br><a href='%s' target='_blank'>点击下载溯源文件</a>", "溯源通知", 1),
    TRACEABILITY_REGISTER("您登记的作品《%s》于%s被溯源查询，请前往【信息追踪】页面查看详情","溯源通知", 1),
    
    // 工单相关通知
    TICKET_ASSIGNED("您有新的工单待处理，工单编号：%s，标题：%s，请及时处理", "工单通知", 2),
    TICKET_CONFIRMED("您的工单（编号：%s）已被客服确认接收，正在处理中", "工单通知", 1),
    TICKET_REJECTED("工单（编号：%s）已被重新分配，原因：%s", "工单通知", 1),
    TICKET_REPLIED("您的工单（编号：%s）有新的回复，请查看", "工单通知", 1),
    TICKET_USER_CONFIRMED("工单（编号：%s）已确认解决", "工单通知", 1),
    TICKET_USER_FOLLOWUP("用户对工单（编号：%s）有新的追问，请及时处理", "工单通知", 2),
    TICKET_TIMEOUT_REJECT("工单（编号：%s）超时未确认，已自动重新分配", "工单通知", 2),
    TICKET_AUTO_CLOSED("您的工单（编号：%s）因长时间未确认已自动关闭", "工单通知", 1);

    private final String content;

    private final String summary;

    private final Integer level;
}
