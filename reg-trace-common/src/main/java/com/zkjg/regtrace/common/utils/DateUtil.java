package com.zkjg.regtrace.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/16 8:45
 */
public class DateUtil {

    private static final ZoneId SHANGHAI_ZONE = ZoneId.of("Asia/Shanghai");


    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_PATTERN = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    public static DateTimeFormatter YYYY_MM_DD_PATTERN = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static DateTimeFormatter HH_MM_SS_PATTERN = DateTimeFormatter.ofPattern("HH:mm:ss");

    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";



    public static String format(LocalDateTime dateTime) {
        return format(dateTime, YYYY_MM_DD_HH_MM_SS);
    }

    public static String format(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDateTime strToLocalDateTime(String str){
        if(StringUtils.isNotEmpty(str)){
            // 定义格式器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

            // 字符串转LocalDateTime
            return LocalDateTime.parse(str, formatter);
        }
        return null;
    }


    /**
     * localDateTime 转成想要的时间格式
     *
     * @param localDateTime 输入的 LocalDateTime
     * @param pattern 格式化的模式
     * @return 格式化后的时间字符串
     */
    public static String localDateTimeToStr(LocalDateTime localDateTime, String pattern) {
        if (Objects.isNull(localDateTime) || StringUtils.isBlank(pattern)) {
            return Strings.EMPTY;
        }
        return DateTimeFormatter.ofPattern(pattern)
                .withZone(SHANGHAI_ZONE)
                .format(localDateTime.atZone(SHANGHAI_ZONE).toInstant());
    }
}
