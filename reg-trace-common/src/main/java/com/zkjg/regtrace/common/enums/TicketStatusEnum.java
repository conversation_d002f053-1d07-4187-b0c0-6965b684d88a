package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TicketStatusEnum {

    PENDING_ASSIGNMENT(0, "待分配"),
    PENDING_CONFIRMATION(1, "待客服确认"),
    IN_PROGRESS(2, "处理中"),
    PENDING_USER_CONFIRMATION(3, "待用户确认"),
    CLOSED(4, "已关闭");

    private final Integer code;
    private final String name;

    public static TicketStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TicketStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
