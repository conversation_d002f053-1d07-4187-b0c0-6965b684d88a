package com.zkjg.regtrace.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.zkjg.regtrace.common.enums.OperationLogType;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 10:14
 */
public class ExcelConvert implements Converter<String> {
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        if (value == null || contentProperty == null || contentProperty.getField() == null) {
            return new WriteCellData<>("");
        }

        String fieldName = contentProperty.getField().getName();
        if (fieldName.equals("operateLogType")) {
            String descriptionByCode = OperationLogType.getNameByCode(value);
            assert descriptionByCode != null;
            return new WriteCellData<>(descriptionByCode);
        }
        return new WriteCellData<>("");
    }
}
