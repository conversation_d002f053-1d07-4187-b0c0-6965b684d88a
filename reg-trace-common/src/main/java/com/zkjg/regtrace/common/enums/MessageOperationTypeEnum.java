package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum MessageOperationTypeEnum {

    MESSAGE_DELETE("MESSAGE_DELETE", "删除归档"),
    MESSAGE_EXPORT("MESSAGE_EXPORT", "导出文件"),
    MESSAGE_MARK("MESSAGE_MARK", "标记已读");

    private final String type;
    private final String description;

    public static MessageOperationTypeEnum fromType(String type) {
        for (MessageOperationTypeEnum value : values()) {
            if (value.type.equalsIgnoreCase(type)) {
                return value;
            }
        }
        return null;
    }
}
