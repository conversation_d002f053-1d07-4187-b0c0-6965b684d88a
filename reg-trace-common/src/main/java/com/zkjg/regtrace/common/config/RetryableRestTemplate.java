package com.zkjg.regtrace.common.config;

import org.apache.http.NoHttpResponseException;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.retry.RetryException;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

public class RetryableRestTemplate extends RestTemplate {

    private final RetryTemplate retryTemplate;

    public RetryableRestTemplate(ClientHttpRequestFactory factory, RetryTemplate retryTemplate) {
        super(factory);
        this.retryTemplate = retryTemplate;
    }

    // 重写需要添加重试逻辑的方法，例如 getForObject、postForObject 等
    @Override
    public <T> T postForObject(String url, Object request, Class<T> responseType, Object... uriVariables) throws ResourceAccessException {
        try {
            return retryTemplate.execute(context -> super.postForObject(url, request, responseType, uriVariables));
        } catch (ResourceAccessException e) {
            // 如果是 NoHttpResponseException 异常，则进行重试
            if (e.getCause() instanceof NoHttpResponseException) {
                throw new RetryException("Failed to respond, retrying...", e);
            } else {
                throw e; // 其他类型的异常直接抛出
            }
        }
    }
}
