package com.zkjg.regtrace.common;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description
 * @create 2024/6/4 16:25
 */
@Getter
@Setter
public abstract class BaasQuery {

    public static String EQUAL = "Eq";

    public static String NOT_EQUAL = "NotEq";

    public static String LIKE = "Like";

    public static String NOT_LIKE = "NotLike";

    public static String LESS_THAN = "Lt";

    public static String LESS_THAN_EQUAL = "LtEq";

    public static String GREATER_THAN = "Gt";

    public static String GREATER_THAN_EQUAL = "GtEq";

    public static String IN = "In";

    public static String IN_OR_NOT = "InOrNot";

    public static String NOT_IN = "NotIn";

    public static String IS_NULL = "IsNull";

    public static String IS_NOT_NULL = "IsNotNull";

    public static String OR = "Or";

}
