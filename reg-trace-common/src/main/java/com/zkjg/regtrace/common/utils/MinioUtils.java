package com.zkjg.regtrace.common.utils;


import com.zkjg.regtrace.common.config.MinioConfig;
import com.zkjg.regtrace.common.enums.RetCodeEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import io.minio.PutObjectArgs;
import io.minio.StatObjectArgs;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * minio文件上传工具类
 * <AUTHOR>
 * @Date 2024/1/5 9:29
 */
@Component
@Slf4j
public class MinioUtils {

    @Resource
    private MinioConfig minioConfig;

    public String uploadFile(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BusinessException(RetCodeEnum.FILE_NAME_NOT_NULL);
        }
        String fileName = null;
        String path = minioConfig.getObjectName() + "/" + originalFilename;
        boolean isFileExists = false;
        try {
            minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object("/"+path).build()
            );
            isFileExists = true;
        } catch (Exception e) {
            log.info("file not exist");
        }

        if(isFileExists){
            // 给文件名加一个随机的后最
            String suffix = originalFilename.substring(originalFilename.lastIndexOf(
                    "."));

            String prefix = originalFilename.substring(0,originalFilename.lastIndexOf(
                    "."));
            fileName = prefix+"_"+ RandomStringUtils.randomAlphabetic(3)+suffix;
            path = minioConfig.getObjectName() + "/" + fileName;
        }

        try (InputStream stream = file.getInputStream()) {
            minioConfig.getMinioClient().putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(path)
                            .contentType(file.getContentType())
                            .stream(stream, stream.available(), -1)
                            .build()
            );
        } catch (Exception e) {
            log.error("文件流上传错误,异常信息为：{}", ExceptionUtils.getStackTrace(e));
            throw new BusinessException("上传失败，请稍后重试");
        }
        return minioConfig.getReadUrl() + "/" + minioConfig.getBucketName() + "/" + path;
    }

    public void upload(String bucketName, String fileName, MultipartFile file) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            PutObjectArgs objectArgs = PutObjectArgs.builder().bucket(bucketName).object(fileName).stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType()).build();
            //文件名称相同会覆盖
            minioConfig.getMinioClient().putObject(objectArgs);
        } catch (Exception e) {
            log.error("MinioUtils upload Exception，bucketName:{},filename:{}", bucketName, fileName, e);
            throw new BusinessException(String.format("bucketName:[%s],fileName:[%s],MinioUtils upload  exception [%s]", bucketName, fileName, e.getMessage()));
        } finally {
            if (Objects.nonNull(inputStream)) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("MinioUtils upload close inputStream IOException，bucketName:{},filename:{}", bucketName, fileName, e);
                }
            }
        }
    }
}
