package com.zkjg.regtrace.common.annotation;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @description
 * @create 2024/10/24 8:43
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = InterfaceInterceptValidator.class)
@Documented
public @interface InterfaceIntercept {
    String message() default "Invalid role source value";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<?> clazz();  // 指定接口类

    @Target({ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @interface List {
        InterfaceIntercept[] value();
    }
}

class InterfaceInterceptValidator implements ConstraintValidator<InterfaceIntercept, Object> {

    private Class<?> clazz;

    @Override
    public void initialize(InterfaceIntercept annotation) {
        this.clazz = annotation.clazz();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;  // 如果值为 null，跳过校验
        }
        try {
            // 获取接口中所有的常量字段
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if (field.getType().equals(Integer.class) || field.getType().equals(int.class) || field.getType().equals(String.class)) {
                    // 检查传入的值是否和常量值匹配
                    if (value.equals(field.get(null))) {
                        return true;
                    }
                }
            }
        } catch (IllegalAccessException e) {
            return false;
        }
        return false;
    }
}