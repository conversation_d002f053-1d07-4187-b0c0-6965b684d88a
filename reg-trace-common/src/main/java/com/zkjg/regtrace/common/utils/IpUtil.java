package com.zkjg.regtrace.common.utils;

import cn.hutool.core.util.ObjectUtil;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import net.dreamlu.mica.ip2region.core.IpInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.StringTokenizer;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 */

public class IpUtil {
    private static final Logger log = LoggerFactory.getLogger(IpUtil.class);

    // IPv4正则匹配
    public static final String _255 = "(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)";
    public static final Pattern IPV4_PATTERN = Pattern.compile("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    /**
     * 获取客户端IP地址（自动处理代理）
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (isInvalidIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (isInvalidIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (isInvalidIp(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
                ip = getLocalHostIp();
            }
        }

        // 处理多级代理IP
        if (ip != null && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }
        return ip;
    }

    private static boolean isInvalidIp(String ip) {
        return ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip);
    }

    private static String getLocalHostIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            return "127.0.0.1";
        }
    }

    /**
     * IP地址转long
     */
    public static long ipToLong(String ip) {
        if (!isIPv4Valid(ip)) {
            throw new BusinessException("Invalid IPv4 address: " + ip);
        }
        String[] octets = ip.split("\\.");
        return (Long.parseLong(octets[0]) << 24) +
                (Long.parseLong(octets[1]) << 16) +
                (Long.parseLong(octets[2]) << 8) +
                Long.parseLong(octets[3]);
    }

    /**
     * long转IP地址
     */
    public static String longToIp(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
                ((ip >> 16) & 0xFF) + "." +
                ((ip >> 8) & 0xFF) + "." +
                (ip & 0xFF);
    }

    /**
     * 验证IPv4有效性
     */
    public static boolean isIPv4Valid(String ip) {
        return ip != null && IPV4_PATTERN.matcher(ip).matches();
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (!isIPv4Valid(ip)) return false;

        long ipNum = ipToLong(ip);
        return (ipNum >= ipToLong("10.0.0.0") && ipNum <= ipToLong("**************")) ||
                (ipNum >= ipToLong("**********") && ipNum <= ipToLong("**************")) ||
                (ipNum >= ipToLong("***********") && ipNum <= ipToLong("***************")) ||
                ip.equals("127.0.0.1");
    }




    /**
     * 判断是否为内网IP（别名方法）
     */
    public static boolean internalIp(String ip) {
        return isInternalIp(ip);
    }

    /**
     * 判断是否为IPv4私有地址
     */
    public static boolean isIPv4Private(String ip) {
        return isInternalIp(ip);
    }

    /**
     * 从请求中获取IP地址（别名方法）
     */
    public static String getIpFromRequest(HttpServletRequest request) {
        return getIp(request);
    }

    /**
     * 获取本机IP地址
     */
    public static String getHostIp() {
        return getLocalHostIp();
    }

    /**
     * 获取本机主机名
     */
    public static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.error("获取主机名失败", e);
            return "unknown";
        }
    }


}
