package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 使用二级菜单做操作类型
 */
@AllArgsConstructor
@Getter
public enum OperationLogType {

    REGISTER("REGISTER", "作品登记", ""),

    // 作品创作登记
    DATA_STATISTICS("DATA_STATISTICS", "数据统计", "/workRegistry/dataOverview"),
    INFO_TRACE("INFO_TRACE", "信息追踪", "/workRegistry/infoTrace"),
    CONTENT_MANAGEMENT_WORK("CONTENT_MANAGEMENT_WORK", "内容管理", "/workRegistry/content"),
    REGISTRATION_RECORD("REGISTRATION_RECORD", "登记记录", "/workRegistry/record"),
    TEMPLATE_MANAGEMENT("TEMPLATE_MANAGEMENT", "水印模版", "/workRegistry/template"),
    WATERMARK_MANAGEMENT("WATERMARK_MANAGEMENT", "水印管理", "/workRegistry/watermark"),

    // 作品溯源认证
    TRACE_RECORD("TRACE_RECORD", "溯源记录", "/workTrace/record"),
    CONDITIONAL_TRACE("CONDITIONAL_TRACE", "条件溯源", "/workTrace/conditionQuery"),
    TRACE_DATA_OVERVIEW("TRACE_DATA_OVERVIEW", "数据概览", "/workTrace/dataOverview"),

    // 平台运营
    REGISTRY_REVIEW("REGISTRY_REVIEW", "登记审核", "/platform/registry"),
    TRACE_CHAIN("TRACE_CHAIN", "溯源管理", "/platform/traceChain"),
    EXCEPTION_DETECTION("EXCEPTION_DETECTION", "异常检测", "/platform/exception"),
    APPLICATION_MANAGEMENT("APPLICATION_MANAGEMENT", "应用管理", "/platform/application"),
    OPERATION_STATISTICS("OPERATION_STATISTICS", "运营统计", "/platform/statistics"),

    // 平台运维人员导航
    NOTIFICATION_MANAGEMENT("NOTIFICATION_MANAGEMENT", "通知管理", "/opsNav/notificationManagement"),
    USER_MANAGEMENT_OPS_NAV("USER_MANAGEMENT_OPS_NAV", "用户管理", "/opsNav/userManagement"),
    TRACE_MANAGEMENT_OPS("TRACE_MANAGEMENT_OPS", "溯源管理", "/opsNav/traceManagement"),
    CONTENT_MANAGEMENT_OPS("CONTENT_MANAGEMENT_OPS", "内容管理", "/opsNav/contentManagement"),
    DATA_ANALYSIS("DATA_ANALYSIS", "数据分析", "/opsNav/dataAnalysis"),

    // 平台运维人员门户
    LOG_MANAGEMENT("LOG_MANAGEMENT", "日志管理", "https://www.baidu.com/3"),
    SYSTEM_MONITORING("SYSTEM_MONITORING", "系统状态监控服务", "https://www.baidu.com/2"),
    SYSTEM_MANAGEMENT_PORTAL("SYSTEM_MANAGEMENT_PORTAL", "系统管理", "/opsPortal/systemManagement"),
    DATA_SECURITY_SERVICE("DATA_SECURITY_SERVICE", "数据安全服务", "https://www.baidu.com/1"),
    CONTAINER_SCHEDULER("CONTAINER_SCHEDULER", "系统容器编排服务", "https://procomponents.ant.design/components/field-set#proformradiogroup"),
    TASK_SCHEDULER("TASK_SCHEDULER", "系统任务调度服务", "/opsPortal/systemTask"),
    FUNCTION_CONFIG("FUNCTION_CONFIG", "功能配置管理", "/opsPortal/functionConfig"),

    // 运维管理
    SYSTEM_MANAGEMENT_OPS("SYSTEM_MANAGEMENT_OPS", "系统管理", "https://www.baidu.com/6"),
    CUSTOMER_SERVICE("CUSTOMER_SERVICE", "客服中心", "/opsManage/customerService"),
    HELP_CENTER("HELP_CENTER", "帮助中心", "/opsManage/helpCenter"),
    SECURITY_MANAGEMENT("SECURITY_MANAGEMENT", "安全管理", "/opsManage/security"),
    USER_CENTER("USER_CENTER", "用户中心", "/opsManage/userCenter"),
    RESOURCE_PLATFORM("RESOURCE_PLATFORM", "资源管理平台", "https://www.baidu.com/5"),
    MONITORING_STATUS("MONITORING_STATUS", "监控状态", "https://www.baidu.com/4"),

    // 运营人员导航
    PROBLEM_HANDLING("PROBLEM_HANDLING", "问题处理", "/operationNav/problem"),
    PROBLEM_MANAGEMENT("PROBLEM_MANAGEMENT", "问题管理", "/operationNav/problemManagement"),
    CONTENT_REVIEW("CONTENT_REVIEW", "登记内容审查", "/operationNav/contentReview"),
    OPERATION_DASHBOARD("OPERATION_DASHBOARD", "运营数据展示板", "/operationNav/operationData"),

    // 权限管理
    ROLE_MANAGEMENT("ROLE_MANAGEMENT", "角色管理", "/access/role"),
    MENU_MANAGEMENT("MENU_MANAGEMENT", "菜单管理", "/access/menu"),
    USER_MANAGEMENT("USER_MANAGEMENT", "用户管理", "/access/user"),

    //消息中心
    MESSAGE_OPERATE("MESSAGE_OPERATE", "消息操作", ""),
    MESSAGE_DETAIL("MESSAGE_DETAIL", "查看消息", ""),
    MESSAGE_DELETE("MESSAGE_DELETE", "删除归档", ""),
    MESSAGE_EXPORT("MESSAGE_EXPORT", "导出文件", ""),
    MESSAGE_MARK("MESSAGE_MARK", "标记已读", "");

    private final String code;
    private final String name;
    private final String routeUrl;

    public static String getNameByCode(String code) {
        for (OperationLogType type : values()) {
            if (type.code.equals(code)) {
                return type.name;
            }
        }
        return null;
    }
}
