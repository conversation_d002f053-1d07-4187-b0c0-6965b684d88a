package com.zkjg.regtrace.common.exceptions;


import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.enums.RetCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.List;

/**
 * 统一的异常处理
 *
 * @Author:zmhao
 * @Date: 16:45 2024/6/5
 */
@Slf4j
@ControllerAdvice
public class ControllerExceptionHandler {

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public Result<Void> exceptionHandler(Exception e) {

        // 业务异常处理
        if (e instanceof BusinessException) {
            BusinessException businessEx = (BusinessException) e;
            RetCodeEnum retCodeEnum = businessEx.getRetCodeEnum();

            if (retCodeEnum != null) {
                log.error("业务异常:{}", businessEx.getErrorMessage());
                return Result.ofFail(retCodeEnum.getCode(), businessEx.getErrorMessage());
            } else {
                log.error("业务异常:{}", businessEx.getMessage());
                return Result.ofFailMsg(businessEx.getMessage());
            }
        }

        // 请求参数读取失败或参数类型不匹配
        if (e instanceof HttpMessageNotReadableException || e instanceof MethodArgumentTypeMismatchException) {
            log.error("参数异常: {}", e.getMessage());
            return Result.ofFailMsg("请求参数错误");
        }

        // 请求方式不支持
        if (e instanceof HttpRequestMethodNotSupportedException) {
            log.warn("请求方式异常: {}", e.getMessage());
            return Result.ofFailMsg("Request method not supported");
        }

        // 参数校验失败
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validEx = (MethodArgumentNotValidException) e;
            List<ObjectError> allErrors = validEx.getBindingResult().getAllErrors();
            String defaultMessage = allErrors.isEmpty() ? "参数校验失败" : allErrors.get(0).getDefaultMessage();
            log.warn("参数校验失败 {}", defaultMessage);
            return Result.ofFailMsg(defaultMessage);
        }

        // 未知异常 - 打印详细错误日志
        log.error("系统异常 未处理的异常: {}", e.getMessage(), e);

        // 获取异常原因的 message
        String message = e.getCause() != null ? e.getCause().getMessage() : e.getMessage();
        return Result.ofFailMsg(StringUtils.defaultString(message, "系统异常"));
    }

}
