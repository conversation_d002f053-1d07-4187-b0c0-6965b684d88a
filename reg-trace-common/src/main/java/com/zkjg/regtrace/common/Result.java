

package com.zkjg.regtrace.common;

import com.zkjg.regtrace.common.enums.RetCodeEnum;
import lombok.Data;

@Data
public class Result<T> {

    private Integer code;
    private String msg;
    private boolean success;
    private T data;


    public static <T> Result<T> ofSuccess(T data) {
        return new Result<T>().setCode(RetCodeEnum.SUCCESS.getCode()).setSuccess(true).setMsg(RetCodeEnum.SUCCESS.getMsg()).setData(data);
    }

    public static <T> Result<T> ofSuccess() {
        return new Result<T>().setCode(RetCodeEnum.SUCCESS.getCode()).setSuccess(true).setMsg(RetCodeEnum.SUCCESS.getMsg());
    }

    public static <T> Result<T> ofFailMsg(String msg) {
        return new Result<T>().setSuccess(false).setCode(RetCodeEnum.FAIL.getCode()).setMsg(msg);
    }

    public static <T> Result<T> ofFailMsg(String msg, T data) {
        return new Result<T>().setSuccess(false).setMsg(msg).setData(data);
    }

    public static <T> Result<T> ofSuccessMsg(String msg) {
        return new Result<T>().setSuccess(true).setCode(RetCodeEnum.SUCCESS.getCode()).setMsg(msg);
    }

    public static <T> Result<T> ofFail(int code, String msg) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }


    public static <T> Result<T> ofFail(RetCodeEnum retCodeEnum) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(retCodeEnum.getCode());
        result.setMsg(retCodeEnum.getMsg());
        return result;
    }

    public static <T> Result<T> ofFail(RetCodeEnum retCodeEnum, T data) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(retCodeEnum.getCode());
        result.setMsg(retCodeEnum.getMsg());
        result.setData(data);
        return result;
    }

    public static <T> Result<T> ofThrowable(int code, Throwable throwable) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMsg(throwable.getClass().getName() + ", " + throwable.getMessage());
        return result;
    }

    /**
     * 判断result是否失败
     *
     * @return true:失败 false:成功
     */
    public boolean isFail() {
        return !success;
    }

    public Result<T> setSuccess(boolean success) {
        this.success = success;
        return this;
    }

    public int getCode() {
        return code;
    }

    public Result<T> setCode(int code) {
        this.code = code;
        return this;
    }

    public Result<T> setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public Result<T> setData(T data) {
        this.data = data;
        return this;
    }

    @Override
    public String toString() {
        return "Result{" + "success=" + success + ", code=" + code + ", msg='" + msg + '\'' + ", data=" + data + '}';
    }


}
