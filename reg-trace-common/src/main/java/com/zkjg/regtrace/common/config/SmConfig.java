package com.zkjg.regtrace.common.config;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description sm2解密
 * @create 2024/6/20 9:09
 */
@Configuration
@ConditionalOnProperty(prefix = "external.secrets.sm2", name = "privateKey")
public class SmConfig {

    @Value("${external.secrets.sm2.privateKey}")
    private String privateKey;

    @Bean(name = "sm2Client")
    public SM2 smConfig() {
        return SmUtil.sm2(privateKey, null);
    }

    public static void generate() {
        SM2 sm2 = SmUtil.sm2();
        // 私钥
        String privateKeyD = HexUtil.encodeHexStr(BCUtil.encodeECPrivateKey(sm2.getPrivateKey()));
        // 公钥
        String publicKeyQ = HexUtil.encodeHexStr(((BCECPublicKey) sm2.getPublicKey()).getQ().getEncoded(false));

        System.out.println("公钥：" + publicKeyQ);
        System.out.println("私钥：" + privateKeyD);

        SM2 smPrivate = SmUtil.sm2(privateKeyD, null);
        SM2 smPublic = SmUtil.sm2(null, publicKeyQ);

        String plaintext = "待加密字符串";
        // 加密
        byte[] encryptedData = smPublic.encrypt(plaintext.getBytes(), KeyType.PublicKey);
        String encryptedHex = HexUtil.encodeHexStr(encryptedData);
        System.out.println("加密后" + encryptedHex);

        // 解密byte[]
        byte[] decryptedData = smPrivate.decrypt(encryptedData, KeyType.PrivateKey);
        String decryptedText = new String(decryptedData);
        System.out.println("byte解密: " + decryptedText);

        //解密String
        String decryptedText2 = smPrivate.decryptStr(encryptedHex, KeyType.PrivateKey);
        System.out.println("String解密: " + decryptedText2);
    }

    public static void main(String[] args) {
        generate();
    }
}
