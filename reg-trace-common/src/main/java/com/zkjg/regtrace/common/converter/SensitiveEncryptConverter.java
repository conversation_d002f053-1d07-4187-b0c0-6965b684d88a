package com.zkjg.regtrace.common.converter;

import com.zkjg.regtrace.common.utils.AesUtil;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 17:04
 */
@Converter
public class SensitiveEncryptConverter implements AttributeConverter<String, String> {
    @Override
    public String convertToDatabaseColumn(String s) {
        return StringUtils.isEmpty(s) ? "" : AesUtil.encrypt(s);
    }

    @Override
    public String convertToEntityAttribute(String s) {
        return StringUtils.isEmpty(s) ? "" : AesUtil.decrypt(s);
    }
}
