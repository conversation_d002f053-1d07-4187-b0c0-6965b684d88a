package com.zkjg.regtrace.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置
 * <AUTHOR>
 */
@Configuration
@EnableCaching
@Slf4j
public class CacheConfig {

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))  // 默认1小时过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();

        // 不同业务的缓存配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 用户信息缓存 - 30分钟
        cacheConfigurations.put("userInfo", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 权限信息缓存 - 15分钟
        cacheConfigurations.put("permissions", defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // 文档统计缓存 - 5分钟
        cacheConfigurations.put("documentStats", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        
        // 系统配置缓存 - 1小时
        cacheConfigurations.put("systemConfig", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 溯源结果缓存 - 24小时
        cacheConfigurations.put("traceResult", defaultConfig.entryTtl(Duration.ofHours(24)));

        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();

        log.info("Redis缓存管理器配置完成");
        return cacheManager;
    }
}
