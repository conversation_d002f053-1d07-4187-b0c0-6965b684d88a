package com.zkjg.regtrace.common.utils;

import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2025/6/13 17:25
 */
public class FileUtil {
    public static MultipartFile convertFileToMultipartFile(File file) throws IOException {
        FileInputStream inputStream = new FileInputStream(file);

        MultipartFile multipartFile = new MockMultipartFile(
                "file",
                file.getName(),
                "application/octet-stream",
                inputStream
        );
        inputStream.close();
        return multipartFile;
    }
}
