package com.zkjg.regtrace.common.constants;

/**
 * 缓存相关常量
 * <AUTHOR>
 */
public class CacheConstants {
    
    /**
     * 缓存名称
     */
    public static final String USER_INFO_CACHE = "userInfo";
    public static final String PERMISSIONS_CACHE = "permissions";
    public static final String DOCUMENT_STATS_CACHE = "documentStats";
    public static final String SYSTEM_CONFIG_CACHE = "systemConfig";
    public static final String TRACE_RESULT_CACHE = "traceResult";
    
    /**
     * 缓存键前缀
     */
    public static final String USER_INFO_KEY_PREFIX = "user:info:";
    public static final String USER_PERMISSIONS_KEY_PREFIX = "user:permissions:";
    public static final String DOCUMENT_VIEW_KEY_PREFIX = "document:view:";
    public static final String DOCUMENT_DOWNLOAD_KEY_PREFIX = "document:download:";
    
    /**
     * 缓存过期时间（秒）
     */
    public static final int USER_INFO_EXPIRE = 1800;        // 30分钟
    public static final int PERMISSIONS_EXPIRE = 900;       // 15分钟
    public static final int DOCUMENT_STATS_EXPIRE = 300;    // 5分钟
    public static final int SYSTEM_CONFIG_EXPIRE = 3600;    // 1小时
    public static final int TRACE_RESULT_EXPIRE = 86400;    // 24小时
    
    private CacheConstants() {
        // 工具类，禁止实例化
    }
}
