package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单交流记录类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CommunicationTypeEnum {

    USER_SUBMIT(1, "用户提交"),
    CS_CONFIRM(2, "客服确认接收"),
    CS_REJECT(3, "客服拒绝接收"),
    CS_REPLY(4, "客服回复"),
    USER_FOLLOW_UP(5, "用户追问"),
    USER_CONFIRM(6, "用户确认"),
    SYSTEM_OPERATION(7, "系统操作");

    private final Integer code;
    private final String name;

    public static CommunicationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CommunicationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
