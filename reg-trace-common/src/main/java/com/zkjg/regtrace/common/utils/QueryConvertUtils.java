package com.zkjg.regtrace.common.utils;


import com.alibaba.fastjson2.JSONArray;
import com.zkjg.regtrace.common.BaasQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @description jpa Specification 构建类
 * @create 2024/6/4 16:16
 */
@Slf4j
@SuppressWarnings("unchecked, rawtypes")
public class QueryConvertUtils {

    public static <T> Specification<T> toSpecification(BaasQuery baasQuery) {

        return (Specification<T>) (root, query, cb) -> {
            List<Predicate> predicates = new LinkedList<>();
            Field[] fields = baasQuery.getClass().getDeclaredFields();
            try {
                for (Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    Object fieldValue = field.get(baasQuery);
                    if (fieldValue == null || (fieldValue instanceof String && StringUtils.isEmpty((String) fieldValue))) {
                        continue;
                    }

                    // 如果包含 Or
                    if (fieldName.contains(BaasQuery.OR)) {
                        Predicate orPredicate = resolveOrField(fieldName, fieldValue, root, cb);
                        if (orPredicate != null) {
                            predicates.add(orPredicate);
                            continue;
                        }
                    }

                    if (fieldName.endsWith(BaasQuery.NOT_EQUAL)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.NOT_EQUAL);
                        predicates.add(cb.notEqual(root.get(colName), fieldValue));
                    } else if (fieldName.endsWith(BaasQuery.NOT_LIKE)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.NOT_LIKE);
                        predicates.add(cb.notLike(root.get(colName), convertLikeParams(fieldValue)));
                    } else if (fieldName.endsWith(BaasQuery.LESS_THAN)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.LESS_THAN);
                        predicates.add(cb.lessThan(root.get(colName), (Comparable) fieldValue));
                    } else if (fieldName.endsWith(BaasQuery.GREATER_THAN)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.GREATER_THAN);
                        predicates.add(cb.greaterThan(root.get(colName), (Comparable) fieldValue));
                    } else if (fieldName.endsWith(BaasQuery.LESS_THAN_EQUAL)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.LESS_THAN_EQUAL);
                        predicates.add(cb.lessThanOrEqualTo(root.get(colName), (Comparable) fieldValue));
                    } else if (fieldName.endsWith(BaasQuery.GREATER_THAN_EQUAL)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.GREATER_THAN_EQUAL);
                        predicates.add(cb.greaterThanOrEqualTo(root.get(colName), (Comparable) fieldValue));
                    } else if (fieldName.endsWith(BaasQuery.NOT_IN)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.NOT_IN);
                        predicates.add(cb.not(root.get(colName).in(convertInParams(fieldValue))));
                    } else if (fieldName.endsWith(BaasQuery.IN_OR_NOT)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.IN);
                        Predicate in = root.get(colName).in(convertInParams(fieldValue));
                        Predicate cbNull = cb.isNull(root.get(colName));
                        predicates.add(cb.or(in, cbNull));
                    } else if (fieldName.endsWith(BaasQuery.IN)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.IN);
                        predicates.add(root.get(colName).in(convertInParams(fieldValue)));
                    } else if (fieldName.endsWith(BaasQuery.IS_NULL)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.IS_NULL);
                        predicates.add(cb.isNull(root.get(colName)));
                    } else if (fieldName.endsWith(BaasQuery.IS_NOT_NULL)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.IS_NOT_NULL);
                        predicates.add(cb.isNotNull(root.get(colName)));
                    } else if (fieldName.endsWith(BaasQuery.EQUAL)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.EQUAL);
                        predicates.add(cb.equal(root.get(colName), fieldValue));
                    } else if (fieldName.endsWith(BaasQuery.LIKE)) {
                        String colName = StringUtils.substringBeforeLast(fieldName, BaasQuery.LIKE);
                        predicates.add(cb.like(root.get(colName), convertLikeParams(fieldValue)));
                    }
                }
            } catch (Exception e) {
                log.error("convert query object failed",e);
                throw new RuntimeException("convert query object failed, maybe you should redesign your query object!");
            }
            return query.where(predicates.toArray(new Predicate[0])).getRestriction();
        };
    }

    private static Predicate resolveOrField(String fieldName, Object fieldValue, Root<?> root, CriteriaBuilder cb) {
        String[] ops = {BaasQuery.EQUAL, BaasQuery.LIKE}; // 支持的操作符
        for (String op : ops) {
            String connector = op + BaasQuery.OR;
            if (fieldName.contains(connector) && fieldName.endsWith(op)) {
                String trimmed = StringUtils.substringBeforeLast(fieldName, op);
                String[] fieldParts = trimmed.split(connector);

                if (fieldParts.length < 2) return null;

                List<Predicate> orConditions = new ArrayList<>();
                for (int i = 0; i < fieldParts.length; i++) {
                    String field = i == 0 ? fieldParts[i] : decapitalize(fieldParts[i]);
                    Path<String> path = root.get(field);
                    Predicate p;
                    if (BaasQuery.EQUAL.equals(op)) {
                        p = cb.equal(path, fieldValue);
                    } else if (BaasQuery.LIKE.equals(op)) {
                        p = cb.like(path, convertLikeParams(fieldValue));
                    } else {
                        continue;
                    }
                    orConditions.add(p);
                }
                return cb.or(orConditions.toArray(new Predicate[0]));
            }
        }
        return null;
    }

    private static String decapitalize(String str) {
        if (StringUtils.isBlank(str)) return str;
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    private static String convertLikeParams(Object o) {
        String s = (String) o;
        if (!s.startsWith("%")) {
            s = "%" + s;
        }
        if (!s.endsWith("%")) {
            s = s + "%";
        }
        return s;
    }

    private static Object[] convertInParams(Object o) {
        return JSONArray.parseArray(JSONArray.toJSONString(o)).toArray();
    }
}
