package com.zkjg.regtrace.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 文件路径注解
 * 标记在字段上，表示该字段需要添加完整的文件访问路径前缀
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FullFilePath {
    
    /**
     * 是否启用，默认为true
     */
    boolean enabled() default true;
    
    /**
     * 自定义前缀，如果不指定则使用默认的MinIO配置
     */
    String prefix() default "";
}
