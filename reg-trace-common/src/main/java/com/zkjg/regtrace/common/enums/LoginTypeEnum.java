package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 10:01
 */
@AllArgsConstructor
@Getter
public enum LoginTypeEnum {
    ACCOUNT_PASSWORD("account_password", "账户密码"),
    EMAIL("email", "邮箱"),
    PHONE("phone", "手机号"),
    WECHAT("wechat", "微信");

    private final String type;
    private final String description;
}
