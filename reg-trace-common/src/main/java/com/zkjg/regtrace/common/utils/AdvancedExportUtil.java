package com.zkjg.regtrace.common.utils;

import com.itextpdf.text.Font;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;

import java.io.*;
import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 增强版通用导出工具，支持多数据源和复杂对象的导出
 */
public class AdvancedExportUtil {

    /**
     * 支持的导出格式
     */
    public enum ExportFormat {
        EXCEL, CSV, PDF
    }

    /**
     * 导出字段定义
     */
    @Data
    @Builder
    public static class ExportFieldDefinition {
        /**
         * 字段标题（显示名称）
         */
        private String title;

        /**
         * 字段值提取函数
         */
        private Function<Object, Object> valueExtractor;

        /**
         * 值转换器
         */
        private ValueConverter<Object> valueConverter;

        /**
         * 字段宽度（仅EXCEL有效）
         */
        private Integer width;
    }

    /**
     * 字段值转换器接口
     */
    @FunctionalInterface
    public interface ValueConverter<T> {
        /**
         * 转换字段值
         * @param value 原始值
         * @return 转换后的值
         */
        String convert(T value);
    }



    /**
     * 导出字段注解
     */
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.FIELD)
    public @interface ExportField {
        /**
         * 导出列标题
         */
        String value();

        /**
         * 转换器类型，指定使用哪种转换器
         * 为空时不进行转换
         */
        String converter() default "";

        /**
         * 字段宽度（仅Excel有效）
         */
        int width() default 0;
    }

    /**
     * 忽略导出字段注解
     */
    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.FIELD)
    public @interface ExportIgnore {}

    /**
     * 存储已注册的值转换器
     */
    private static final Map<String, ValueConverter<?>> VALUE_CONVERTERS = new HashMap<>();

    /**
     * 注册一个值转换器
     * @param name 转换器名称
     * @param converter 转换器实现
     */
    public static void registerConverter(String name, ValueConverter<?> converter) {
        VALUE_CONVERTERS.put(name, converter);
    }

    /**
     * 注册状态转换器，方便将数字状态码转为文本描述
     * @param name 转换器名称
     * @param valueMap 状态映射表
     */
    public static void registerStatusConverter(String name, Map<Object, String> valueMap) {
        registerConverter(name, value -> {
            if (value == null) return "";
            String result = valueMap.get(value);
            return result != null ? result : value.toString();
        });
    }

    /**
     * 使用类定义导出数据
     * @param data 数据列表
     * @param clazz 类定义
     * @param format 导出格式
     * @param fileBaseName 文件名前缀
     * @return 导出文件
     */
    public static <T> ExportFile export(List<T> data, Class<T> clazz, ExportFormat format, String fileBaseName) throws Exception {
        List<Map<String, Object>> mappedData = data.stream()
                .map(item -> convertToMap(item, clazz))
                .collect(Collectors.toList());

        List<ExportFieldDefinition> fieldDefinitions = getFieldDefinitionsFromClass(clazz);

        return exportWithDefinitions(mappedData, fieldDefinitions, format, fileBaseName);
    }


    /**
     * 根据类定义生成字段定义列表
     */
    private static List<ExportFieldDefinition> getFieldDefinitionsFromClass(Class<?> clazz) {
        List<ExportFieldDefinition> definitions = new ArrayList<>();

        // 扫描当前类的直接字段
        List<Field> fields = getExportableFields(clazz);
        for (Field field : fields) {
            definitions.add(createFieldDefinition(field, field.getName()));
        }

        // 扫描嵌套对象字段
        scanNestedFields(clazz, "", definitions, new HashSet<>(), 0);

        return definitions;
    }

    /**
     * 递归扫描嵌套对象中的可导出字段
     * @param clazz 要扫描的类
     * @param pathPrefix 路径前缀
     * @param definitions 字段定义列表
     * @param visitedClasses 已访问的类（防止循环引用）
     * @param depth 当前深度（防止无限递归）
     */
    private static void scanNestedFields(Class<?> clazz, String pathPrefix,
                                       List<ExportFieldDefinition> definitions,
                                       Set<Class<?>> visitedClasses, int depth) {
        // 防止无限递归和循环引用
        if (depth > 3 || visitedClasses.contains(clazz)) {
            return;
        }

        visitedClasses.add(clazz);

        Field[] allFields = clazz.getDeclaredFields();
        for (Field field : allFields) {
            // 跳过已经标注了 @ExportField 的字段（已在上面处理）
            if (field.isAnnotationPresent(ExportField.class)) {
                continue;
            }

            // 跳过被忽略的字段
            if (field.isAnnotationPresent(ExportIgnore.class)) {
                continue;
            }

            // 检查是否是可能包含嵌套字段的对象类型
            Class<?> fieldType = field.getType();
            if (isNestedObjectType(fieldType)) {
                String nestedPath = pathPrefix.isEmpty() ? field.getName() : pathPrefix + "." + field.getName();

                // 递归扫描嵌套对象的字段
                List<Field> nestedFields = getExportableFields(fieldType);
                for (Field nestedField : nestedFields) {
                    String fullPath = nestedPath + "." + nestedField.getName();
                    definitions.add(createFieldDefinitionWithPath(nestedField, fullPath));
                }

                // 继续递归扫描更深层的嵌套
                scanNestedFields(fieldType, nestedPath, definitions, visitedClasses, depth + 1);
            }
        }
    }

    /**
     * 判断是否是需要扫描的嵌套对象类型
     */
    private static boolean isNestedObjectType(Class<?> type) {
        // 排除基本类型、包装类型、字符串、日期等
        if (type.isPrimitive() ||
            type.equals(String.class) ||
            type.equals(Integer.class) || type.equals(Long.class) ||
            type.equals(Double.class) || type.equals(Float.class) ||
            type.equals(Boolean.class) || type.equals(Byte.class) ||
            type.equals(Short.class) || type.equals(Character.class) ||
            java.util.Date.class.isAssignableFrom(type) ||
            java.time.temporal.Temporal.class.isAssignableFrom(type) ||
            java.util.Collection.class.isAssignableFrom(type) ||
            java.util.Map.class.isAssignableFrom(type) ||
            type.isArray()) {
            return false;
        }

        // 排除 java.* 和 javax.* 包下的类
        if (type.getName().startsWith("java.") || type.getName().startsWith("javax.")) {
            return false;
        }

        return true;
    }

    /**
     * 创建字段定义（用于直接字段）
     */
    private static ExportFieldDefinition createFieldDefinition(Field field, String fieldName) {
        ExportField annotation = field.getAnnotation(ExportField.class);

        String title = annotation != null ? annotation.value() : field.getName();
        int width = annotation != null ? annotation.width() : 0;
        String converterName = annotation != null ? annotation.converter() : "";

        ValueConverter<Object> converter = null;
        if (converterName != null && !converterName.isEmpty()) {
            converter = (ValueConverter<Object>) VALUE_CONVERTERS.get(converterName);
        }

        final Field finalField = field;

        return ExportFieldDefinition.builder()
                .title(title)
                .valueExtractor(obj -> {
                    try {
                        if (obj instanceof Map) {
                            Map<String, Object> map = (Map<String, Object>) obj;
                            return map.get(fieldName);
                        } else {
                            finalField.setAccessible(true);
                            return finalField.get(obj);
                        }
                    } catch (Exception e) {
                        return null;
                    }
                })
                .valueConverter(converter)
                .width(width > 0 ? width : null)
                .build();
    }

    /**
     * 创建带路径的字段定义（用于嵌套字段）
     */
    private static ExportFieldDefinition createFieldDefinitionWithPath(Field field, String path) {
        ExportField annotation = field.getAnnotation(ExportField.class);

        String title = annotation != null ? annotation.value() : field.getName();
        int width = annotation != null ? annotation.width() : 0;
        String converterName = annotation != null ? annotation.converter() : "";

        ValueConverter<Object> converter = null;
        if (converterName != null && !converterName.isEmpty()) {
            converter = (ValueConverter<Object>) VALUE_CONVERTERS.get(converterName);
        }

        return ExportFieldDefinition.builder()
                .title(title)
                .valueExtractor(obj -> {
                    try {
                        return getNestedPropertyValue(obj, path);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .valueConverter(converter)
                .width(width > 0 ? width : null)
                .build();
    }

    /**
     * 获取嵌套对象的属性值
     */
    private static Object getNestedPropertyValue(Object obj, String path) {
        if (obj == null || path == null || path.isEmpty()) {
            return null;
        }

        String[] properties = path.split("\\.");
        Object current = obj;

        for (String property : properties) {
            if (current == null) {
                return null;
            }

            // 如果当前对象是Map，直接从Map中获取值
            if (current instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) current;
                current = map.get(property);
            } else {
                try {
                    // 尝试使用getter方法
                    String getterName = "get" + property.substring(0, 1).toUpperCase() + property.substring(1);
                    Method getter = current.getClass().getMethod(getterName);
                    current = getter.invoke(current);
                } catch (Exception e) {
                    try {
                        // 尝试直接访问字段
                        Field field = current.getClass().getDeclaredField(property);
                        field.setAccessible(true);
                        current = field.get(current);
                    } catch (Exception ex) {
                        return null;
                    }
                }
            }
        }

        return current;
    }

    /**
     * 将对象转换为Map
     */
    private static <T> Map<String, Object> convertToMap(T item, Class<T> clazz) {
        Map<String, Object> map = new HashMap<>();
        try {
            List<Field> fields = getExportableFields(clazz);

            // 首先添加所有对象的直接属性
            Field[] allFields = item.getClass().getDeclaredFields();
            for (Field f : allFields) {
                try {
                    f.setAccessible(true);
                    Object value = f.get(item);
                    map.put(f.getName(), value);
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }

            // 处理导出字段，为每个字段计算正确的值
            for (Field field : fields) {
                String key = field.getName();
                Object value;

                // 对于普通字段，直接从对象获取值
                try {
                    field.setAccessible(true);
                    value = field.get(item);
                } catch (Exception e) {
                    value = null;
                }

                map.put(key, value);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return map;
    }

    /**
     * 使用自定义字段定义导出数据
     * @param data 数据列表
     * @param fieldDefinitions 字段定义列表
     * @param format 导出格式
     * @param fileBaseName 文件名前缀
     * @return 导出文件
     */
    public static ExportFile exportWithDefinitions(
            List<Map<String, Object>> data,
            List<ExportFieldDefinition> fieldDefinitions,
            ExportFormat format,
            String fileBaseName) throws Exception {

        switch (format) {
            case EXCEL:
                return exportExcelWithDefinitions(data, fieldDefinitions, fileBaseName + ".xlsx");
            case CSV:
                return exportCSVWithDefinitions(data, fieldDefinitions, fileBaseName + ".csv");
            case PDF:
                return exportPDFWithDefinitions(data, fieldDefinitions, fileBaseName + ".pdf");
            default:
                throw new BusinessException("Unsupported export format");
        }
    }

    /**
     * 使用自定义字段定义导出Excel
     */
    private static ExportFile exportExcelWithDefinitions(
            List<Map<String, Object>> data,
            List<ExportFieldDefinition> fieldDefinitions,
            String fileName) throws Exception {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");
        // 创建表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < fieldDefinitions.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(fieldDefinitions.get(i).getTitle());

            // 设置列宽
            Integer width = fieldDefinitions.get(i).getWidth();
            if (width != null && width > 0) {
                sheet.setColumnWidth(i, width * 256); // 转换为Excel单位
            }
        }

        // 填充数据
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            Map<String, Object> item = data.get(i);
            for (int j = 0; j < fieldDefinitions.size(); j++) {
                Cell cell = row.createCell(j);
                ExportFieldDefinition fieldDef = fieldDefinitions.get(j);

                Object rawValue = fieldDef.getValueExtractor().apply(item);

                String displayValue = convertValue(rawValue, fieldDef.getValueConverter());

                cell.setCellValue(displayValue != null ? safeExcelValue(displayValue) : "");
            }
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        workbook.write(out);
        workbook.close();

        return new ExportFile(fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                new ByteArrayInputStream(out.toByteArray()));
    }

    /**
     * 使用自定义字段定义导出CSV
     */
    private static ExportFile exportCSVWithDefinitions(
            List<Map<String, Object>> data,
            List<ExportFieldDefinition> fieldDefinitions,
            String fileName) throws Exception {

        StringBuilder sb = new StringBuilder();

        // 创建表头
        for (int i = 0; i < fieldDefinitions.size(); i++) {
            sb.append(escapeCsv(fieldDefinitions.get(i).getTitle()));
            if (i != fieldDefinitions.size() - 1) sb.append(",");
        }
        sb.append("\n");

        // 填充数据
        for (Map<String, Object> item : data) {
            for (int i = 0; i < fieldDefinitions.size(); i++) {
                ExportFieldDefinition fieldDef = fieldDefinitions.get(i);

                Object rawValue = fieldDef.getValueExtractor().apply(item);
                String displayValue = convertValue(rawValue, fieldDef.getValueConverter());

                sb.append(escapeCsv(displayValue != null ? displayValue : ""));
                if (i != fieldDefinitions.size() - 1) sb.append(",");
            }
            sb.append("\n");
        }

        byte[] bytes = sb.toString().getBytes(StandardCharsets.UTF_8);
        return new ExportFile(fileName, "text/csv;charset=UTF-8", new ByteArrayInputStream(bytes));
    }

    /**
     * 转换字段值
     */
    @SuppressWarnings("unchecked")
    private static String convertValue(Object value, ValueConverter<Object> converter) {
        if (value == null) {
            return "";
        }

        if (converter != null) {
            return converter.convert(value);
        }

        return value.toString();
    }

    private static List<Field> getExportableFields(Class<?> clazz) {
        Field[] allFields = clazz.getDeclaredFields();
        List<Field> exportFields = new ArrayList<>();
        for (Field field : allFields) {
            // 检查是否有导出注解
            boolean hasExportAnnotation = field.isAnnotationPresent(ExportField.class);

            // 检查是否被明确忽略
            boolean shouldIgnore = field.isAnnotationPresent(ExportIgnore.class);

            // 只有标注了ExportField且没有被忽略的字段才会被导出
            if (hasExportAnnotation && !shouldIgnore) {
                field.setAccessible(true);
                exportFields.add(field);
            }
        }
        return exportFields;
    }

    private static String safeExcelValue(String value) {
        return value.matches("^[=\\-+@].*") ? "'" + value : value;
    }

    private static String escapeCsv(String value) {
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    /**
     * 使用自定义字段定义导出PDF
     */
    private static ExportFile exportPDFWithDefinitions(
            List<Map<String, Object>> data,
            List<ExportFieldDefinition> fieldDefinitions,
            String fileName) throws Exception {

        ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter.getInstance(document, pdfOutputStream);
        document.open();

        // 设置中文字体
        BaseFont baseFont;
        try {
            // 尝试使用系统中文字体
            baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            // 如果没有中文字体，使用默认字体
            baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
        }
        Font font = new Font(baseFont, 10, Font.NORMAL);
        Font headerFont = new Font(baseFont, 12, Font.BOLD);

        // 计算列数
        int columnCount = fieldDefinitions.size();
        if (columnCount == 0) {
            document.close();
            throw new BusinessException("No field definitions provided");
        }

        // 创建PDF表格
        PdfPTable table = new PdfPTable(columnCount);
        table.setWidthPercentage(100);

        // 添加表头
        for (ExportFieldDefinition fieldDef : fieldDefinitions) {
            PdfPCell headerCell = new PdfPCell(new Phrase(fieldDef.getTitle(), headerFont));
            headerCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            headerCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            table.addCell(headerCell);
        }

        // 添加数据行
        for (Map<String, Object> item : data) {
            for (ExportFieldDefinition fieldDef : fieldDefinitions) {
                Object rawValue = fieldDef.getValueExtractor().apply(item);
                String displayValue = convertValue(rawValue, fieldDef.getValueConverter());

                PdfPCell dataCell = new PdfPCell(new Phrase(displayValue != null ? displayValue : "", font));
                dataCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                table.addCell(dataCell);
            }
        }

        document.add(table);
        document.close();

        return new ExportFile(fileName, "application/pdf", new ByteArrayInputStream(pdfOutputStream.toByteArray()));
    }

    @Getter
    @RequiredArgsConstructor
    public static class ExportFile {
        private final String fileName;
        private final String contentType;
        private final InputStream inputStream;
    }
}

