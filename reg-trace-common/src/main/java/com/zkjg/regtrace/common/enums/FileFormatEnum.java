package com.zkjg.regtrace.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文件格式枚举
 * <AUTHOR>
 */
@Getter
public enum FileFormatEnum {
    MP3("mp3", "MP3音频格式", 0),
    WAV("wav", "WAV音频格式", 0),
    MP4("mp4", "MP4视频格式", 1),
    AVI("avi", "AVI视频格式", 1),
    MOV("mov", "MOV视频格式", 1),
    FLV("flv", "FLV视频格式", 1),
    WMV("wmv", "WMV视频格式", 1);

    private final String format;
    private final String desc;
    private final Integer fileType; // 0=音频，1=视频

    FileFormatEnum(String format, String desc, Integer fileType) {
        this.format = format;
        this.desc = desc;
        this.fileType = fileType;
    }

    /**
     * 根据格式名称获取枚举
     * @param format 格式名称
     * @return 枚举对象
     */
    public static FileFormatEnum getByFormat(String format) {
        if (format == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(e -> e.format.equalsIgnoreCase(format))
                .findFirst()
                .orElse(null);
    }

    /**
     * 验证格式是否支持
     * @param format 格式名称
     * @return 是否支持
     */
    public static boolean isValidFormat(String format) {
        return getByFormat(format) != null;
    }

    /**
     * 获取所有支持的格式
     * @return 格式集合
     */
    public static Set<String> getAllFormats() {
        return Arrays.stream(values())
                .map(FileFormatEnum::getFormat)
                .collect(Collectors.toSet());
    }

    /**
     * 根据文件类型获取支持的格式
     * @param fileType 文件类型 0=音频，1=视频
     * @return 格式集合
     */
    public static Set<String> getFormatsByFileType(Integer fileType) {
        return Arrays.stream(values())
                .filter(e -> e.fileType.equals(fileType))
                .map(FileFormatEnum::getFormat)
                .collect(Collectors.toSet());
    }

    /**
     * 验证格式与文件类型是否匹配
     * @param format 格式名称
     * @param fileType 文件类型
     * @return 是否匹配
     */
    public static boolean isFormatMatchFileType(String format, Integer fileType) {
        FileFormatEnum formatEnum = getByFormat(format);
        return formatEnum != null && formatEnum.fileType.equals(fileType);
    }
}

