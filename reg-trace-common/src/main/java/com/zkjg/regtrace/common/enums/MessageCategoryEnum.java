package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum MessageCategoryEnum {
    SYSTEM(0, "系统消息"),
    REGISTRATION(1, "登记消息"),
    TRACEABILITY(2, "溯源消息"),
    OTHER(3, "其他");

    private final int code;
    private final String description;

    public static Map<Object, String> toMap() {
        return Arrays.stream(values()).collect(Collectors.toMap(MessageCategoryEnum::getCode, MessageCategoryEnum::getDescription));
    }
}