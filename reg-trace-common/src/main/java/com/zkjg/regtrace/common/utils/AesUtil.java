package com.zkjg.regtrace.common.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 17:05
 */
public class AesUtil {
    private static final String AES_KEY = "002545hfzkjgbfgz";

    private static final AES aes = SecureUtil.aes(AES_KEY.getBytes(CharsetUtil.CHARSET_UTF_8));

    public static String encrypt(String plaintext) {
        return aes.encryptBase64(plaintext, CharsetUtil.CHARSET_UTF_8);
    }

    public static String decrypt(String ciphertext) {
        return aes.decryptStr(ciphertext, CharsetUtil.CHARSET_UTF_8);
    }
}
