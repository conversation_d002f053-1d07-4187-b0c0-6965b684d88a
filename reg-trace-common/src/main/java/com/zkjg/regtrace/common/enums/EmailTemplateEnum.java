package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 10:07
 */
@AllArgsConstructor
@Getter
public enum EmailTemplateEnum {
    VERIFICATION_CODE("音视频溯源管理平台", "email/verification.ftl"),
    RESET_PASSWORD("音视频溯源管理平台", "email/resetPassword.ftl"),
    TRACEABILITY("音视频溯源管理平台", "email/traceability.ftl"),
    WORK_REGISTRATION_AUDIT("音视频登记审核结果通知", "email/work_registration_audit_result.ftl"),
    CHANGE_USER_INFO("音视频溯源管理平台", "email/changeUserInfo.ftl"),
    ;

    private final String subject;
    private final String template;
}
