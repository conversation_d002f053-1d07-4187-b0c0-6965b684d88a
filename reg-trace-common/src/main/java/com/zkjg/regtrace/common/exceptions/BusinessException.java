package com.zkjg.regtrace.common.exceptions;

import com.zkjg.regtrace.common.enums.RetCodeEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 业务异常
 *
 * @Author:zmhao
 * @Date: 10:52 2024/6/5
 */
@Getter
public class BusinessException extends RuntimeException {

    private final String errorMessage;

    private RetCodeEnum retCodeEnum;

    public BusinessException(String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
    }

    public BusinessException(RetCodeEnum retCodeEnum) {
        super(retCodeEnum.getMsg());
        this.errorMessage = retCodeEnum.getMsg();
        this.retCodeEnum = retCodeEnum;
    }

    public BusinessException(RetCodeEnum retCodeEnum, String... args) {
        super(getErrorMsg(retCodeEnum, args));
        this.errorMessage = getErrorMsg(retCodeEnum, args);
        this.retCodeEnum = retCodeEnum;
    }

    public static String getErrorMsg(RetCodeEnum retCodeEnum, String... args) {
        String errorMsg = String.format(retCodeEnum.getMsg(), args);
        // 说明StringFormat不启作用
        if (StringUtils.equals(errorMsg, retCodeEnum.getMsg())) {
            StringBuffer buffer = new StringBuffer();
            buffer.append(retCodeEnum.getMsg()).append("，详情：");
            if (args != null) {
                for (String arg : args) {
                    buffer.append(arg).append(" ");
                }
            }
            errorMsg = buffer.toString();
        }
        return errorMsg;
    }
}
