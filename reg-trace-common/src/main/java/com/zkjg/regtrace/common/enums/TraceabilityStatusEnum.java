package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum TraceabilityStatusEnum {
    FAIL(0, "失败"),
    SUCCESS(1, "成功");

    private final Integer type;
    private final String description;

    public static String getDesc(Integer code) {
        for (TraceabilityStatusEnum result : TraceabilityStatusEnum.values()) {
            if (result.getType().equals(code)) {
                return result.getDescription();
            }
        }
        return null;
    }

    public static Map<Object, String> toMap() {
        return Arrays.stream(TraceabilityStatusEnum.values())
                .collect(Collectors.toMap(
                        TraceabilityStatusEnum::getType,
                        TraceabilityStatusEnum::getDescription
                ));
    }
}
