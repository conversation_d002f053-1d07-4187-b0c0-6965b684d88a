package com.zkjg.regtrace.common.threads;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 线程池管理工具
 * @date 2023/11/17 11:10
 */
@Component
@Slf4j
public class ThreadPoolManager {


    /**
     * 默认队列大小 1000
     */
    private static final Integer QUEUE_SIZE = 10240;

    private static final Integer QUEUE_SIZE_TWO = 65530;

    private static final Integer CORE_POOL_SIZE = 16;
    private static final Integer MAX_POOL_SIZE = 32;
    private static final Integer MAX_POOL_SIZE_TWO = 24;

    private static final Integer KEEP_ALIVE_TIME = 30;


    /**
     * 线程池,公用
     */
    public static final ExecutorService commonThreadPool = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(QUEUE_SIZE),
            r -> new Thread(r, "commonThreadPool-" + r.hashCode()),
            new ThreadPoolExecutor.CallerRunsPolicy());


    /**
     * 线程池。登记
     */
    public static final ExecutorService registrationThreadPool = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(QUEUE_SIZE_TWO),
            r -> new Thread(r, "registrationThreadPool-" + r.hashCode()),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 线程池。溯源
     */
    public static final ExecutorService traceabilityThreadPool = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE_TWO,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(QUEUE_SIZE_TWO),
            r -> new Thread(r, "traceabilityThreadPool-" + r.hashCode()),
            new ThreadPoolExecutor.CallerRunsPolicy());
}
