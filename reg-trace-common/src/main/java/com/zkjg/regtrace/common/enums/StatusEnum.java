package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 9:26
 */
@AllArgsConstructor
@Getter
public enum StatusEnum {
    NO(0, "否"),
    YES(1, "是");

    private final int code;
    private final String description;

    public static String getDescriptionByCode(int code) {
        for (StatusEnum e : values()) {
            if (e.code == code) {
                return e.description;
            }
        }
        return null;
    }

    public static Map<Object, String> toMap() {
        return Arrays.stream(values()).collect(Collectors.toMap(StatusEnum::getCode, StatusEnum::getDescription));
    }
}
