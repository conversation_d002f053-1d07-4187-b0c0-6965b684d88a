package com.zkjg.regtrace.common.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据库连接池优化配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class DatabaseConfig {

    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.hikari")
    public HikariConfig hikariConfig() {
        HikariConfig config = new HikariConfig();
        
        // 连接池优化配置
        config.setMaximumPoolSize(50);  // 降低最大连接数，避免数据库压力过大
        config.setMinimumIdle(10);      // 增加最小空闲连接数
        config.setConnectionTimeout(20000);  // 连接超时 20秒
        config.setIdleTimeout(300000);       // 空闲超时 5分钟
        config.setMaxLifetime(1200000);      // 连接最大生命周期 20分钟
        config.setLeakDetectionThreshold(60000); // 连接泄漏检测 1分钟
        
        // 性能优化
        config.setAutoCommit(true);
        config.setReadOnly(false);
        config.setTransactionIsolation("TRANSACTION_READ_COMMITTED");
        
        // 连接验证
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(3000);
        
        // 连接池名称
        config.setPoolName("HikariCP-RegTrace");
        
        // JMX监控
        config.setRegisterMbeans(true);
        
        log.info("HikariCP 连接池配置完成");
        return config;
    }

    @Bean
    @Primary
    public DataSource dataSource(HikariConfig hikariConfig) {
        return new HikariDataSource(hikariConfig);
    }
}
