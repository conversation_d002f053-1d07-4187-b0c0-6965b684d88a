package com.zkjg.regtrace.common.converter;

import com.zkjg.regtrace.common.enums.*;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;

import java.util.HashMap;
import java.util.Map;

public class EnumConverterRegistry {

    public static void registerEnum(Class<? extends ExportableEnum> enumClass, String converterName) {
        Map<Object, String> valueMap = new HashMap<>();
        for (ExportableEnum e : enumClass.getEnumConstants()) {
            valueMap.put(e.getType(), e.getDescription());
        }
        UniversalExportUtil.registerStatusConverter(converterName, valueMap);
    }

    // 批量注册
    public static void registerAll() {
        UniversalExportUtil.registerStatusConverter("fileTypeConverter", FileTypeEnum.toMap());
        UniversalExportUtil.registerStatusConverter("statusEnumConverter", StatusEnum.toMap());
        UniversalExportUtil.registerStatusConverter("messageLevelEnumConverter", MessageLevelEnum.toMap());
        UniversalExportUtil.registerStatusConverter("messageCategoryEnumConverter", MessageCategoryEnum.toMap());
        UniversalExportUtil.registerStatusConverter("traceabilityStatusConverter", TraceabilityStatusEnum.toMap());
        registerEnum(TraceabilityTypeEnum.class, "traceabilityTypeConverter");
        registerEnum(ExceptionEnum.class, "exceptionTypeConverter");
    }
}
