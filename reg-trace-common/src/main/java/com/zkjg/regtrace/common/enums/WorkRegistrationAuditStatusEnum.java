package com.zkjg.regtrace.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum WorkRegistrationAuditStatusEnum {
    DRAFT(0, "草稿"),
    PENDING(1, "审核中"),
    APPROVED(2, "审核通过"),
    REJECTED(3, "审核驳回");
    private final Integer code;
    private final String desc;
    WorkRegistrationAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static WorkRegistrationAuditStatusEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.code.equals(code)).findFirst().orElse(null);
    }
    public static Map<Object, String> toMap() {
        return Arrays.stream(values()).collect(Collectors.toMap(WorkRegistrationAuditStatusEnum::getCode, WorkRegistrationAuditStatusEnum::getDesc));
    }
}
