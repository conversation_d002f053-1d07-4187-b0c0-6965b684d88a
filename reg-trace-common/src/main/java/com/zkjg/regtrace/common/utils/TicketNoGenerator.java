package com.zkjg.regtrace.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 工单编号生成器
 * <AUTHOR>
 */
public class TicketNoGenerator {

    private static final String PREFIX = "TK";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    /**
     * 生成工单编号
     * 格式：TK + 年月日时分秒 + 4位随机字符
     * 示例：TK202312251430255A3B
     *
     * @return 工单编号
     */
    public static String generate() {
        StringBuilder ticketNo = new StringBuilder(PREFIX);

        // 添加时间戳
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        ticketNo.append(timestamp);

        // 添加4位随机字符
        Random random = ThreadLocalRandom.current();
        for (int i = 0; i < 4; i++) {
            ticketNo.append(CHARS.charAt(random.nextInt(CHARS.length())));
        }

        return ticketNo.toString();
    }


    /**
     * 生成简短的工单编号
     * 格式：年月日 + 6位随机数字
     * 示例：202312250123456
     *
     * @return 工单编号
     */
    public static String generateShort() {
        StringBuilder ticketNo = new StringBuilder();

        // 添加年月日
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        ticketNo.append(date);

        // 添加6位随机数字
        Random random = ThreadLocalRandom.current();
        for (int i = 0; i < 6; i++) {
            ticketNo.append(random.nextInt(10));
        }

        return ticketNo.toString();
    }

    /**
     * 生成带前缀和后缀的工单编号
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 工单编号
     */
    public static String generateCustom(String prefix, String suffix) {
        StringBuilder ticketNo = new StringBuilder();

        // 添加前缀
        if (prefix != null && !prefix.isEmpty()) {
            ticketNo.append(prefix);
        }

        // 添加时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmm"));
        ticketNo.append(timestamp);

        // 添加3位随机数
        Random random = ThreadLocalRandom.current();
        ticketNo.append(String.format("%03d", random.nextInt(1000)));

        // 添加后缀
        if (suffix != null && !suffix.isEmpty()) {
            ticketNo.append(suffix);
        }

        return ticketNo.toString();
    }
}
