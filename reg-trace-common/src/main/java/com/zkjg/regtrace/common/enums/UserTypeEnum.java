package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 9:23
 */
@AllArgsConstructor
@Getter
public enum UserTypeEnum {
    SYSTEM_USER(0, "系统用户"),
    PERSONAL_USER(1, "个人用户"),
    ENTERPRISE_USER(2, "企业用户"),
    GOVERNMENT_USER(3, "政府用户"),
    TRACEABILITY_USER(4, "溯源用户"),
    GUEST_USER(5, "访客");

    private final int code;
    private final String description;
}
