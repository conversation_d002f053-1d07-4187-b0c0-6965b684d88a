package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TraceabilityTypeEnum implements ExportableEnum{
    HASH_TRACEABILITY("hash", "条件溯源"),
    FILE_TRACEABILITY("file", "文件溯源");

    private final String type;
    private final String description;

    public static String getDescription(String type) {
        for (TraceabilityTypeEnum e : values()) {
            if (e.getType().equals(type)) {
                return e.getDescription();
            }
        }
        return null;
    }

    public static TraceabilityTypeEnum fromType(String type) {
        for (TraceabilityTypeEnum t : values()) {
            if (t.type.equalsIgnoreCase(type)) {
                return t;
            }
        }
        return null;
    }
}