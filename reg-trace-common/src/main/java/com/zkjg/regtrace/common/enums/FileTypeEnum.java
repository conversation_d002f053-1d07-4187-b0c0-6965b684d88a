package com.zkjg.regtrace.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件类型
 */
@Getter
public enum FileTypeEnum {
    AUDIO(0, "音频"),
    VIDEO(1, "视频"),
    IMAGE(2, "图片");

    private final Integer code;
    private final String desc;

    FileTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Object, String> toMap() {
        return Arrays.stream(values()).collect(Collectors.toMap(FileTypeEnum::getCode, FileTypeEnum::getDesc));
    }

}
