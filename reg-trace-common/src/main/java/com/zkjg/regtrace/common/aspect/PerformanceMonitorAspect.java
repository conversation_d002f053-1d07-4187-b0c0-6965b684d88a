package com.zkjg.regtrace.common.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 性能监控切面
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class PerformanceMonitorAspect {

    /**
     * 监控Service层方法性能
     */
    @Around("execution(* com.zkjg.regtrace.service..*(..))")
    public Object monitorServicePerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, "SERVICE");
    }

    /**
     * 监控Manager层方法性能
     */
    @Around("execution(* com.zkjg.regtrace.manager..*(..))")
    public Object monitorManagerPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, "MANAGER");
    }

    /**
     * 监控Repository层方法性能
     */
    @Around("execution(* com.zkjg.regtrace.persistence.repository..*(..))")
    public Object monitorRepositoryPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, "REPOSITORY");
    }

    private Object monitorPerformance(ProceedingJoinPoint joinPoint, String layer) throws Throwable {
        String methodName = joinPoint.getTarget().getClass().getSimpleName() + "." + joinPoint.getSignature().getName();
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录慢查询（超过1秒）
            if (duration > 1000) {
                log.warn("慢查询警告 - [{}] {}, 耗时: {}ms", layer, methodName, duration);
            } else if (duration > 500) {
                log.info("性能监控 - [{}] {}, 耗时: {}ms", layer, methodName, duration);
            }
            
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("方法执行异常 - [{}] {}, 耗时: {}ms, 异常: {}", layer, methodName, duration, e.getMessage());
            throw e;
        }
    }
}
