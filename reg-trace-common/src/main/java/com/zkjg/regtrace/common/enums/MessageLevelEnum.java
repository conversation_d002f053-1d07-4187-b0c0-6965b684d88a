package com.zkjg.regtrace.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum MessageLevelEnum {
    NORMAL(1, "常规"),
    URGENT(2, "紧急");

    private final int code;
    private final String description;

    public static Map<Object, String> toMap() {
        return Arrays.stream(values()).collect(Collectors.toMap(MessageLevelEnum::getCode, MessageLevelEnum::getDescription));
    }
}