package com.zkjg.regtrace.common.annotation;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @description 查询时间验证
 * @create 2024/8/22 15:06
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateRangeValidator.class)
@Documented
public @interface ValidDateRange {
    String message() default "Invalid value";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int maxDays() default 90;

    String startTimeField() default "startTime";

    String endTimeField() default "endTime";
}


class DateRangeValidator implements ConstraintValidator<ValidDateRange, Object> {
    private int maxDays;
    private String startTime;
    private String endTime;

    @Override
    public void initialize(ValidDateRange constraintAnnotation) {
        this.maxDays = constraintAnnotation.maxDays();
        this.startTime = constraintAnnotation.startTimeField();
        this.endTime = constraintAnnotation.endTimeField();
    }

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext context) {
        try {
            Field startTimeField = object.getClass().getDeclaredField(startTime);
            Field endTimeField = object.getClass().getDeclaredField(endTime);

            startTimeField.setAccessible(true);
            endTimeField.setAccessible(true);

            LocalDateTime startTime = (LocalDateTime) startTimeField.get(object);
            LocalDateTime endTime = (LocalDateTime) endTimeField.get(object);

            //空值不校验
            if (startTime == null || endTime == null) {
                return true;
            }

            // 验证结束时间是否晚于开始时间
            if (!endTime.isAfter(startTime)) {
                return false;
            }

            // 验证时间差是否在允许范围内
            long daysBetween = ChronoUnit.DAYS.between(startTime, endTime);
            return daysBetween <= maxDays;

        } catch (NoSuchFieldException | IllegalAccessException e) {
            // 如果类中没有 `startTime` 或 `endTime` 字段，或者无法访问，校验失败
            return false;
        }
    }
}