package com.zkjg.regtrace.auth.token.provider;

import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.repository.SysUserRepository;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 11:04
 */
@Service
public class PasswordAuthProvider implements UserDetailsService {

    @Resource
    private SysUserRepository sysUserRepository;
    @Resource
    private CommonProvider commonProvider;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUserDO sysUser = sysUserRepository.findByUsernameAndDeleted(username, StatusEnum.NO.getCode());
        if (Objects.isNull(sysUser)) {
            throw new UsernameNotFoundException("用户名或密码错误");
        }
        if (sysUser.getUserStatus().equals(StatusEnum.NO.getCode())) {
            throw new BadCredentialsException("该账号已被封禁");
        }
        String lockKey = String.format(RedisKeyConstant.LOGIN_LOCK, sysUser.getId());
        if (redisUtil.hasKey(lockKey)) {
            throw new BadCredentialsException("该账号已被冻结");
        }
        return LoginUser.builder()
                .userId(sysUser.getId())
                .username(sysUser.getUsername())
                .password(sysUser.getPassword())
                .permissions(commonProvider.getPermissions(sysUser.getId()))
                .build();
    }
}
