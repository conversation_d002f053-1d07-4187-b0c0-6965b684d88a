package com.zkjg.regtrace.auth.token.factory.impl;

import com.zkjg.regtrace.auth.token.factory.LoginStrategy;
import com.zkjg.regtrace.common.constants.UserConstant;
import com.zkjg.regtrace.persistence.vo.request.login.LoginRequest;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 10:43
 */
@Component(UserConstant.LoginType.ACCOUNT_PASSWORD)
public class PasswordLoginStrategy implements LoginStrategy {

    @Resource
    private AuthenticationManager authenticationManager;

    @Override
    public Authentication authenticate(LoginRequest request) {
        UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword());
        return authenticationManager.authenticate(authToken);
    }
}
