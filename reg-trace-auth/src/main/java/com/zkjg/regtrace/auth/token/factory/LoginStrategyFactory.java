package com.zkjg.regtrace.auth.token.factory;

import com.zkjg.regtrace.common.exceptions.BusinessException;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 10:47
 */
@Component
public class LoginStrategyFactory {

    private final Map<String,LoginStrategy> strategyMap;

    public LoginStrategyFactory(Map<String, LoginStrategy> strategyMap) {
        this.strategyMap = strategyMap;
    }

    /**
     * 获取登录执行器
     */
    public LoginStrategy getLoginHandler(String loginType) {
        LoginStrategy handler = strategyMap.get(loginType);
        if (handler == null) {
            throw new BusinessException("LoginStrategy type not found: " + loginType);
        }
        return handler;
    }
}
