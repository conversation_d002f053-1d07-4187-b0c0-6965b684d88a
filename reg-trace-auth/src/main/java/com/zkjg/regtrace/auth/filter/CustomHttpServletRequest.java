package com.zkjg.regtrace.auth.filter;


import com.zkjg.regtrace.common.constants.CommonConstant;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

public class CustomHttpServletRequest extends HttpServletRequestWrapper {

    private final String accessToken;

    public CustomHttpServletRequest(HttpServletRequest request, String accessToken) {
        super(request);
        this.accessToken = accessToken;
    }

    @Override
    public String getHeader(String name) {
        if (CommonConstant.AUTHORIZATION_HEADER_NAME.equalsIgnoreCase(name)) {
            return CommonConstant.TOKEN_PREFIX + accessToken;
        }
        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        if (CommonConstant.AUTHORIZATION_HEADER_NAME.equalsIgnoreCase(name)) {
            List<String> headers = Collections.singletonList(CommonConstant.TOKEN_PREFIX + accessToken);
            return Collections.enumeration(headers);
        }
        return super.getHeaders(name);
    }

    @Override
    public Enumeration<String> getHeaderNames() {
        List<String> names = Collections.list(super.getHeaderNames());
        if (!names.contains(CommonConstant.AUTHORIZATION_HEADER_NAME)) {
            names.add(CommonConstant.AUTHORIZATION_HEADER_NAME);
        }
        return Collections.enumeration(names);
    }
}
