package com.zkjg.regtrace.auth.config;

import com.alibaba.fastjson2.JSON;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.ResponseNotIntercept;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Objects;

@RestControllerAdvice
public class ResultAdvice implements ResponseBodyAdvice<Object> {
    @Override
    public boolean supports(MethodParameter returnType, @Nullable Class<? extends HttpMessageConverter<?>> converterType) {
        if (returnType.getDeclaringClass().isAnnotationPresent(ResponseNotIntercept.class)) {
            //若在类中加了@ResponseNotIntercept 则该类中的方法不用做统一的拦截
            return false;
        }
        //若方法上加了@ResponseNotIntercept 则该方法不用做统一的拦截
        return !Objects.requireNonNull(returnType.getMethod()).isAnnotationPresent(ResponseNotIntercept.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, @Nullable MethodParameter returnType, @Nullable MediaType selectedContentType,
                                  @Nullable Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @Nullable ServerHttpRequest request, @Nullable ServerHttpResponse response) {
        String path = request.getURI().getPath();
        if (isSwaggerPath(path)) {
            return body;
        }
        if (body instanceof Result) {
            // 提供一定的灵活度，如果body已经被包装了，就不进行包装
            return body;
        }
        if (body instanceof String) {
            //解决返回值为字符串时，不能正常包装
            return JSON.toJSONString(Result.ofSuccess(body));
        }
        return Result.ofSuccess(body);
    }

    private boolean isSwaggerPath(String path) {
        // 判断是否是Swagger相关请求路径
        return path.contains("/swagger") || path.contains("/v2/api-docs") || path.contains("/v3/api-docs")
                || path.contains("/swagger-resources") || path.contains("/webjars") || path.contains("/doc.html");
    }
}
