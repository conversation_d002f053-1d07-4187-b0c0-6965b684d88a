package com.zkjg.regtrace.auth.filter;


import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * token过滤器 验证token有效性
 *
 * <AUTHOR>
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    @Resource
    private TokenService tokenService;

    @Value("#{'${security.url-skip:}'.split(',')}")
    private String[] skipUrl;

    @Resource
    private RedisUtil redisUtil;

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain chain) throws ServletException, IOException {
        String token = tokenService.getToken(request);
        LoginUser loginUser = null;
        if (StringUtils.isNotBlank(token)) {
            String accessToken = redisUtil.get(String.format(RedisKeyConstant.LOGIN_UUID_PREFIX, token));
            if (StringUtils.isNotBlank(accessToken)) {
                request = new CustomHttpServletRequest(request, accessToken);
            }
            loginUser = tokenService.getLoginUser(accessToken);
        }
        // 过滤特定请求
        Set<String> filterUri = new HashSet<>();
        for (String url : skipUrl) {
            filterUri.add(request.getContextPath() + url);
        }

        if (Objects.nonNull(loginUser) && Objects.isNull(SecurityContextHolder.getContext().getAuthentication()) && !filterUri.contains(request.getRequestURI())) {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        }
        chain.doFilter(request, response);
    }
}
