package com.zkjg.regtrace.auth.token;


import cn.hutool.core.util.IdUtil;
import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.LoginPlatformEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.utils.RedisUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.security.core.authority.SimpleGrantedAuthority;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 9:12
 */
@Component
@Slf4j
public class TokenService {
    @Resource
    private RedisUtil redisUtil;

    public String generateToken(LoginUser loginUser) {
        String uuid = IdUtil.simpleUUID();
        loginUser.setUuid(uuid);
        //删除上一次登录的uuid
        delLoginUserByLoginName(loginUser.getUsername(), loginUser.getLoginPlatform());
        refreshToken(loginUser);
        return uuid;
    }

    public void delLoginUserByLoginName(String loginName) {
        for (LoginPlatformEnum platform : LoginPlatformEnum.values()) {
            String uuid = redisUtil.get(String.format(RedisKeyConstant.LOGIN_USERNAME_PREFIX, loginName, platform));
            redisUtil.delete(String.format(RedisKeyConstant.LOGIN_UUID_PREFIX, uuid));
        }
    }

    public void delLoginUserByLoginName(String loginName, LoginPlatformEnum loginPlatform) {
        String uuid = redisUtil.get(String.format(RedisKeyConstant.LOGIN_USERNAME_PREFIX, loginName, loginPlatform));
        redisUtil.delete(String.format(RedisKeyConstant.LOGIN_UUID_PREFIX, uuid));
    }

    public void refreshToken(LoginUser loginUser) {
        Map<String, Object> claims = new HashMap<>(1);
        claims.put("userId", loginUser.getUserId());
        claims.put("jti", loginUser.getUuid());
        claims.put("sub", loginUser.getUsername());
        claims.put("created", System.currentTimeMillis());
        claims.put("exp", System.currentTimeMillis() + 30 * 60 * 1000);
        claims.put("authorities", loginUser.getPermissions());
        claims.put("platform", loginUser.getLoginPlatform());
        String accessToken = createToken(claims);
        redisUtil.setEx(String.format(RedisKeyConstant.LOGIN_UUID_PREFIX, loginUser.getUuid()), accessToken, 30, TimeUnit.MINUTES);
        redisUtil.setEx(String.format(RedisKeyConstant.LOGIN_USERNAME_PREFIX, loginUser.getUsername(), loginUser.getLoginPlatform()),
                loginUser.getUuid(), 30,
                TimeUnit.MINUTES);
    }

    private Claims parseToken(String token) {
        return Jwts.parser().setSigningKey(CommonConstant.SECRET).parseClaimsJws(token).getBody();
    }

    private String createToken(Map<String, Object> claims) {
        return Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, CommonConstant.SECRET).compact();
    }

    public String getToken(HttpServletRequest request) {
        String token = request.getHeader(CommonConstant.AUTHORIZATION_HEADER_NAME);
        if (StringUtils.isNotEmpty(token) && token.startsWith(CommonConstant.TOKEN_PREFIX)) {
            token = token.replace(CommonConstant.TOKEN_PREFIX, "");
        }
        return token;
    }

    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new BusinessException("用户信息获取异常");
        }
    }

    public LoginUser getLoginUser(String accessToken) {
        if (StringUtils.isNotEmpty(accessToken)) {
            try {
                Claims claims = parseToken(accessToken);
                Date exp = claims.getExpiration();
                // 解析对应的权限以及用户信息
                String uuid = claims.getId();
                List<String> authoritiesList = (List<String>) claims.get("authorities");
                LoginUser user = LoginUser.builder().userId((Integer) claims.get("userId"))
                        .username(claims.getSubject()).loginPlatform(LoginPlatformEnum.valueOf((String) claims.get("platform")))
                        .uuid(uuid).permissions(authoritiesList).build();
                if (exp.getTime() - System.currentTimeMillis() < (10 * 60 * 1000)) {
                    refreshToken(user);
                }
                return user;
            } catch (Exception ignored) {
                log.error("鉴权异常：" + ignored.getMessage(), ignored);
            }
        }
        return null;
    }

    public static Collection<? extends GrantedAuthority> getAuthoritiesByPermission(List<String> permissions) {
        if (Objects.isNull(permissions)) {
            return null;
        }
        Set<String> collect = permissions.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        return collect.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
    }
}
