package com.zkjg.regtrace.auth.filter;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.utils.RedisUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

    @Resource
    private TokenService tokenService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 退出处理
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException {
        // 获取当前登录用户
        String token = tokenService.getToken(request);
        if (token != null) {
            // 清除缓存中的登录信息
            redisUtil.delete(String.format(RedisKeyConstant.LOGIN_UUID_PREFIX, token));
        }
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"code\":200, \"msg\":\"退出成功\"}");
    }

}
