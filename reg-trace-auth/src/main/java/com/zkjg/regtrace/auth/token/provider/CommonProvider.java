package com.zkjg.regtrace.auth.token.provider;

import com.zkjg.regtrace.auth.token.EmailAuthToken;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.persistence.entity.SysPermissionDO;
import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.SysUserRoleDO;
import com.zkjg.regtrace.persistence.repository.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.context.event.EventListener;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 15:19
 */
@Component
public class CommonProvider {
    @Resource
    private SysUserRepository sysUserRepository;
    @Resource
    private SysRoleRepository sysRoleRepository;
    @Resource
    private SysPermissionRepository sysPermissionRepository;
    @Resource
    private SysUserRoleRepository sysUserRoleRepository;
    @Resource
    private RedisUtil redisUtil;

    public List<String> getPermissions(Integer userId) {
        List<String> interfaceIdentity = new ArrayList<>();
        List<SysUserRoleDO> allRole = sysUserRoleRepository.findByUserIdAndStatus(userId, StatusEnum.YES.getCode());
        if (CollectionUtils.isEmpty(allRole)) {
            return interfaceIdentity;
        }
        Set<SysUserRoleDO> commonRole = new HashSet<>(allRole);
        if (!commonRole.isEmpty()) {
            Set<Integer> roleIds = commonRole.stream().map(SysUserRoleDO::getRoleId).collect(Collectors.toSet());
            SysRoleDO sysRoleDO = sysRoleRepository.findByRoleName(CommonConstant.SUPPER_ADMIN_CODE).orElseThrow(() -> new BusinessException("系统未初始化"));
            if (roleIds.contains(sysRoleDO.getId())) {
                //管理员账号
                List<SysPermissionDO> permissions = sysPermissionRepository.findAll();
                interfaceIdentity = permissions.stream().map(SysPermissionDO::getInterfaceIdentity).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            } else {
                interfaceIdentity = sysPermissionRepository.selectInterfaceIdentityForRoles(roleIds);
            }
        }
        return interfaceIdentity;
    }

    @EventListener
    public void onAuthenticationFailure(AuthenticationFailureBadCredentialsEvent event) {
        Authentication auth = event.getAuthentication();
        Object principal = auth.getPrincipal();
        SysUserDO userDO = null;
        if (auth instanceof EmailAuthToken) {
            userDO = sysUserRepository.findByEmailAndDeleted((String) principal, StatusEnum.NO.getCode());
        } else if (auth instanceof UsernamePasswordAuthenticationToken) {
            userDO = sysUserRepository.findByUsernameAndDeleted((String) principal, StatusEnum.NO.getCode());
        }
        if (Objects.nonNull(userDO)) {
            recordFailLogin(userDO.getId());
        }
    }

    public void recordFailLogin(Integer userId) {
        String lockKey = String.format(RedisKeyConstant.LOGIN_LOCK, userId);
        String failNumKey = String.format(RedisKeyConstant.LOGIN_FAIL, userId);

        Long failNum = redisUtil.incrBy(failNumKey, 1);
        if (failNum == 1) {
            redisUtil.expire(failNumKey, 10, TimeUnit.MINUTES);
        }
        if (failNum >= 5) {
            redisUtil.setEx(lockKey, userId + "", 30, TimeUnit.MINUTES);
        }
    }
}
