package com.zkjg.regtrace.auth.dto;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.enums.LoginPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 9:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginUser implements UserDetails, Serializable {

    private Integer userId;
    private String username;
    private String password;
    private String uuid;
    private List<String> permissions;
    private LoginPlatformEnum loginPlatform;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return TokenService.getAuthoritiesByPermission(permissions);
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
