package com.zkjg.regtrace.auth.token.provider;

import com.zkjg.regtrace.auth.dto.LoginUser;
import com.zkjg.regtrace.auth.token.EmailAuthToken;
import com.zkjg.regtrace.common.constants.RedisKeyConstant;
import com.zkjg.regtrace.common.enums.EmailSendTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.repository.SysUserRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 10:19
 */
@Component
public class EmailAuthProvider implements AuthenticationProvider {

    @Resource
    private SysUserRepository sysUserRepository;
    @Resource
    private CommonProvider commonProvider;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String email = (String) authentication.getPrincipal();
        String code = (String) authentication.getCredentials();

        SysUserDO sysUserDO = sysUserRepository.findByEmailAndDeleted(email, StatusEnum.NO.getCode());
        if (Objects.isNull(sysUserDO)) {
            throw new UsernameNotFoundException("邮箱或者验证码错误");
        }
        String lockKey = String.format(RedisKeyConstant.LOGIN_LOCK, sysUserDO.getId());
        if(redisUtil.hasKey(lockKey)){
            throw new BadCredentialsException("该账号已被冻结");
        }
        if (sysUserDO.getUserStatus().equals(StatusEnum.NO.getCode())) {
            throw new BadCredentialsException("该账号已被封禁");
        }
        String redisKey = String.format(RedisKeyConstant.EMAIL_VERIFICATION_CODE, email, EmailSendTypeEnum.LOGIN.getCode());
        String verificationCode = redisUtil.get(redisKey);
        if (StringUtils.isEmpty(verificationCode)) {
            throw new BadCredentialsException("验证码失效");
        }
        if (!verificationCode.equals(code)) {
            throw new BadCredentialsException("错误的验证码");
        }

        LoginUser build = LoginUser.builder().username(sysUserDO.getUsername()).userId(sysUserDO.getId())
                .permissions(commonProvider.getPermissions(sysUserDO.getId())).build();
        return new EmailAuthToken(build, build.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return EmailAuthToken.class.isAssignableFrom(authentication);
    }
}
