<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zkjg</groupId>
        <artifactId>reg-trace</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>reg-trace-auth</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zkjg</groupId>
            <artifactId>reg-trace-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zkjg</groupId>
            <artifactId>reg-trace-persistence</artifactId>
        </dependency>
    </dependencies>

</project>