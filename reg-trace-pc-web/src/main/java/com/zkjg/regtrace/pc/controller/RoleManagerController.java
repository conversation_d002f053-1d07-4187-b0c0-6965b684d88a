package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.RoleManager;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleQueryUserListForAddVo;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleQueryUserListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:30
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/role/manager")
public class RoleManagerController {
    @Resource
    private RoleManager roleManager;

    @PostMapping("role/list")
    @ApiOperation(value = "查询角色")
//    @PreAuthorize("hasAuthority('sys:role:list')")
    @OperationLog(type = OperationLogType.ROLE_MANAGEMENT, content = "查询了角色列表")
    public PageResult<RoleListVo> selectRoleList(@RequestBody @Validated RoleListRequest req) {
        return roleManager.selectRoleList(req);
    }

    @PostMapping("role/add")
    @ApiOperation(value = "添加角色")
//    @PreAuthorize("hasAuthority('sys:role:add')")
    @OperationLog(type = OperationLogType.ROLE_MANAGEMENT, content = "添加了角色#req.roleName")
    public void addRole(@RequestBody @Validated RoleAddRequest req) {
        roleManager.addRole(req);
    }

    @GetMapping("role/detail/{roleId}")
    @ApiOperation(value = "角色权限详情", httpMethod = "GET")
//    @PreAuthorize("hasAuthority('sys:role:detail')")
    public List<PermissionDetailVo> selectPermissionListByRole(@PathVariable Integer roleId) {
        return roleManager.selectPermissionListByRole(roleId);
    }

    @PostMapping("role/edit")
    @ApiOperation(value = "编辑角色")
    @OperationLog(type = OperationLogType.ROLE_MANAGEMENT, content = "编辑了角色#req.roleName")
//    @PreAuthorize("hasAuthority('sys:role:edit')")
    public void editRole(@RequestBody @Validated RoleEditRequest req) {
        roleManager.editRole(req);
    }

    @PostMapping("batchDelete")
    @ApiOperation(value = "删除角色")
//    @PreAuthorize("hasAuthority('sys:role:batch:delete')")
    public void batchDelete(@RequestBody List<Integer> roleIds) {
        roleManager.deleteRoles(roleIds);
    }

    @DeleteMapping("delete/{roleId}")
    @ApiOperation(value = "删除角色")
//    @PreAuthorize("hasAuthority('sys:role:delete')")
    public void delete(@PathVariable Integer roleId) {
        roleManager.deleteRoles(Collections.singletonList(roleId));
    }

    @PostMapping("selectUser")
    @ApiOperation(value = "查询已分配用户")
//    @PreAuthorize("hasAuthority('sys:role:select:user')")
    public PageResult<RoleQueryUserListVo> selectUser(@RequestBody @Validated RoleQueryUserListRequest request) {
        return roleManager.selectUser(request);
    }

    @PostMapping("deleteUserRoles")
    @ApiOperation(value = "取消授权")
//    @PreAuthorize("hasAuthority('sys:role:delete:user')")
    public void deleteUserRoles(@RequestBody @Validated RoleDeleteOrAddUserListRequest request) {
        roleManager.deleteUserRoles(request);
    }

    @PostMapping("deleteUserRole")
    @ApiOperation(value = "批量取消授权")
//    @PreAuthorize("hasAuthority('sys:role:delete:users')")
    public void deleteUserRole(@RequestBody @Validated RoleDeleteOrAddUserListRequest request) {
        roleManager.deleteUserRoles(request);
    }

    @PostMapping("selectUserForAdd")
    @ApiOperation(value = "查询可添加用户")
//    @PreAuthorize("hasAuthority('sys:role:select:user:add')")
    public PageResult<RoleQueryUserListForAddVo> selectUserForAdd(@RequestBody @Validated RoleQueryUserListForAddRequest request) {
        return roleManager.selectUserForAdd(request);
    }

    @PostMapping("addUserRole")
    @ApiOperation(value = "批量添加用户")
//    @PreAuthorize("hasAuthority('sys:role:add:users')")
    public void addUserRole(@RequestBody @Validated RoleDeleteOrAddUserListRequest request) {
        roleManager.addUserRoles(request);
    }
}
