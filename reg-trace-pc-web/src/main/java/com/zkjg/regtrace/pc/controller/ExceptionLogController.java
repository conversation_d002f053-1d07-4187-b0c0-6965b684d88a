package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.ExceptionManager;
import com.zkjg.regtrace.persistence.vo.request.exception.ExceptionLogRequest;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionLogVo;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionStatisticsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Api(tags = "异常管理")
@RestController
@RequestMapping("/exception")
@Validated
public class ExceptionLogController {

    @Resource
    private ExceptionManager exceptionManager;

    @PostMapping("queryLogs")
    @ApiOperation(value = "查询异常日志", httpMethod = "POST")
    public PageResult<ExceptionLogVo> queryLogs(@RequestBody @Valid ExceptionLogRequest req) {
        return exceptionManager.queryLogs(req);
    }

    @GetMapping("export")
    @ApiOperation(value = "导出异常日志", httpMethod = "POST")
    public void exportLogs(HttpServletResponse response,
                           @RequestBody @Valid ExceptionLogRequest req,
                           @RequestParam(defaultValue = "EXCEL", required = false) String type) {
        exceptionManager.exportLogs(response, req, type);
    }

    @GetMapping("statistics")
    @ApiOperation(value = "统计", httpMethod = "GET")
    public ExceptionStatisticsVo statistics(@RequestParam Integer days) {
        return exceptionManager.statistics(days);
    }
}
