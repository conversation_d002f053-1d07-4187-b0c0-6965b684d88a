package com.zkjg.regtrace.pc.service.impl;

import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.persistence.repository.ContentRegisterMetricsRepository;
import com.zkjg.regtrace.persistence.repository.SysUserRepository;
import com.zkjg.regtrace.persistence.repository.TraceMetricsRepository;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsDecimalVo;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;
import com.zkjg.regtrace.pc.service.OperateMetricsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class OperateMetricsServiceImpl implements OperateMetricsService {

    @Resource
    private ContentRegisterMetricsRepository contentRegisterMetricsRepository;

    @Resource
    private TraceMetricsRepository traceMetricsRepository;

    @Resource
    private SysUserRepository sysUserRepository;

    @Override
    public StatsVo getContentRegisterStats(String dimension, Integer fileType, String statTime) {
        if ("daily".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 8) {
                throw new BusinessException("Invalid daily statTime format, should be: YYYYMMDD");
            }
            List<Map<String, Object>> results = contentRegisterMetricsRepository.findDailyStatsForDate(fileType, statTime);
            return formatDailyStats(results);
        } else if ("monthly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 6) {
                throw new BusinessException("Invalid monthly statTime format, should be: YYYYMM");
            }
            List<Map<String, Object>> results = contentRegisterMetricsRepository.findMonthlyStatsForMonth(fileType, statTime);
            return formatMonthlyStats(results);
        } else {
            throw new BusinessException("Invalid dimension: " + dimension);
        }
    }

    @Override
    public StatsVo getTraceStats(String dimension, Integer status, String statTime) {
        if ("daily".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 8) {
                throw new BusinessException("Invalid daily statTime format, should be: YYYYMMDD");
            }
            List<Map<String, Object>> results = traceMetricsRepository.findDailyStatsForDate(status, statTime);
            return formatDailyStats(results);
        } else if ("monthly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 6) {
                throw new BusinessException("Invalid monthly statTime format, should be: YYYYMM");
            }
            List<Map<String, Object>> results = traceMetricsRepository.findMonthlyStatsForMonth(status, statTime, null);
            return formatMonthlyStats(results);
        } else if ("yearly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 4) {
                throw new BusinessException("Invalid yearly statTime format, should be: YYYY");
            }
            List<Map<String, Object>> results = traceMetricsRepository.findYearlyStatsForYear(status, statTime);
            return formatYearlyStats(results);
        } else {
            throw new BusinessException("Invalid dimension: " + dimension);
        }
    }

    @Override
    public StatsVo getNewUserStats(String dimension, String statTime) {
        if ("daily".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 8) {
                throw new BusinessException("Invalid daily statTime format, should be: YYYYMMDD");
            }
            List<Map<String, Object>> results = sysUserRepository.findDailyNewUserStatsForDate(statTime);
            return formatDailyStats(results);
        } else if ("monthly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 6) {
                throw new BusinessException("Invalid monthly statTime format, should be: YYYYMM");
            }
            List<Map<String, Object>> results = sysUserRepository.findMonthlyNewUserStatsForMonth(statTime);
            return formatMonthlyStats(results);
        } else if ("yearly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 4) {
                throw new BusinessException("Invalid yearly statTime format, should be: YYYY");
            }
            List<Map<String, Object>> results = sysUserRepository.findYearlyNewUserStatsForYear(statTime);
            return formatYearlyStats(results);
        } else {
            throw new BusinessException("Invalid dimension: " + dimension);
        }
    }

    @Override
    public StatsDecimalVo getActiveUserRatioStats(String dimension, String statTime) {
        // 验证时间格式
        if ("daily".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 8) {
                throw new BusinessException("Invalid daily statTime format, should be: YYYYMMDD");
            }
            List<Map<String, Object>> results = contentRegisterMetricsRepository.findDailyActiveUserRatioForDate(statTime);
            return formatActiveUserRatioStats(results, 24);
        } else if ("monthly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 6) {
                throw new BusinessException("Invalid monthly statTime format, should be: YYYYMM");
            }
            List<Map<String, Object>> results = contentRegisterMetricsRepository.findMonthlyActiveUserRatioForMonth(statTime);
            return formatActiveUserRatioStats(results, 31);
        } else if ("yearly".equalsIgnoreCase(dimension)) {
            if (statTime.length() != 4) {
                throw new BusinessException("Invalid yearly statTime format, should be: YYYY");
            }
            List<Map<String, Object>> results = contentRegisterMetricsRepository.findYearlyActiveUserRatioForYear(statTime);
            return formatActiveUserRatioStats(results, 12);
        } else {
            throw new BusinessException("Invalid dimension: " + dimension);
        }
    }

    private StatsVo formatDailyStats(List<Map<String, Object>> results) {
        StatsVo dto = new StatsVo();
        List<String> labels = new ArrayList<>();
        List<Long> values = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            labels.add(String.format("%02d:00", i));
            values.add(0L);
        }
        for (Map<String, Object> result : results) {
            int hour = (int) result.get("hour");
            long count = (long) result.get("count");
            values.set(hour, count);
        }
        dto.setLabels(labels);
        dto.setValues(values);
        return dto;
    }

    private static StatsVo formatMonthlyStats(List<Map<String, Object>> results) {
        StatsVo dto = new StatsVo();
        List<String> labels = new ArrayList<>();
        List<Long> values = new ArrayList<>();
        for (int i = 1; i <= 31; i++) {
            labels.add(String.valueOf(i));
            values.add(0L);
        }
        for (Map<String, Object> result : results) {
            int day = (int) result.get("day");
            long count = (long) result.get("count");
            values.set(day - 1, count);
        }
        dto.setLabels(labels);
        dto.setValues(values);
        return dto;
    }

    private StatsVo formatYearlyStats(List<Map<String, Object>> results) {
        StatsVo dto = new StatsVo();
        List<String> labels = new ArrayList<>();
        List<Long> values = new ArrayList<>();

        for (int i = 1; i <= 12; i++) {
            labels.add(i + "月");
            values.add(0L);
        }

        for (Map<String, Object> result : results) {
            int month = ((Number) result.get("month")).intValue();
            long count = ((Number) result.get("count")).longValue();
            values.set(month - 1, count);
        }

        dto.setLabels(labels);
        dto.setValues(values);
        return dto;
    }

    private StatsDecimalVo formatActiveUserRatioStats(List<Map<String, Object>> results, int size) {
        StatsDecimalVo dto = new StatsDecimalVo();
        List<String> labels = new ArrayList<>();
        List<Double> values = new ArrayList<>();

        // 初始化默认值
        for (int i = 0; i < size; i++) {
            labels.add(i < 10 ? "0" + i : String.valueOf(i));
            values.add(0.0);
        }

        // 填充实际数据
        for (Map<String, Object> result : results) {
            int timeSlot = ((Number) result.get("time_slot")).intValue();
            double ratio = ((Number) result.get("ratio")).doubleValue();
            if (timeSlot >= 0 && timeSlot < size) {
                values.set(timeSlot, ratio);
            }
        }

        dto.setLabels(labels);
        dto.setValues(values);
        return dto;
    }
}
