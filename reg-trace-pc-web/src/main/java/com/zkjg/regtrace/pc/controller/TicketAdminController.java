package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.TicketManager;
import com.zkjg.regtrace.persistence.vo.request.ticket.TicketQueryRequest;
import com.zkjg.regtrace.persistence.vo.response.ticket.CustomerServicePerformanceVO;
import com.zkjg.regtrace.persistence.vo.response.ticket.TicketCategoryStatisticsVO;
import com.zkjg.regtrace.persistence.vo.response.ticket.TicketDetailVO;
import com.zkjg.regtrace.persistence.vo.response.ticket.TicketListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 管理员工单控制器
 * 负责管理员对所有工单的管理操作
 * <AUTHOR>
 */
@Api(tags = "管理员-工单管理")
@RestController
@Slf4j
public class TicketAdminController {

    @Resource
    private TicketManager ticketManager;

    /**
     * 获取客服绩效统计
     * 统计客服在指定时间段内的工单处理情况
     */
    @ApiOperation("获取客服绩效统计")
    @GetMapping("/admin/tickets/statistics/service-performance")
    public Result<List<CustomerServicePerformanceVO>> getServicePerformance(
            @ApiParam("统计天数，默认30天") @RequestParam(defaultValue = "30") Integer days) {
        List<CustomerServicePerformanceVO> performances = ticketManager.getCustomerServicePerformance();
        return Result.ofSuccess(performances);
    }

    /**
     * 获取问题分类统计
     * 统计各类问题的数量分布
     */
    @ApiOperation("获取问题分类统计")
    @GetMapping("/admin/tickets/statistics/categories")
    public Result<List<TicketCategoryStatisticsVO>> getCategoryStatistics(
            @ApiParam("统计天数，默认30天") @RequestParam(defaultValue = "30") Integer days) {
        List<TicketCategoryStatisticsVO> statistics = ticketManager.getTicketCategoryStatistics();
        return Result.ofSuccess(statistics);
    }

    /**
     * 查询所有工单列表
     * 管理员可以查看所有工单
     */
    @ApiOperation("查询所有工单列表")
    @GetMapping("/admin/tickets")
    public Result<PageResult<TicketListVO>> getTicketList(@Valid TicketQueryRequest request) {
        PageResult<TicketListVO> result = ticketManager.queryTicketList(request);
        return Result.ofSuccess(result);
    }

    /**
     * 查询工单详情
     * 管理员可以查看任何工单的详情
     */
    @ApiOperation("查询工单详情")
    @GetMapping("/admin/tickets/{ticketId}")
    public Result<TicketDetailVO> getTicketDetail(
            @ApiParam(value = "工单ID", required = true) @PathVariable Long ticketId) {
        TicketDetailVO detail = ticketManager.getTicketDetail(ticketId);
        return Result.ofSuccess(detail);
    }
}
