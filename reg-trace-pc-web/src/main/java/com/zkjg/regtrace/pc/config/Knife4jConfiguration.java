package com.zkjg.regtrace.pc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Configuration
@EnableSwagger2WebMvc
@Profile({"dev","uat"})
public class Knife4jConfiguration {

    @Bean(value = "defaultApi")
    public Docket defaultApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(getApiInfo("音视频溯源管理平台"))
                //分组名称
                .groupName("后台用户")
                .select()
                //这里指定Controller扫描包路径
                .apis(RequestHandlerSelectors.basePackage("com.zkjg.regtrace.pc.controller"))
                .paths(PathSelectors.any())
                .build();
    }
    private ApiInfo getApiInfo(String description) {
        return new ApiInfoBuilder()
                .description(description)
                .termsOfServiceUrl("https://xxxx.com/")
                .version("1.0")
                .build();
    }


}
