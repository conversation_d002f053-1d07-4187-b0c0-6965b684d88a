package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.TraceabilityManager;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityAdminQueryRequest;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityAdminQueryResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "溯源管理")
@RestController
@RequestMapping("/traceability/admin")
@Validated
public class TraceabilityAdminController {

    @Resource
    private TraceabilityManager traceabilityManager;

    @PostMapping("batchHide")
    @ApiOperation(value = "批量隐藏", httpMethod = "POST")
    @OperationLog(type = OperationLogType.TRACE_RECORD, content = "批量隐藏了溯源，溯源id为：#ids")
    public void batchHide(@RequestBody List<Long> ids) {
        traceabilityManager.batchUpdateTraceabilityLogStatus(ids, "HIDE");
    }

    @PostMapping("batchShow")
    @ApiOperation(value = "批量显示", httpMethod = "POST")
    @OperationLog(type = OperationLogType.TRACE_RECORD, content = "批量显示溯源，溯源id为：#ids")
    public void batchShow(@RequestBody List<Long> ids) {
        traceabilityManager.batchUpdateTraceabilityLogStatus(ids, "SHOW");
    }

    @PostMapping("queryTraceabilityAdminList")
    @ApiOperation(value = "查询溯源管理列表", httpMethod = "POST")
    public PageResult<TraceabilityAdminQueryResponse> queryTraceabilityAdminList(@RequestBody @Valid TraceabilityAdminQueryRequest req) {
        return traceabilityManager.queryTraceabilityAdminList(req);
    }

}
