package com.zkjg.regtrace.pc.runner;

import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.enums.*;
import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.service.SysRoleService;
import com.zkjg.regtrace.service.SysUserRoleService;
import com.zkjg.regtrace.service.SysUserService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 14:19
 */
@Component
public class InitAdminRunner implements ApplicationRunner {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private BCryptPasswordEncoder bCryptPasswordEncoder;

    @Override
    @Transactional
    public void run(ApplicationArguments args) {
        SysRoleDO adminRole;
        Optional<SysRoleDO> byRoleName = sysRoleService.findByRoleName(CommonConstant.SUPPER_ADMIN_CODE);
        if (!byRoleName.isPresent()) {
            adminRole = SysRoleDO.builder().roleName(CommonConstant.SUPPER_ADMIN_CODE).roleStatus(StatusEnum.YES.getCode()).remark("超级管理员").createTime(LocalDateTime.now()).deleted(StatusEnum.NO.getCode()).source(RoleTypeEnum.PRE.getCode()).canDelete(StatusEnum.NO.getCode()).build();
            sysRoleService.saveRole(adminRole);
        } else {
            adminRole = byRoleName.get();
        }
        SysUserDO adminUser = sysUserService.findUserByUsername(CommonConstant.SUPPER_ADMIN_CODE);
        if (Objects.isNull(adminUser)) {
            SysUserDO userDO = SysUserDO.builder().email(CommonConstant.SUPPER_ADMIN_CODE).phone(CommonConstant.SUPPER_ADMIN_CODE).username(CommonConstant.SUPPER_ADMIN_CODE).password(bCryptPasswordEncoder.encode(CommonConstant.DEFAULT_PWD)).userType(UserTypeEnum.SYSTEM_USER.getCode()).userStatus(StatusEnum.YES.getCode()).deleted(StatusEnum.NO.getCode()).createTime(LocalDateTime.now()).build();
            sysUserService.saveUser(userDO);

            sysUserRoleService.deleteByUserId(userDO.getId());
            sysUserRoleService.saveUserPermission(userDO.getId(), Collections.singletonList(adminRole.getId()), CommonConstant.SUPPER_ADMIN_CODE);
        }
    }
}
