package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsDecimalVo;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;
import com.zkjg.regtrace.pc.service.OperateMetricsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-06-18 09:06
 */
@Api(tags = "平台运营数据统计接口")
@RestController
@RequestMapping("/pc/metrics")
public class OperateMetricsController {

    @Resource
    private OperateMetricsService operateMetricsService;

    @GetMapping("/content-register-stats")
    @ApiOperation("获取内容登记统计")
    public Result<StatsVo> getContentRegisterStats(
            @ApiParam(value = "时间维度", required = true, allowableValues = "daily,monthly")
            @RequestParam String dimension,
            @ApiParam(value = "文件类型: 0=音频, 1=视频")
            @RequestParam(required = false) Integer fileType,
            @ApiParam(value = "统计时间，格式：按天YYYYMMDD, 按月YYYYMM", required = true)
            @RequestParam String statTime) {
        return Result.ofSuccess(operateMetricsService.getContentRegisterStats(dimension, fileType, statTime));
    }

    @GetMapping("/trace-stats")
    @ApiOperation("获取溯源统计")
    public Result<StatsVo> getTraceStats(
            @ApiParam(value = "时间维度", required = true, allowableValues = "daily,monthly,yearly")
            @RequestParam String dimension,
            @ApiParam(value = "溯源状态: 1=成功, 0=失败")
            @RequestParam(required = false) Integer status,
            @ApiParam(value = "统计时间，格式：按天YYYYMMDD, 按月YYYYMM, 按年YYYY", required = true)
            @RequestParam String statTime) {
        return Result.ofSuccess(operateMetricsService.getTraceStats(dimension, status, statTime));
    }

    @GetMapping("/new-user-stats")
    @ApiOperation("获取新用户统计")
    public Result<StatsVo> getNewUserStats(
            @ApiParam(value = "时间维度", required = true, allowableValues = "daily,monthly,yearly")
            @RequestParam String dimension,
            @ApiParam(value = "统计时间，格式：按天YYYYMMDD, 按月YYYYMM, 按年YYYY", required = true)
            @RequestParam String statTime) {
        return Result.ofSuccess(operateMetricsService.getNewUserStats(dimension, statTime));
    }

    @GetMapping("/active-user-ratio-stats")
    @ApiOperation("获取活跃用户比例统计")
    public Result<StatsDecimalVo> getActiveUserRatioStats(
            @ApiParam(value = "时间维度", required = true, allowableValues = "daily,monthly,yearly")
            @RequestParam String dimension,
            @ApiParam(value = "统计时间，格式：按天YYYYMMDD, 按月YYYYMM, 按年YYYY", required = true)
            @RequestParam String statTime) {
        return Result.ofSuccess(operateMetricsService.getActiveUserRatioStats(dimension, statTime));
    }
}
