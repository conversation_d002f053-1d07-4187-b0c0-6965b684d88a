package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.DocumentManager;
import com.zkjg.regtrace.persistence.vo.request.document.DocumentQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.document.DocumentRequest;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentDetailVo;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/7/16 15:09
 */
@Api(tags = "文档管理")
@RestController
@RequestMapping("")
@Validated
public class DocumentController {

    @Resource
    private DocumentManager documentManager;

    @GetMapping("/documents")
    @ApiOperation(value = "查询文档管理列表", httpMethod = "GET")
    public PageResult<DocumentVo> queryDocuments(@Valid DocumentQueryRequest req) {
        return documentManager.selectList(req);
    }

    @GetMapping("/documents/{id}")
    @ApiOperation(value = "查询文档管理详情", httpMethod = "GET")
    public DocumentDetailVo queryDocuments(@PathVariable Integer id) {
        return documentManager.selectDetail(id);
    }

    @PostMapping("/documents")
    @ApiOperation(value = "创建文档", httpMethod = "POST")
    public Result<Boolean> addDocument(@RequestBody @Valid DocumentRequest req) {
        return documentManager.addDocument(req);
    }

    @PutMapping("/documents/{id}")
    @ApiOperation(value = "更新文档", httpMethod = "PUT")
    public Result<Boolean> editDocument(@PathVariable Integer id, @RequestBody @Valid DocumentRequest req) {
        return documentManager.editDocument(id, req);
    }

    @DeleteMapping("/documents/{id}")
    @ApiOperation(value = "删除文档", httpMethod = "DELETE")
    public Result<Boolean> deleteDocument(@PathVariable Integer id) {
        return documentManager.deleteDocument(id);
    }
}
