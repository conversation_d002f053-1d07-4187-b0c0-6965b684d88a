package com.zkjg.regtrace.pc.service;

import com.zkjg.regtrace.persistence.vo.response.stats.StatsDecimalVo;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;

public interface OperateMetricsService {

    /**
     * 获取内容登记统计
     * @param dimension 时间维度：daily/monthly
     * @param fileType 文件类型：0=音频, 1=视频
     * @param statTime 统计时间，格式：按天YYYYMMDD, 按月YYYYMM
     * @return 统计结果
     */
    StatsVo getContentRegisterStats(String dimension, Integer fileType, String statTime);

    /**
     * 获取溯源统计
     * @param dimension 时间维度：daily/monthly/yearly
     * @param status 溯源状态: 1=成功, 0=失败
     * @param statTime 统计时间，格式：按天YYYYMMDD, 按月YYYYMM, 按年YYYY
     * @return 统计结果
     */
    StatsVo getTraceStats(String dimension, Integer status, String statTime);

    /**
     * 获取新用户统计
     * @param dimension 时间维度：daily/monthly/yearly
     * @param statTime 统计时间，格式：daily-20250620, monthly-202506, yearly-2025
     * @return 统计结果
     */
    StatsVo getNewUserStats(String dimension, String statTime);

    /**
     * 获取活跃用户比例统计
     * @param dimension 时间维度：daily/monthly/yearly
     * @param statTime 统计时间，格式：按天YYYYMMDD, 按月YYYYMM, 按年YYYY
     * @return 统计结果
     */
    StatsDecimalVo getActiveUserRatioStats(String dimension, String statTime);
}
