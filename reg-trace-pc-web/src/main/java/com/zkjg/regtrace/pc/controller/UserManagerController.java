package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.UserManager;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.RoleListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserListVo;
import com.zkjg.regtrace.persistence.vo.response.permission.UserManageDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:30
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/user/manager")
public class UserManagerController {
    @Resource
    private BCryptPasswordEncoder bCryptPasswordEncoder;
    @Resource
    private UserManager userManager;

    @PostMapping("list")
    @ApiOperation(value = "查询用户")
//    @PreAuthorize("hasAuthority('sys:user:list')")
    @OperationLog(type = OperationLogType.USER_MANAGEMENT, content = "查询了用户列表")
    public PageResult<UserListVo> selectUserList(@RequestBody @Validated UserListRequest req) {
        return userManager.selectUserList(req);
    }

    @PostMapping("add")
    @ApiOperation(value = "添加用户")
//    @PreAuthorize("hasAuthority('sys:user:add')")
    @OperationLog(type = OperationLogType.USER_MANAGEMENT, content = "添加了用户#req.username")
    public Integer addUser(@RequestBody @Validated UserAddRequest req) {
        req.setPassword(bCryptPasswordEncoder.encode(req.getPassword()));
        return userManager.addUser(req);
    }

    @GetMapping("detail/{userId}")
//    @PreAuthorize("hasAuthority('sys:user:detail')")
    @ApiOperation(value = "用户详情", httpMethod = "GET")
    public UserManageDetailVo detail(@PathVariable Integer userId) {
        return userManager.selectUserDetail(userId);
    }

    @GetMapping("selectRoleList")
    @ApiOperation(value = "查询角色下拉列表", httpMethod = "GET")
    public List<RoleListVo> selectRoleList() {
        return userManager.selectRoleList();
    }

    @PostMapping("edit")
    @ApiOperation(value = "编辑用户信息")
//    @PreAuthorize("hasAuthority('sys:user:edit')")
    @OperationLog(type = OperationLogType.USER_MANAGEMENT, content = "编辑了用户#req.userId")
    public void editUser(@RequestBody @Validated UserManageEditRequest req) {
        userManager.editUser(req);
    }

    @DeleteMapping("delete/{userId}")
    @ApiOperation(value = "删除用户", httpMethod = "DELETE")
//    @PreAuthorize("hasAuthority('sys:user:delete')")
    @OperationLog(type = OperationLogType.USER_MANAGEMENT, content = "删除了用户#userId")
    public void delete(@PathVariable Integer userId) {
        if (userId.equals(TokenService.getLoginUser().getUserId())) {
            throw new BusinessException("不能删除自己");
        }
        userManager.deleteUser(userId);
    }
}
