package com.zkjg.regtrace.pc.controller;

import com.alibaba.excel.EasyExcel;
import com.zkjg.regtrace.common.annotation.ResponseNotIntercept;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.OperateLogManager;
import com.zkjg.regtrace.persistence.vo.request.log.OperateLogExportRequest;
import com.zkjg.regtrace.persistence.vo.request.log.OperateLogListRequest;
import com.zkjg.regtrace.persistence.vo.response.log.OperateLogListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 9:00
 */
@Api(tags = "操作日志")
@RestController
@RequestMapping("/operate/log")
public class OperateLogController {

    @Resource
    private OperateLogManager operateLogManager;

    @PostMapping("list")
    @ApiOperation(value = "查询操作日志列表")
    public PageResult<OperateLogListVo> selectOperateLogList(@RequestBody @Validated OperateLogListRequest req) {
        return operateLogManager.selectOperateLogList(req);
    }

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    @ResponseNotIntercept
    public void exportApi(@RequestBody @Validated OperateLogExportRequest req, HttpServletResponse response) throws IOException {
        this.setExcelResponseProp(response, "操作日志" + System.currentTimeMillis());
        List<OperateLogListVo> vos = operateLogManager.exportOperateLogList(req);
        EasyExcel.write(response.getOutputStream(), OperateLogListVo.class).sheet("操作日志").doWrite(vos);
    }

    private void setExcelResponseProp(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xls");
    }
}
