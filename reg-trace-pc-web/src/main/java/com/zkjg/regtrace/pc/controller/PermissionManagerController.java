package com.zkjg.regtrace.pc.controller;


import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.manager.PermissionManager;
import com.zkjg.regtrace.persistence.vo.request.permission.PermissionAddRequest;
import com.zkjg.regtrace.persistence.vo.request.permission.PermissionEditRequest;
import com.zkjg.regtrace.persistence.vo.request.permission.PermissionListRequest;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 11:28
 */
@Api(tags = "菜单管理")
@RestController
@RequestMapping("/permission/manager")
public class PermissionManagerController {

    @Resource
    private PermissionManager permissionManager;

    @PostMapping("list")
    @ApiOperation(value = "查询菜单")
    @OperationLog(type = OperationLogType.MENU_MANAGEMENT, content = "查询了菜单列表")
    public List<PermissionListVo> list(@RequestBody @Validated PermissionListRequest req) {
        return permissionManager.selectPermissionList(req);
    }

    @PostMapping("add")
    @ApiOperation(value = "新增菜单")
    @OperationLog(type = OperationLogType.MENU_MANAGEMENT, content = "新增了菜单#req.permissionName")
    public Integer add(@RequestBody @Validated PermissionAddRequest req) {
        if (permissionManager.checkSameNameByParentId(req.getPermissionName(), req.getParentId(), null)) {
            throw new BusinessException("同一级别菜单名称不可重复");
        }
        return permissionManager.insertPermission(req);
    }

    @PostMapping("addBatch")
    @ApiOperation(value = "批量新增菜单")
    public void addBatch(@RequestBody @Validated List<PermissionAddRequest> req) {
        req.forEach(e -> permissionManager.insertPermission(e));
    }

    @PostMapping("edit")
    @ApiOperation(value = "编辑菜单")
    @OperationLog(type = OperationLogType.MENU_MANAGEMENT, content = "编辑了菜单#req.permissionName")
    public void edit(@RequestBody @Validated PermissionEditRequest req) {
        if (permissionManager.checkSameNameByParentId(req.getPermissionName(), null, req.getId())) {
            throw new BusinessException("同一级别菜单名称不可重复");
        }
        permissionManager.updatePermission(req);
    }

    @DeleteMapping("delete/{permissionId}")
    @ApiOperation(value = "删除菜单")
    @OperationLog(type = OperationLogType.MENU_MANAGEMENT, content = "删除了菜单#permissionId")
    public void delete(@PathVariable Integer permissionId) {
        if (permissionManager.hasChild(permissionId)) {
            throw new BusinessException("存在子菜单,不允许删除");
        }
        if (permissionManager.hasPermissionExistRole(permissionId)) {
            throw new BusinessException("菜单已分配角色,不允许删除");
        }
        permissionManager.deletePermission(permissionId);
    }
}
