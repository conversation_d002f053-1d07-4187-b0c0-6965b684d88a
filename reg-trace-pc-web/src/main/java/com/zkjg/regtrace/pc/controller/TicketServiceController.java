package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.TicketManager;
import com.zkjg.regtrace.persistence.vo.request.ticket.*;
import com.zkjg.regtrace.persistence.vo.response.ticket.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 客服工单控制器
 * 负责客服人员的工单处理操作
 * <AUTHOR>
 */
@Api(tags = "客服中心-工单管理")
@RestController
@Slf4j
public class TicketServiceController {

    @Resource
    private TicketManager ticketManager;

    /**
     * 查询分配给我的工单列表
     * 客服只能查看分配给自己的工单
     */
    @GetMapping("/service/tickets/my")
    @ApiOperation("查询分配给我的工单列表")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "查询分配给我的工单")
    public Result<PageResult<TicketListVO>> getMyTickets(@Valid TicketQueryRequest request) {
        Integer userId = TokenService.getLoginUser().getUserId();
        PageResult<TicketListVO> result = ticketManager.queryCustomerServiceTickets(userId, request);
        return Result.ofSuccess(result);
    }

    /**
     * 查询工单详情
     * 客服只能查看分配给自己的工单详情
     */
    @GetMapping("/service/tickets/{ticketId}")
    @ApiOperation("查询工单详情")
    public Result<TicketDetailVO> getTicketDetail(@ApiParam("工单ID") @PathVariable Long ticketId) {
        TicketDetailVO detail = ticketManager.getTicketDetail(ticketId);
        return Result.ofSuccess(detail);
    }

    /**
     * 确认接收工单
     * 客服确认接收分配给自己的工单
     */
    @PutMapping("/service/tickets/{ticketId}/confirm")
    @ApiOperation("确认接收工单")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "确认接收工单#ticketId")
    public Result<Void> confirmTicket(@ApiParam("工单ID") @PathVariable Long ticketId) {
        ticketManager.confirmTicket(ticketId);
        return Result.ofSuccess();
    }

    /**
     * 拒绝工单
     * 客服拒绝分配给自己的工单，需要提供拒绝原因
     */
    @PutMapping("/service/tickets/{ticketId}/reject")
    @ApiOperation("拒绝工单")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "拒绝工单#ticketId")
    public Result<Void> rejectTicket(
            @ApiParam("工单ID") @PathVariable Long ticketId,
            @ApiParam("拒绝原因") @RequestParam String reason) {
        ticketManager.rejectTicket(ticketId, reason);
        return Result.ofSuccess();
    }

    /**
     * 回复工单
     * 客服回复工单内容
     */
    @PostMapping("/service/tickets/{ticketId}/reply")
    @ApiOperation("回复工单")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "回复工单#ticketId")
    public Result<Void> replyTicket(
            @ApiParam("工单ID") @PathVariable Long ticketId,
            @RequestBody @Validated TicketReplyRequest request) {
        ticketManager.replyTicket(ticketId, request);
        return Result.ofSuccess();
    }
}
