package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.ProblemManagementManager;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementAddRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementEditRequest;
import com.zkjg.regtrace.persistence.vo.request.problem.ProblemManagementQueryRequest;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemManagementVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 问题管理控制器
 * <AUTHOR>
 */
@Api(tags = "问题管理")
@RestController
@RequestMapping("/problem")
@Slf4j
public class ProblemManagementController {

    @Resource
    private ProblemManagementManager problemManagementManager;

    /**
     * 分页查询问题管理列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询问题管理列表")
    @OperationLog(type = OperationLogType.PROBLEM_MANAGEMENT, content = "查询问题管理列表")
    public Result<PageResult<ProblemManagementVO>> queryProblemList(@Valid ProblemManagementQueryRequest request) {
        PageResult<ProblemManagementVO> result = problemManagementManager.queryProblemList(request);
        return Result.ofSuccess(result);
    }

    /**
     * 添加问题
     */
    @PostMapping("/add")
    @ApiOperation("添加问题")
    @OperationLog(type = OperationLogType.PROBLEM_MANAGEMENT, content = "添加问题：#request.problemContent")
    public Result<Long> addProblem(@Valid @RequestBody ProblemManagementAddRequest request) {
        Long id = problemManagementManager.addProblem(request);
        return Result.ofSuccess(id);
    }

    /**
     * 查询问题详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperation("查询问题详情")
    @OperationLog(type = OperationLogType.PROBLEM_MANAGEMENT, content = "查询问题详情")
    public Result<ProblemManagementVO> getProblemDetail(
            @ApiParam(value = "问题ID", required = true) @PathVariable Long id) {
        ProblemManagementVO result = problemManagementManager.getProblemDetail(id);
        return Result.ofSuccess(result);
    }

    /**
     * 编辑问题
     */
    @PostMapping("/edit")
    @ApiOperation("编辑问题")
    @OperationLog(type = OperationLogType.PROBLEM_MANAGEMENT, content = "编辑问题：#request.problemContent")
    public Result<Boolean> editProblem(@Valid @RequestBody ProblemManagementEditRequest request) {
        Boolean result = problemManagementManager.editProblem(request);
        return Result.ofSuccess(result);
    }

    /**
     * 删除问题
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除问题")
    @OperationLog(type = OperationLogType.PROBLEM_MANAGEMENT, content = "删除问题")
    public Result<Boolean> deleteProblem(
            @ApiParam(value = "问题ID", required = true) @PathVariable Long id) {
        Boolean result = problemManagementManager.deleteProblem(id);
        return Result.ofSuccess(result);
    }
}
