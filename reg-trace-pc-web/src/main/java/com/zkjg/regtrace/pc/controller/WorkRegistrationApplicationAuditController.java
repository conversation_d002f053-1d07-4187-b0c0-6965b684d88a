package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.WorkRegistrationApplicationManager;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationAuditRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationAuditDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 申请登记管理-pc端
 * @create 2025/6/11 17:54
 */
@Api(tags = "申请登记管理-pc端")
@RestController
@Slf4j
public class WorkRegistrationApplicationAuditController {

    @Resource
    private WorkRegistrationApplicationManager workRegistrationApplicationManager;

    @PostMapping("/work-register/audit")
    @ApiOperation(value = "审核音视频登记审核", httpMethod = "POST")
    @OperationLog(type = OperationLogType.REGISTRY_REVIEW, content = "审核id为#req.id的音视频登记")
    public Result<Boolean> audit(@RequestBody @Valid WorkRegistrationApplicationAuditRequest req) {
        return workRegistrationApplicationManager.audit(req);
    }

    @GetMapping("/work-register/audit/detail/{id}")
    @ApiOperation(value = "查询音视频登记审核审核详情", httpMethod = "GET")
    public Result<WorkRegistrationApplicationAuditDetailVo> auditDetail(@PathVariable Integer id) {
        return workRegistrationApplicationManager.auditDetail(id);
    }

    @GetMapping("/work-register/audit/list")
    @ApiOperation(value = "查询音视频登记审核列表", httpMethod = "GET")
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> listAudit(QueryWorkRegistrationApplicationRequest req) {
        return workRegistrationApplicationManager.listAudit(req);
    }

    @GetMapping("/work-register/list")
    @ApiOperation(value = "查询音视频登记记录列表", httpMethod = "GET")
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> list(QueryWorkRegistrationApplicationRequest req) {
        return workRegistrationApplicationManager.list(req);
    }

    @GetMapping("/work-register/detail/{id}")
    @ApiOperation(value = "查询音视频登记记录详情", httpMethod = "GET")
    public Result<WorkRegistrationApplicationDetailVo> detail(@PathVariable Integer id) {
        return workRegistrationApplicationManager.detail(id);
    }

    @GetMapping("/work-register/export/detail/{id}")
    @ApiOperation(value = "导出登记申请详情为PDF", httpMethod = "GET")
    public void exportDetailToPdf(@PathVariable Integer id, HttpServletResponse response) {
        workRegistrationApplicationManager.exportDetailToPdf(id, response);
    }

}
