package com.zkjg.regtrace.pc.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.manager.UserManager;
import com.zkjg.regtrace.persistence.vo.response.permission.UserMenuListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/20 16:51
 */
@Api(tags = "系统菜单")
@RestController
@RequestMapping("/menu")
public class MenuController {

    @Resource
    private UserManager userManager;

    @GetMapping("list")
    @ApiOperation(value = "查询用户菜单")
    public List<UserMenuListVo> selectMenu() {
        return userManager.selectMenu(TokenService.getLoginUser().getUserId());
    }
}
