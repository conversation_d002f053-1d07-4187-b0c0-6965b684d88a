<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    <title>作品登记详情</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .work-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .registration-number {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #ddd;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .info-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 150px;
        }
        .info-table td {
            background-color: white;
        }
        .record-table {
            width: 100%;
            border-collapse: collapse;
        }
        .record-table th, .record-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .record-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .record-table td {
            background-color: white;
        }
        .status-approved { color: #28a745; font-weight: bold; }
        .status-rejected { color: #dc3545; font-weight: bold; }
        .status-pending { color: #ffc107; font-weight: bold; }
        .export-button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            margin-left: 10px;
        }
    </style>
</head>

<body>
<div class="container">
    <!-- 头部信息 -->
    <div class="header">
        <div class="work-title">${workName!'未知作品'}</div>
        <div class="registration-number">登记编号：${registrationNumber!'--'}</div>
        <div class="registration-number">
            审核状态：
            <#if auditStatus == 1>
                <span class="status-pending">待审核</span>
            <#elseif auditStatus == 2>
                <span class="status-approved">审核通过</span>
            <#elseif auditStatus == 3>
                <span class="status-rejected">审核拒绝</span>
            <#else>
                <span>未知状态</span>
            </#if>
        </div>
    </div>

    <!-- 作品信息 -->
    <div class="section">
        <div class="section-title">作品信息</div>
        <table class="info-table">
            <tr>
                <th>作品类型</th>
                <td>
                    <#if fileType??>
                        <#if fileType == 0>音频
                        <#elseif fileType == 1>视频
                        <#elseif fileType == 2>图片
                        <#else>未知类型
                        </#if>
                    <#else>--
                    </#if>
                </td>
            </tr>
            <tr>
                <th>作品描述</th>
                <td>${applicationReason!'--'}</td>
            </tr>
            <tr>
                <th>登记时间</th>
                <td>${createTime!'--'}</td>
            </tr>
        </table>
    </div>

    <!-- 文件信息 -->
    <div class="section">
        <div class="section-title">文件信息</div>
        <table class="info-table">
            <tr>
                <th>文件名称</th>
                <td>${fileName!'--'}</td>
            </tr>
            <tr>
                <th>文件大小</th>
                <td>${fileSize!'--'}</td>
            </tr>
            <tr>
                <th>原始文件哈希</th>
                <td style="word-break: break-all;">${originalWorkHash!'--'}</td>
            </tr>
            <tr>
                <th>加水印文件哈希</th>
                <td style="word-break: break-all;">${watermarkedWorkHash!'--'}</td>
            </tr>
            <tr>
                <th>水印哈希</th>
                <td style="word-break: break-all;">${watermarkHash!'--'}</td>
            </tr>
            <tr>
                <th>上传时间</th>
                <td>${createTime!'--'}</td>
            </tr>
        </table>
    </div>

    <!-- 申请信息 -->
    <div class="section">
        <div class="section-title">申请信息</div>
        <table class="info-table">
            <tr>
                <th>申请人</th>
                <td>${applicantName!'--'}</td>
            </tr>
            <tr>
                <th>申请人联系方式</th>
                <td>${applicantEmail!'--'}</td>
            </tr>
            <tr>
                <th>申请时间</th>
                <td>${createTime!'--'}</td>
            </tr>
            <tr>
                <th>申请原因</th>
                <td>${applicationReason!'--'}</td>
            </tr>
        </table>
    </div>

    <!-- 操作记录 -->
    <div class="section">
        <div class="section-title">操作记录</div>
        <table class="record-table">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>操作内容</th>
                    <th>操作人</th>
                </tr>
            </thead>
            <tbody>
                <#if records?? && records?size gt 0>
                    <#list records as record>
                        <tr>
                            <td>${record.operateTime!'--'}</td>
                            <td>${record.operateContent!'--'}</td>
                            <td>${record.operator!'--'}</td>
                        </tr>
                    </#list>
                <#else>
                    <tr>
                        <td colspan="3">暂无操作记录</td>
                    </tr>
                </#if>
            </tbody>
        </table>
    </div>

    <!-- 审核意见 -->
    <#if auditComment?? && auditComment != ''>
    <div class="section">
        <div class="section-title">审核意见</div>
        <div style="padding: 15px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff;">
            ${auditComment}
        </div>
    </div>
    </#if>

    <!-- 页脚 -->
    <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;">
        <p>此文档由音视频溯源管理平台自动生成</p>
        <p>生成时间：${.now?string("yyyy-MM-dd HH:mm:ss")}</p>
    </div>
</div>
</body>
</html>
