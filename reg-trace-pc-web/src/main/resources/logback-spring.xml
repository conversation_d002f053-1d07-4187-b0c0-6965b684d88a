<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false"  scan="true" scanPeriod="30 seconds" >
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>

	<property name="LOG_HOME" value="./logs"/>
	<property name="LOG_NAME" value="pc"/>

	<!--控制台日志， 控制台输出 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出（配色：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度,%msg：日志消息，%n是换行符-->
			<pattern>%yellow(%d{yyyy-MM-dd HH:mm:ss}) %yellow([%thread]) %highlight(%-5level) %cyan(%logger{50}) - %magenta(%msg) %n</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!--文件日志， 按照每天生成日志文件 -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 日志文件输出的文件名 -->
			<fileNamePattern>${LOG_HOME}/${LOG_NAME}_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!-- 日志文件保留天数 -->
			<maxHistory>30</maxHistory>
			<!--日志文件最大的大小-->
			<maxFileSize>512MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>DENY</onMatch>
			<onMismatch>ACCEPT</onMismatch>
		</filter>
	</appender>

	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/ERROR/${LOG_NAME}_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!-- 日志文件保留天数 -->
			<maxHistory>30</maxHistory>
			<!--日志文件最大的大小-->
			<maxFileSize>512MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
		</encoder>

	</appender>
	<logger name="tech.powerjob.worker.background" level="OFF"/>
	<!-- 日志输出级别 -->
	<root level="INFO">
		<appender-ref ref="STDOUT"/>
		<appender-ref ref="FILE"/>
		<appender-ref ref="ERROR"/>
	</root>
</configuration>
