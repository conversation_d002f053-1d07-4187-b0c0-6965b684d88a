server:
  servlet:
    context-path: /customer
  port: 8081

spring:
  profiles:
    active: dev
  mvc:
    converters:
      preferred-json-mapper: jackson
  jpa:
    hibernate:
      naming:
        physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
    properties:
      hibernate:
        dialect: org.hibernate.dialect.DmDialect
  datasource:
    driver-class-name: dm.jdbc.driver.DmDriver
    url: jdbc:dm://************:5236/REG-TRACE-2?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=UTF-8
    username: REG-TRACE-2
    password: Zkjg@12345

    hikari:
      maximum-pool-size: 100
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    host: ***********
    port: 6379
    password: root123
    lettuce:
      pool:
        min-idle: 0
        max-idle: 20
        max-active: 1024
        max-wait: 1000
    database: 0
    timeout: 5000
  mail:
    host: mail.zlattice.top
    port: 465
    username: su<PERSON>yo<PERSON>@zlattice.top
    password: b!tdbklnd3D
    protocol: smtp
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
  freemarker:
    template-loader-path: classpath:/templates/
    suffix: .ftl
    charset: UTF-8
    check-template-location: true
    settings:
      number_format: '0.##'
minio:
  endpoint: http://***********:9000
  bucket: reg-trace
  accessKey: I9BT7F7DmsfHLMzo50ym
  secretKey: RpoJ6I1n1rYM49OnrOv81oHqHdAwI9GA61TlAttP
  object: reg-trace
  read-url: http://***********:9000

security:
  url-skip: "/user/login,/v2/api-docs,/webjars/**,/doc.html,/swagger-ui/**,/swagger-resources,/register/**,/helpCenter/**"

external:
  secrets:
    sm2:
      privateKey: 430f6418f474ca31f8d7ce8a8a129ba5ac1318daeff588dbd0b69e0c14331505
  service:
    email-url: www.baidu.com
    traceability-url: www.baidu.com

powerjob:
  worker:
    enabled: false
