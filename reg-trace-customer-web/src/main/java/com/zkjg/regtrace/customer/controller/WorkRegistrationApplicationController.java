package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.WorkRegistrationApplicationManager;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationFileRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationApplicationRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationDetailVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationVo;
import com.zkjg.regtrace.service.WorkRegistrationApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 申请登记管理-客户端
 * @create 2025/6/11 16:50
 */
@Api(tags = "申请登记管理-客户端")
@RestController
@Slf4j
public class WorkRegistrationApplicationController {

    @Resource
    private WorkRegistrationApplicationService workRegistrationApplicationService;

    @Resource
    private WorkRegistrationApplicationManager workRegistrationApplicationManager;

    @PostMapping("/work-register/apply")
    @ApiOperation(value = "提交音视频登记申请", httpMethod = "POST")
    @OperationLog(type = OperationLogType.REGISTER, content = "登记了作品#req.workName")
    public Result<Boolean> apply(@RequestBody @Validated WorkRegistrationApplicationRequest req) {
        return workRegistrationApplicationManager.apply(req);
    }

    @GetMapping("/work-register/search")
    @ApiOperation(value = "根据作品名称搜索作品", httpMethod = "GET")
    public Result<List<WorkRegistrationApplicationVo>> search(String keyword) {
        return workRegistrationApplicationService.findByWorkNameContainingAndAuditStatus(keyword);
    }


    @PostMapping("/work-register/trash/move/{id}")
    @ApiOperation(value = "移出到回收站", httpMethod = "POST")
    public Result<Boolean> trashMove(@PathVariable Integer id) {
        return workRegistrationApplicationManager.trashMove(id);
    }

    @PostMapping("/work-register/trash/restore/{id}")
    @ApiOperation(value = "从回收站移出", httpMethod = "POST")
    public Result<Boolean> trashRestore(@PathVariable Integer id) {
        return workRegistrationApplicationManager.trashRestore(id);
    }

    @GetMapping("/work-register/trash/list")
    @ApiOperation(value = "回收站列表", httpMethod = "GET")
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> trashList(QueryWorkRegistrationApplicationRequest req) {
        return workRegistrationApplicationManager.trashList(req);
    }

    @GetMapping("/work-register/list")
    @ApiOperation(value = "查询音视频登记列表", httpMethod = "GET")
    public Result<PageResult<WorkRegistrationApplicationDetailVo>> list(QueryWorkRegistrationApplicationRequest req) {
        return workRegistrationApplicationManager.list(req);
    }

    @GetMapping("/work-register/detail/{id}")
    @ApiOperation(value = "查询音视频登记详情", httpMethod = "GET")
    public Result<WorkRegistrationApplicationDetailVo> detail(@PathVariable Integer id) {
        return workRegistrationApplicationManager.detail(id);
    }

    @PostMapping("/work-register-detail")
    @ApiOperation(value = "查询登记文件详情", httpMethod = "POST")
    public WorkRegistrationApplicationDetailVo queryWorkFileDetail(@RequestBody QueryWorkRegistrationApplicationFileRequest req) {
        return workRegistrationApplicationManager.queryWorkFileDetail(req);
    }

    @PostMapping("/work-register/import")
    @ApiOperation(value = "导入登记申请", httpMethod = "POST")
    public Result<String> importWorkRegistrationApplication(@RequestParam("file") MultipartFile file){
        return workRegistrationApplicationManager.importWorkRegistrationApplication(file);
    }

    @GetMapping("/work-register/export/detail/{id}")
    @ApiOperation(value = "导出登记申请详情为PDF", httpMethod = "GET")
    public ResponseEntity<FileSystemResource> exportDetailToPdf(@PathVariable Integer id) {
        try {
            Result<Map<String, Object>> result = workRegistrationApplicationManager.exportDetailToPdf(id);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().build();
            }

            Map<String, Object> data = result.getData();
            String filePath = (String) data.get("filePath");
            String fileName = (String) data.get("fileName");

            File file = new File(filePath);
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            FileSystemResource resource = new FileSystemResource(file);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename*=UTF-8''" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf");

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(resource);

        } catch (Exception e) {
            log.error("导出PDF文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}

