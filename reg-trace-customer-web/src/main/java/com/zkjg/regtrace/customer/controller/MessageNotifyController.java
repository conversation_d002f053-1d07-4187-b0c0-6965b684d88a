package com.zkjg.regtrace.customer.controller;


import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.MessageNotifyManager;
import com.zkjg.regtrace.persistence.vo.request.message.BatchUpdateMessageRequest;
import com.zkjg.regtrace.persistence.vo.request.message.MessageNotifyRequest;
import com.zkjg.regtrace.persistence.vo.response.message.MessageNotifyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "消息中心")
@RestController
@RequestMapping("/message")
public class MessageNotifyController {

    @Resource
    private MessageNotifyManager messageNotifyManager;

    @PostMapping("list")
    @ApiOperation(value = "消息列表", httpMethod = "POST")
    public PageResult<MessageNotifyVo> messages(@RequestBody @Validated MessageNotifyRequest req) {
        return messageNotifyManager.messages(req);
    }

    @GetMapping("detail/{id}/{isRead}")
    @ApiOperation(value = "消息详情", httpMethod = "GET")
    @OperationLog(type = OperationLogType.MESSAGE_DETAIL, content = "#isRead")
    public MessageNotifyVo detail(@PathVariable Integer id, @PathVariable Integer isRead) {
        return messageNotifyManager.detail(id);
    }

    @GetMapping("unReadCount")
    @ApiOperation(value = "未读消息数量", httpMethod = "GET")
    public Long unReadCount() {
        return messageNotifyManager.unReadCount();
    }

    @PostMapping("/batch-operate")
    @ApiOperation("批量操作")
    @OperationLog(type = OperationLogType.MESSAGE_OPERATE)
    public void batchOperate(@RequestBody BatchUpdateMessageRequest req, HttpServletResponse response) {
        messageNotifyManager.batchOperate(req, response);
    }

}
