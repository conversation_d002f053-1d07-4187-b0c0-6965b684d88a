package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.manager.EmailManager;
import com.zkjg.regtrace.manager.SysUserManager;
import com.zkjg.regtrace.persistence.vo.request.register.RegisterRequest;
import com.zkjg.regtrace.persistence.vo.request.register.ResetPasswordRequest;
import com.zkjg.regtrace.persistence.vo.request.register.SendEmailRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/11 15:53
 */
@Api(tags = "注册")
@RestController
@RequestMapping("/register")
public class RegisterController {

    @Resource
    private SysUserManager sysUserManager;
    @Resource
    private EmailManager emailManager;
    @Resource
    private BCryptPasswordEncoder bCryptPasswordEncoder;

    @PostMapping("sendEmail")
    @ApiOperation(value = "发送邮箱验证码", httpMethod = "POST")
    public Result<String> send(@RequestBody @Validated SendEmailRequest req) {
        return Result.ofSuccess(emailManager.sendEmail(req));
    }

    @PostMapping("resetPassword")
    @ApiOperation(value = "邮箱重置密码", httpMethod = "POST")
    public void resetPassword(@RequestBody @Validated ResetPasswordRequest req) {
        req.setPassword(bCryptPasswordEncoder.encode(req.getPassword()));
        sysUserManager.resetPassword(req);
    }

    @PostMapping("register")
    @ApiOperation(value = "注册", httpMethod = "POST")
    public void register(@RequestBody @Validated RegisterRequest req) {
        req.setPassword(bCryptPasswordEncoder.encode(req.getPassword()));
        sysUserManager.register(req);
    }
}
