package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.SysUserManager;
import com.zkjg.regtrace.persistence.vo.request.userinfo.*;
import com.zkjg.regtrace.persistence.vo.response.userinfo.ImageShowVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserHistoryVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 用户信息
 * @create 2025/6/11 16:38
 */
@Api(tags = "用户中心")
@RestController
@RequestMapping("/userInfo")
public class UserInfoController {

    @Resource
    private SysUserManager sysUserManager;

    @GetMapping("detail")
    @ApiOperation(value = "用户信息详情", httpMethod = "GET")
    public UserDetailVo detail() {
        return sysUserManager.selectDetail(TokenService.getLoginUser().getUserId());
    }

    @PostMapping("edit")
    @ApiOperation(value = "修改用户信息", httpMethod = "POST")
    public void edit(@RequestBody @Validated EditUserInfoRequest req) {
        req.setUserId(TokenService.getLoginUser().getUserId());
        sysUserManager.editUser(req);
    }

    @PostMapping("list")
    @ApiOperation(value = "历史记录", httpMethod = "POST")
    public PageResult<UserHistoryVo> list(@RequestBody @Validated UserHistoryInfoListRequest req) {
        req.setUserIdEq(TokenService.getLoginUser().getUserId());
        return sysUserManager.selectList(req);
    }

    @PostMapping("reset")
    @ApiOperation(value = "恢复个人信息", httpMethod = "POST")
    public void reset(@RequestBody @Validated ResetUserinfoRequest req) {
        sysUserManager.resetUser(req);
    }

    @PostMapping("resetPermission")
    @ApiOperation(value = "重置权限", httpMethod = "POST")
    public void resetPermission(@RequestBody @Validated EditUserPermissionRequest req) {
        sysUserManager.resetPermission(req);
    }

    @PostMapping("changeHeadImg")
    @ApiOperation(value = "修改头像", httpMethod = "POST")
    public void changeHeadImg(@RequestBody @Validated ChangeUserHeadImgRequest req) {
        sysUserManager.changeHeadImg(req);
    }

    @PostMapping("uploadImage")
    @ApiOperation("上传图片")
    public ImageShowVo uploadImage(@RequestParam("file") MultipartFile file) {
        return sysUserManager.uploadInfoImage(file, TokenService.getLoginUser(), "userinfo");
    }
}
