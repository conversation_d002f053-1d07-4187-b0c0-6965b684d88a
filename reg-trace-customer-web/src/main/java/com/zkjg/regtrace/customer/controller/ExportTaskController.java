package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import com.zkjg.regtrace.manager.ExportTaskManager;
import com.zkjg.regtrace.persistence.vo.response.schedule.ExportTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 导出任务相关接口
 */
@Api(tags = "导出任务管理")
@RestController
@RequestMapping("/export-task")
public class ExportTaskController {

    @Resource
    private ExportTaskManager exportTaskManager;

    /**
     * 修改导出任务的调度类型
     *
     * @param id           任务ID
     * @param scheduleType 调度类型（枚举）
     */
    @ApiOperation("修改导出任务计划类型")
    @PostMapping("/modify")
    public void updateScheduleType(
            @ApiParam("任务ID") @RequestParam (required = false) Integer id,
            @ApiParam("计划任务类型（NONE、WEEKLY、MONTHLY）")
            @RequestParam ExportTaskScheduleTypeEnum scheduleType) {
        exportTaskManager.updateScheduleType(id, scheduleType);
    }

    @GetMapping("/my")
    @ApiOperation("我的导出任务")
    public ExportTaskVo getMyTask() {
        return exportTaskManager.getTaskByCreatorId();
    }
}
