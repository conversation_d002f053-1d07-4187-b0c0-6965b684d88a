package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkTemplateQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkTemplateRequest;
import com.zkjg.regtrace.persistence.vo.response.watermark.TemplateChangeHistoryVO;
import com.zkjg.regtrace.persistence.vo.response.watermark.WatermarkTemplateVO;
import com.zkjg.regtrace.service.WatermarkTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 客户端水印模板控制器
 */
@RestController
@RequestMapping("/watermark/template")
@Api(tags = "客户端水印模板管理接口")
public class WatermarkTemplateController {

    @Resource
    private WatermarkTemplateService watermarkTemplateService;

    /**
     * 创建水印模板
     */
    @PostMapping
    @ApiOperation("创建水印模板")
    @OperationLog(type = OperationLogType.TEMPLATE_MANAGEMENT, content = "创建水印模板#reqVO.templateName")
    public Result<Void> createTemplate(@RequestBody @Valid WatermarkTemplateRequest reqVO) {
        Integer userId = TokenService.getLoginUser().getUserId();
        watermarkTemplateService.createTemplate(userId, reqVO);
        return Result.ofSuccess();
    }

    /**
     * 更新水印模板
     */
    @PutMapping
    @ApiOperation("更新水印模板")
    @OperationLog(type = OperationLogType.TEMPLATE_MANAGEMENT, content = "更新水印模板#reqVO.templateName")
    public Result<Void> updateTemplate(@RequestBody @Valid WatermarkTemplateRequest reqVO) {
        Integer userId = TokenService.getLoginUser().getUserId();
        watermarkTemplateService.updateTemplate(userId, reqVO);
        return Result.ofSuccess();
    }

    /**
     * 查询水印模板列表
     */
    @PostMapping("/query")
    @ApiOperation("查询水印模板列表")
    public Result<List<WatermarkTemplateVO>> queryTemplates(@RequestBody WatermarkTemplateQueryRequest queryVO) {
        Integer userId = TokenService.getLoginUser().getUserId();
        List<WatermarkTemplateVO> templateVOList = watermarkTemplateService.queryTemplates(userId, queryVO);
        return Result.ofSuccess(templateVOList);
    }

    /**
     * 获取水印模板详情
     */
    @GetMapping("/{templateId}")
    @ApiOperation("获取水印模板详情")
    public Result<WatermarkTemplateVO> getTemplateDetail(@PathVariable Integer templateId) {
        Integer userId = TokenService.getLoginUser().getUserId();
        WatermarkTemplateVO templateVO = watermarkTemplateService.getTemplateDetail(userId, templateId);
        return Result.ofSuccess(templateVO);
    }

    /**
     * 删除水印模板
     */
    @DeleteMapping("/{templateId}")
    @ApiOperation("删除水印模板")
    public Result<Boolean> deleteTemplate(@PathVariable Integer templateId) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Boolean result = watermarkTemplateService.deleteTemplate(userId, templateId);
        return Result.ofSuccess(result);
    }

    /**
     * 获取模板变更记录
     *
     * @return 变更记录列表
     */
    @GetMapping("/history")
    @ApiOperation("分页获取模板变更记录")
    public Result<PageResult<TemplateChangeHistoryVO>> getTemplateChangeHistory(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        Integer userId = TokenService.getLoginUser().getUserId();
        PageResult<TemplateChangeHistoryVO> pageResult = watermarkTemplateService
                .getTemplateChangeHistory(userId, pageNum, pageSize);
        return Result.ofSuccess(pageResult);
    }


    @PostMapping("/history/{historyId}/rollback")
    @ApiOperation("回滚模板变更")
    public Result<Boolean> rollbackTemplateChange(@PathVariable Long historyId) {
        Integer userId = TokenService.getLoginUser().getUserId();
        boolean result = watermarkTemplateService.rollbackTemplateChange(userId, historyId);
        return Result.ofSuccess(result);
    }
}