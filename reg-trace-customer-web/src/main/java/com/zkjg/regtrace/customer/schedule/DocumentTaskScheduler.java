package com.zkjg.regtrace.customer.schedule;

import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.common.utils.RedisUtil;
import com.zkjg.regtrace.persistence.entity.DocumentDO;
import com.zkjg.regtrace.persistence.jpa.DocumentJpaSpec;
import com.zkjg.regtrace.service.DocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 导出任务定时调度器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentTaskScheduler {

    private final DocumentService documentService;

    private final RedisUtil redisUtil;

    /**
     * 每天凌晨 0 点检查执行同步文档浏览、下载量任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void executeScheduledTasks() {
        log.debug("开始执行定时同步文档浏览、下载量任务...");
        Set<String> keys = redisUtil.keys("reg-trace:document:*");
        if (CollectionUtils.isEmpty(keys)) {
            log.debug("无 Redis 记录，任务结束。");
            return;
        }

        Map<Integer, DocumentDO> docUpdateMap = new HashMap<>();

        for (String key : keys) {
            String value = redisUtil.get(key);
            if (value == null) continue;

            String[] parts = key.split(":");
            if (parts.length < 4) continue; // 错误 key 忽略

            Integer docId = Integer.valueOf(parts[3]);
            String type = parts[2]; // view 或 download

            DocumentDO doc = docUpdateMap.computeIfAbsent(docId, id -> {
                DocumentDO d = new DocumentDO();
                d.setId(id);
                return d;
            });

            if ("view".equals(type)) {
                doc.setViews(Long.valueOf(value));
            } else if ("download".equals(type)) {
                doc.setDownloads(Long.valueOf(value));
            }

            redisUtil.delete(key); // 删除 key
        }

        if (docUpdateMap.isEmpty()) {
            log.debug("无有效文档数据，任务结束。");
            return;
        }

        // 查询原始记录
        List<Integer> ids = new ArrayList<>(docUpdateMap.keySet());
        DocumentJpaSpec spec = new DocumentJpaSpec();
        spec.setIdIn(ids);
        Specification<DocumentDO> specification = QueryConvertUtils.toSpecification(spec);
        List<DocumentDO> dbDocuments = documentService.findAll(specification);

        for (DocumentDO dbDoc : dbDocuments) {
            DocumentDO redisDoc = docUpdateMap.get(dbDoc.getId());
            if (redisDoc.getViews() != null) {
                dbDoc.setViews(redisDoc.getViews());
            }
            if (redisDoc.getDownloads() != null) {
                dbDoc.setDownloads(redisDoc.getDownloads());
            }
        }

        documentService.saveAll(dbDocuments);
        log.debug("定时同步文档浏览、下载量任务执行完成。");
    }
}

