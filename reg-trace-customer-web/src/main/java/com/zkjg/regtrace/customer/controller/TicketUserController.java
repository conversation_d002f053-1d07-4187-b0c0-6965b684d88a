package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.TicketManager;
import com.zkjg.regtrace.persistence.vo.request.ticket.*;
import com.zkjg.regtrace.persistence.vo.response.ticket.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户工单控制器
 * 负责用户的工单创建和管理操作
 * <AUTHOR>
 */
@Api(tags = "用户-工单管理")
@RestController
@Slf4j
public class TicketUserController {

    @Resource
    private TicketManager ticketManager;

    /**
     * 创建工单
     * 用户创建新的工单
     */
    @PostMapping("/user/tickets")
    @ApiOperation("创建工单")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "创建工单")
    public Result<Long> createTicket(@RequestBody @Validated TicketCreateRequest request) {
        Long ticketId = ticketManager.createTicket(request);
        return Result.ofSuccess(ticketId);
    }

    /**
     * 查询我的工单列表
     * 用户只能查看自己创建的工单
     */
    @GetMapping("/user/tickets")
    @ApiOperation("查询我的工单列表")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "查询工单列表")
    public Result<PageResult<TicketListVO>> getMyTickets(@Valid TicketQueryRequest request) {
        PageResult<TicketListVO> result = ticketManager.queryUserTickets(request);
        return Result.ofSuccess(result);
    }

    /**
     * 查询工单详情
     * 用户只能查看自己创建的工单详情
     */
    @GetMapping("/user/tickets/{ticketId}")
    @ApiOperation("查询工单详情")
    public Result<TicketDetailVO> getTicketDetail(@ApiParam("工单ID") @PathVariable Long ticketId) {
        TicketDetailVO detail = ticketManager.getTicketDetail(ticketId);
        return Result.ofSuccess(detail);
    }

    /**
     * 确认问题已解决
     * 用户确认工单问题已解决，关闭工单
     */
    @PutMapping("/user/tickets/{ticketId}/resolve")
    @ApiOperation("确认问题已解决")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "确认工单已解决#ticketId")
    public Result<Void> resolveTicket(@ApiParam("工单ID") @PathVariable Long ticketId) {
        ticketManager.resolveTicket(ticketId);
        return Result.ofSuccess();
    }

    /**
     * 用户追问
     * 用户对工单进行追问或补充说明
     */
    @PostMapping("/user/tickets/{ticketId}/follow-ups")
    @ApiOperation("用户追问")
    @OperationLog(type = OperationLogType.CUSTOMER_SERVICE, content = "工单追问#ticketId")
    public Result<Void> followUpTicket(
            @ApiParam("工单ID") @PathVariable Long ticketId,
            @RequestBody @Validated TicketFollowUpRequest request) {
        Integer userId = TokenService.getLoginUser().getUserId();
        ticketManager.followUpTicket(ticketId, request, userId);
        return Result.ofSuccess();
    }
}
