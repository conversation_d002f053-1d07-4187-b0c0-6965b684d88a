package com.zkjg.regtrace.customer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/10 14:21
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.zkjg.regtrace"})
@EnableJpaRepositories(basePackages = "com.zkjg.regtrace.persistence.repository")
@EntityScan("com.zkjg.regtrace.persistence.entity")
@EnableScheduling
public class RegTraceCustomerWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(RegTraceCustomerWebApplication.class, args);
    }
}
