package com.zkjg.regtrace.customer.schedule;

import com.zkjg.regtrace.manager.TicketManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 工单定时任务
 * <AUTHOR>
 */
@Slf4j
@Component
public class TicketScheduledTask {

    @Resource
    private TicketManager ticketManager;

    /**
     * 每5分钟检查一次超时未确认的工单
     */
    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void handleTimeoutTickets() {
        log.info("开始检查超时未确认的工单");
        try {
            ticketManager.handleTimeoutTickets();
            log.info("超时工单处理完成");
        } catch (Exception e) {
            log.error("处理超时工单时发生错误", e);
        }
    }

    /**
     * 每小时检查一次用户超时未确认的工单（超过7天自动关闭）
     */
    @Scheduled(fixedDelay = 60 * 60 * 1000)
    public void handleUserTimeoutTickets() {
        log.info("开始检查用户超时未确认的工单");
        try {
            ticketManager.handleUserTimeoutTickets();
            log.info("用户超时工单处理完成");
        } catch (Exception e) {
            log.error("处理用户超时工单时发生错误", e);
        }
    }
}
