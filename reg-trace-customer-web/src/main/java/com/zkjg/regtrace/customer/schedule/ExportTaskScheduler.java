package com.zkjg.regtrace.customer.schedule;

import com.zkjg.regtrace.common.config.MinioConfig;
import com.zkjg.regtrace.common.constants.MessageCategoryConstant;
import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import com.zkjg.regtrace.common.enums.MessageEnum;
import com.zkjg.regtrace.common.enums.MessageTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.entity.ExportTaskDO;
import com.zkjg.regtrace.persistence.repository.ExportTaskRepository;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRecordRequest;
import com.zkjg.regtrace.service.MessageNotifyService;
import com.zkjg.regtrace.service.TraceabilityService;
import com.zkjg.regtrace.service.UploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 导出任务定时调度器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExportTaskScheduler {

    private final ExportTaskRepository exportTaskRepository;

    private final TraceabilityService traceabilityService;

    private final UploadService uploadService;

    private final MessageNotifyService messageNotifyService;

    private final MinioConfig minioConfig;

    /**
     * 每天凌晨 0 点检查执行 WEEKLY、MONTHLY 的导出任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void executeScheduledTasks() {
        log.debug("开始执行定时导出任务...");

        List<ExportTaskDO> tasks = exportTaskRepository.findAllByScheduleTypeIsNot(ExportTaskScheduleTypeEnum.NONE);
        LocalDate today = LocalDate.now();

        for (ExportTaskDO task : tasks) {
            ExportTaskScheduleTypeEnum type = task.getScheduleType();
            Integer creatorId = task.getCreatorId();

            switch (type) {
                case WEEKLY:
                    if (today.getDayOfWeek().getValue() == 1) {
                        LocalDate lastWeekStart = today.minusWeeks(1).with(DayOfWeek.MONDAY);
                        LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
                        LocalDateTime startTime = lastWeekStart.atStartOfDay();
                        LocalDateTime endTime = lastWeekEnd.atTime(LocalTime.MAX);
                        triggerExport(creatorId, startTime, endTime);
                    }
                    break;
                case MONTHLY:
                    if (today.getDayOfMonth() == 1) {
                        LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
                        LocalDate lastDayOfLastMonth = firstDayOfLastMonth.withDayOfMonth(firstDayOfLastMonth.lengthOfMonth());
                        LocalDateTime startTime = firstDayOfLastMonth.atStartOfDay();
                        LocalDateTime endTime = lastDayOfLastMonth.atTime(LocalTime.MAX);
                        triggerExport(creatorId, startTime, endTime);
                    }
                    break;
                default:
                    break;
            }
        }
        log.debug("定时导出任务执行完成。");
    }

    /**
     * 执行导出逻辑
     */
    private void triggerExport(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        TraceabilityRecordRequest req = TraceabilityRecordRequest.builder()
                .page(1)
                .size(Integer.MAX_VALUE)
                .startTime(startTime)
                .endTime(endTime)
                .userId(userId)
                .build();
        Page<TraceabilityRecordDto> recordDtos = traceabilityService.pageQueryTraceabilityRecord(req);
        String fileName = "定时溯源记录导出-" + LocalDateTime.now();
        try {
            UniversalExportUtil.ExportFile csv = UniversalExportUtil.export(recordDtos.getContent(), TraceabilityRecordDto.class, UniversalExportUtil.ExportFormat.CSV, fileName);
            //转MultipartFile
            MultipartFile multipartFile = new MockMultipartFile(
                    csv.getFileName(),
                    csv.getFileName(),
                    csv.getContentType(),
                    csv.getInputStream()
            );
            String path = minioConfig.getReadUrl() + uploadService.upload(multipartFile);
            String content = String.format(MessageEnum.TRACEABILITY_SCHEDULE_EXPORT.getContent(), startTime, endTime, path);
            //发送消息通知
            messageNotifyService.sendMessageNotify(
                    userId,
                    null,
                    content,
                    MessageEnum.TRACEABILITY_SCHEDULE_EXPORT.getSummary(),
                    MessageCategoryConstant.TRACEABILITY_MESSAGE,
                    MessageTypeEnum.INTERNAL.getType(),
                    StatusEnum.YES.getCode(),
                    MessageEnum.TRACEABILITY_SCHEDULE_EXPORT.getLevel()
            );
        } catch (Exception e) {
            log.error("定时任务导出溯源记录文件异常, error:{}", e.getMessage());
        }
    }
}
