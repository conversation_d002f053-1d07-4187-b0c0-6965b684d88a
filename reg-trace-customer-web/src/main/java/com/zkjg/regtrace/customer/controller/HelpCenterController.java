package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.DocumentManager;
import com.zkjg.regtrace.manager.ProblemManagementManager;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.document.HelpCenterRequest;
import com.zkjg.regtrace.persistence.vo.response.document.DocumentVo;
import com.zkjg.regtrace.persistence.vo.response.problem.ProblemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/7/18 10:57
 */
@Api(tags = "帮助中心")
@RestController
@RequestMapping("")
@Validated
public class HelpCenterController {

    @Resource
    private DocumentManager documentManager;

    @Resource
    private ProblemManagementManager problemManagementManager;

    @GetMapping("/helpCenter/files")
    @ApiOperation(value = "操作指南/政策文件", httpMethod = "GET")
    public PageResult<DocumentVo> operationGuide(@Valid HelpCenterQueryRequest request) {
        return documentManager.selectOperationGuide(request);
    }

    @GetMapping("/helpCenter/click/{id}")
    @ApiOperation(value = "查看/下载", httpMethod = "GET")
    public void click(@PathVariable Integer id, HelpCenterRequest request) {
        documentManager.click(id, request);
    }

    @GetMapping("/helpCenter/problems")
    @ApiOperation(value = "常见问题", httpMethod = "GET")
    public PageResult<ProblemVo> questions(@Valid HelpCenterQueryRequest request) {
        return problemManagementManager.selectProblemList(request);
    }

}
