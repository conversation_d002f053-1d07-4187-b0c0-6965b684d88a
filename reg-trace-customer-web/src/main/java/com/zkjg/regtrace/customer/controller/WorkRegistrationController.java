package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.WorkRegistrationManager;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.QueryWorkRegistrationApplicationFileListRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationFileVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationContentDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/6/18 9:18
 */
@Api(tags = "内容管理")
@RestController
public class WorkRegistrationController {

    @Resource
    private WorkRegistrationManager workRegistrationManager;

    @GetMapping("/work-register/files")
    @ApiOperation(value = "内容管理列表（登记成功的内容）", httpMethod = "GET")
    public Result<PageResult<WorkRegistrationApplicationFileVo>> getContentList(QueryWorkRegistrationApplicationFileListRequest request) {
        PageResult<WorkRegistrationApplicationFileVo> pageResult = workRegistrationManager.getContent(request);
        return Result.ofSuccess(pageResult);
    }

    @GetMapping("/work-register/files/{id}")
    @ApiOperation(value = "内容管理详情", httpMethod = "GET")
    public Result<WorkRegistrationContentDetailVo> getContentDetail(@PathVariable Integer id) {
        WorkRegistrationContentDetailVo pageResult = workRegistrationManager.getContentDetail(id);
        return Result.ofSuccess(pageResult);
    }

    @DeleteMapping("/work-register/files/{id}")
    @ApiOperation(value = "删除内容", httpMethod = "DELETE")
    public Result<Boolean> deleteContent(@PathVariable Integer id) {
        return workRegistrationManager.recycleContent(id, SqlConstant.DELETED);
    }

    @PostMapping("/work-register/files/restore/{id}")
    @ApiOperation(value = "恢复内容", httpMethod = "POST")
    public Result<Boolean> restoreContent(@PathVariable Integer id) {
        return workRegistrationManager.recycleContent(id, SqlConstant.UN_DELETED);
    }
}
