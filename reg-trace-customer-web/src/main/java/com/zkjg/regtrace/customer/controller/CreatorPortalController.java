package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.manager.WorkRegistrationManager;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationGrowthVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationRetraceVo;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserRegistrationTrendVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/16 14:46
 */
@RestController
@Api(tags = "创作者门户")
public class CreatorPortalController {

    @Resource
    private WorkRegistrationManager workRegistrationManager;

    @GetMapping("/creator-portal/trend")
    @ApiOperation(value = "创作登记总量趋势", httpMethod = "GET")
    public Result<List<UserRegistrationTrendVo>> getRegistrationTrend(
            @ApiParam(value = "时间类型:day, month, year", required = true)
            @RequestParam String type,
            @ApiParam(value = "时间选择:day对应yyyy-mm-dd, month对应yyyy-mm, year对应yyyy", required = true)
            @RequestParam String date) {
        List<UserRegistrationTrendVo> voList = workRegistrationManager.getUserRegistrationTrend(type, date);
        return Result.ofSuccess(voList);
    }

    @GetMapping("/creator-portal/growth")
    @ApiOperation(value = "创作登记增长率", httpMethod = "GET")
    public Result<List<UserRegistrationGrowthVo>> getGrowthRate(
            @ApiParam(value = "时间类型:month, year", required = true)
            @RequestParam String type,
            @ApiParam(value = "时间选择:month对应yyyy-mm, year对应yyyy", required = true)
            @RequestParam String date) {
        List<UserRegistrationGrowthVo> voList = workRegistrationManager.getGrowthRate(type, date);
        return Result.ofSuccess(voList);
    }

    @GetMapping("/creator-portal/retrace")
    @ApiOperation(value = "全景追溯视图", httpMethod = "GET")
    public Result<List<UserRegistrationRetraceVo>> getRetrace() {
        List<UserRegistrationRetraceVo> voList = workRegistrationManager.getRetrace();
        return Result.ofSuccess(voList);
    }

    @GetMapping("/creator-portal/trend/excel")
    @ApiOperation(value = "用户登记统计导出excel", httpMethod = "GET")
    public void getTrendExcel(
            HttpServletResponse response,
            @ApiParam(value = "时间类型:day, month, year", required = true)
            @RequestParam String type) {
        workRegistrationManager.getRegistrationTrendExcel(response, type);
    }

    @GetMapping("/creator-portal/growth/excel")
    @ApiOperation(value = "登记增长率导出excel", httpMethod = "GET")
    public void getGrowthRateExcel(
            HttpServletResponse response,
            @ApiParam(value = "时间类型:day, month, year", required = true)
            @RequestParam String type) {
        workRegistrationManager.getGrowthRateExcel(response, type);
    }

    @GetMapping("/creator-portal/retrace/excel")
    @ApiOperation(value = "全景追溯导出excel", httpMethod = "GET")
    public void getRetraceExcel(HttpServletResponse response) {
        workRegistrationManager.getRetraceExcel(response);
    }
}
