package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.TraceabilityManager;
import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRecordRequest;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.response.stats.StatsVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.BatchTraceabilityVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityDetailVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "溯源")
@RestController
@RequestMapping("/traceability/")
public class TraceabilityController {

    @Resource
    private TraceabilityManager traceabilityManager;

    @PostMapping("fileTrace")
    @ApiOperation(value = "新增溯源-文件溯源", httpMethod = "POST")
    @OperationLog(type = OperationLogType.CONDITIONAL_TRACE, content = "溯源了文件#req.fileName")
    public TraceabilityResultVo addTraceability(@RequestBody TraceabilityRequest req) {
        return traceabilityManager.addTraceability(req);
    }

    @PostMapping("conditionTrace")
    @ApiOperation(value = "新增溯源-条件溯源", httpMethod = "POST")
    @OperationLog(type = OperationLogType.CONDITIONAL_TRACE, content = "溯源了文件#req.fileName")
    public TraceabilityResultVo conditionTraceability(@RequestBody ConditionalTraceabilityRequest req) {
        return traceabilityManager.conditionTraceability(req);
    }

    @PostMapping("batchTrace")
    @ApiOperation(value = "新增溯源-批量条件溯源", httpMethod = "POST")
    @OperationLog(type = OperationLogType.CONDITIONAL_TRACE, content = "批量溯源了文件#file.name")
    public BatchTraceabilityVo addTraceability(@RequestParam("file") MultipartFile file) {
        return traceabilityManager.batchAddTraceability(file);
    }

    @PostMapping("queryRecords")
    @ApiOperation(value = "溯源记录", httpMethod = "POST")
    public PageResult<TraceabilityRecordDto> queryTraceabilityRecord(@RequestBody @Validated TraceabilityRecordRequest req) {
        return traceabilityManager.queryTraceabilityRecords(req);
    }

    @GetMapping("detail/{traceabilityNumber}")
    @ApiOperation(value = "溯源记录详情", httpMethod = "GET")
    public TraceabilityDetailVo traceabilityDetail(@PathVariable String traceabilityNumber) {
        return traceabilityManager.traceabilityDetail(traceabilityNumber);
    }

    @GetMapping("exportRecords")
    @ApiOperation(value = "导出溯源记录", httpMethod = "GET")
    public void export(HttpServletResponse response,
                       @RequestBody @Validated TraceabilityRecordRequest req,
                       @RequestParam(defaultValue = "EXCEL", required = false) String type) {
        traceabilityManager.exportTraceabilityRecord(req, response, type);
    }

    @GetMapping("traceStatistics")
    @ApiOperation(value = "溯源认证统计")
    public StatsVo traceStatistics(@RequestParam Integer days) {
        return traceabilityManager.traceStatistics(days);
    }
}
