package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.manager.CreateUserNavigationManager;
import com.zkjg.regtrace.manager.TraceabilityManager;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.ContentManagerQueryRequest;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.DataTableRequest;
import com.zkjg.regtrace.persistence.vo.request.createUserNavigation.TraceRecordRequest;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "创作用户导航")
@RestController
@RequestMapping("/createUser/navigation")
public class CreateUserNavigationController {

    @Resource
    private CreateUserNavigationManager createUserNavigationManager;

    @Resource
    private TraceabilityManager traceabilityManager;

    @PostMapping("registerStatistics")
    @ApiOperation(value = "创作登记统计表格（近七天）", httpMethod = "POST")
    public List<DataTableVo> registerStatisticsList() {
        return createUserNavigationManager.registerStatisticsList();
    }

    // @PostMapping("exportRegisterStatistics")
    // @ApiOperation(value = "导出创作登记统计", httpMethod = "POST")
    // public void exportRegisterStatistics(HttpServletResponse response,
    //                             @RequestParam(defaultValue = "EXCEL") String type) {
    //     createUserNavigationManager.exportRegisterStatistics(response, type);
    // }

    @PostMapping("increaseLine")
    @ApiOperation(value = "创作登记新增趋势图", httpMethod = "POST")
    public List<DataLineVo> increaseLine(@RequestBody DataTableRequest request) {
        return createUserNavigationManager.increaseLine(request);
    }

    @PostMapping("contentManager")
    @ApiOperation(value = "内容管理分页查询", httpMethod = "POST")
    public PageResult<ContentManagerVo> queryContentManager(@RequestBody @Valid ContentManagerQueryRequest req) {
        return createUserNavigationManager.contentManager(req);
    }

    // @PostMapping("exportContentManager")
    // @ApiOperation(value = "导出内容管理", httpMethod = "POST")
    // public void exportContentManager(HttpServletResponse response,
    //                                  @RequestBody @Valid ContentManagerQueryRequest req,
    //                                  @RequestParam(defaultValue = "EXCEL") String type) {
    //     createUserNavigationManager.exportContentManager(req, response, type);
    // }

    @PostMapping("traceStatistics")
    @ApiOperation(value = "追踪统计 柱状图", httpMethod = "POST")
    public List<TraceStatisticsVo> traceStatistics() {
        return createUserNavigationManager.traceStatistics();
    }

    @PostMapping("queryTraceRecords")
    @ApiOperation(value = "溯源记录", httpMethod = "POST")
    public PageResult<TraceaRecordVo> queryTraceRecords(@RequestBody @Validated TraceRecordRequest req) {
        return createUserNavigationManager.queryTraceRecords(req);
    }

    @GetMapping("traceDetail/{id}")
    @ApiOperation(value = "溯源详情", httpMethod = "GET")
    public TraceRecordDetailVo getTraceRecordDetail(@PathVariable Integer id) {
        return createUserNavigationManager.getTraceRecordDetail(id);
    }

}
