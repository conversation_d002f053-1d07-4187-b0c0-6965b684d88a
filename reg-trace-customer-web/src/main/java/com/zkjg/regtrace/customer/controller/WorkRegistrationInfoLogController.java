package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.persistence.vo.request.workRegistration.WorkRegistrationInfoLogRequest;
import com.zkjg.regtrace.service.WorkRegistrationInfoLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/6/12 17:00
 */
@Api(tags = "音视频登记错误日志管理-客户端")
@RestController
public class WorkRegistrationInfoLogController {

    @Resource
    private WorkRegistrationInfoLogService workRegistrationInfoLogService;

    @ApiOperation(value = "保存音视频登记错误日志", httpMethod = "POST")
    @PostMapping("/work-register/log")
    public Result<Boolean> log(@RequestBody @Validated WorkRegistrationInfoLogRequest req){
        workRegistrationInfoLogService.save(req);
        return Result.ofSuccess(true);
    }

}
