package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.auth.token.TokenService;
import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.common.annotation.OperationLog;
import com.zkjg.regtrace.common.enums.OperationLogType;
import com.zkjg.regtrace.common.page.PageResult;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkFavoriteRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkSearchRequest;
import com.zkjg.regtrace.persistence.vo.request.watermark.WatermarkUploadRequest;
import com.zkjg.regtrace.persistence.vo.response.watermark.WatermarkVO;
import com.zkjg.regtrace.service.WatermarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 客户端水印管理控制器
 */
@RestController
@RequestMapping("/watermark")
@Api(tags = "用户户端水印管理接口")
public class WatermarkController {

    @Resource
    private WatermarkService watermarkService;

    /**
     * 批量上传水印
     */
    @PostMapping(value = "/upload", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("批量上传水印")
    @OperationLog(type = OperationLogType.WATERMARK_MANAGEMENT, content = "批量上传水印")
    public Result<Void> uploadWatermarks(@RequestBody @Valid List<WatermarkUploadRequest> reqVOList) {
        Integer userId = TokenService.getLoginUser().getUserId();
        watermarkService.uploadWatermarks(userId, reqVOList);
        return Result.ofSuccess();
    }

    /**
     * 分页获取我的水印列表（无搜索条件）
     */
    @GetMapping("/my-list")
    @ApiOperation("分页获取我的水印列表")
    public Result<PageResult<WatermarkVO>> getMyWatermarks(@RequestParam int pageNum, @RequestParam int pageSize) {
        Integer userId = TokenService.getLoginUser().getUserId();
        PageResult<WatermarkVO> page = watermarkService.getMyWatermarks(userId, pageNum, pageSize);
        return Result.ofSuccess(page);
    }

    /**
     * 分页搜索平台共享水印，收藏的排前面
     */
    @PostMapping("/search")
    @ApiOperation("搜索水印（分页、只查共享、收藏排前）")
    public Result<PageResult<WatermarkVO>> searchWatermark(@RequestBody WatermarkSearchRequest reqVO) {
        Integer userId = TokenService.getLoginUser().getUserId();
        PageResult<WatermarkVO> page = watermarkService.searchWatermark(userId, reqVO);
        return Result.ofSuccess(page);
    }

    /**
     * 获取水印详情
     */
    @GetMapping("/{watermarkId}")
    @ApiOperation("获取水印详情")
    public Result<WatermarkVO> getWatermarkDetail(@PathVariable Integer watermarkId) {
        Integer userId = TokenService.getLoginUser().getUserId();
        WatermarkVO watermarkVO = watermarkService.getWatermarkDetail(userId, watermarkId);
        return Result.ofSuccess(watermarkVO);
    }


    /**
     * 收藏水印
     */
    @PostMapping("/favorite")
    @ApiOperation("收藏水印")
    public Result<Boolean> addFavorite(@RequestBody @Valid WatermarkFavoriteRequest reqVO) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Boolean result = watermarkService.addFavorite(userId, reqVO);
        return Result.ofSuccess(result);
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/favorite/{watermarkId}")
    @ApiOperation("取消收藏")
    public Result<Boolean> cancelFavorite(@PathVariable Integer watermarkId) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Boolean result = watermarkService.cancelFavorite(userId, watermarkId);
        return Result.ofSuccess(result);
    }

    /**
     * 获取用户收藏的水印列表
     */
    @GetMapping("/favorite/list")
    @ApiOperation("获取用户收藏的水印列表")
    public Result<List<WatermarkVO>> getUserFavorites() {
        Integer userId = TokenService.getLoginUser().getUserId();
        List<WatermarkVO> watermarkVOList = watermarkService.getUserFavorites(userId);
        return Result.ofSuccess(watermarkVOList);
    }

    /**
     * 检查水印名称是否已存在
     */
    @GetMapping("/check-name")
    @ApiOperation("检查水印名称是否已存在")
    public Result<Boolean> checkWatermarkNameExists(@RequestParam String name) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Boolean exists = watermarkService.checkNameExists(userId, name);
        return Result.ofSuccess(exists);
    }

    /**
     * 删除水印
     */
    @DeleteMapping("/{watermarkId}")
    @ApiOperation("删除水印")
    public Result<Boolean> deleteWatermark(@PathVariable Integer watermarkId) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Boolean result = watermarkService.deleteWatermark(userId, watermarkId);
        return Result.ofSuccess(result);
    }

    /**
     * 设置水印共享状态
     */
    @PostMapping("/set-shared")
    @ApiOperation("设置水印共享状态")
    public Result<Boolean> setWatermarkShared(@RequestParam Integer watermarkId, @RequestParam Integer isShared) {
        Integer userId = TokenService.getLoginUser().getUserId();
        Boolean result = watermarkService.setWatermarkShared(userId, watermarkId, isShared);
        return Result.ofSuccess(result);
    }


    @GetMapping("detail/{hash}")
    @ApiOperation(value = "哈希查询水印详情", httpMethod = "GET")
    public WatermarkVO watermarkDetailByHash(@PathVariable String hash) {
        return watermarkService.watermarkDetailByHash(hash);
    }

    /**
     * 获取热门水印排名
     */
    @GetMapping("/popular")
    @ApiOperation("获取热门水印排名")
    public Result<List<WatermarkVO>> getPopularWatermarks(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) Integer timeRange) {
        Integer userId = TokenService.getLoginUser().getUserId();
        List<WatermarkVO> watermarks = watermarkService.getPopularWatermarks(userId, limit, timeRange);
        return Result.ofSuccess(watermarks);
    }
}
