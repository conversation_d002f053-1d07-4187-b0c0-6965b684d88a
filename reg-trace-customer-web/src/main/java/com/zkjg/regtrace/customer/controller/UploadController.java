package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.service.UploadService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@RestController
public class UploadController {

    @Resource
    private UploadService uploadService;

    @PostMapping("file-upload")
    public String upload(@RequestPart("file") MultipartFile file) {
        return uploadService.upload(file);
    }
}