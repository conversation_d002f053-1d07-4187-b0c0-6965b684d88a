package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.exceptions.BusinessException;
import com.zkjg.regtrace.service.UniversalExportService;
import com.zkjg.regtrace.service.export.ExportRegistry;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 通用导出控制器
 */
@RestController
@RequestMapping("/api/export")
@Api(tags = "通用导出管理")
@Slf4j
public class UniversalExportController {

    @Resource
    private UniversalExportService universalExportService;

    @Resource
    private ExportRegistry exportRegistry;

    /**
     * 通用导出接口
     * @param exportId 导出ID 目前有work-registration-application代表音视频登记申请导出(用户客户侧)
     * @param format 导出格式（EXCEL/CSV）
     * @param requestParams 查询参数，传递给导出定义中的数据提取器
     * @param response HTTP响应对象
     */
    @GetMapping("/{exportId}")
    @ApiOperation(value = "通用导出接口", notes = "根据导出ID和传入的参数导出数据 目前有work-registration-application代表音视频登记申请导出(用户客户侧)")
    public void export(
            @ApiParam(value = "导出ID", required = true)
            @PathVariable("exportId") String exportId,
            @ApiParam(value = "导出格式，EXCEL或CSV，默认EXCEL")
            @RequestParam(value = "format", defaultValue = "EXCEL") String format,
            @ApiParam(value = "所有查询参数")
            @RequestParam Map<String, Object> requestParams,
            HttpServletResponse response) {
        try {
            // 检查导出定义是否存在
            if (!exportRegistry.hasExportDefinition(exportId)) {
                throw new BusinessException("导出定义不存在: " + exportId);
            }
            // 执行导出
            universalExportService.export(exportId, requestParams, format, response);
        } catch (Exception e) {
            log.error("导出失败: {}", exportId, e);
            throw new BusinessException("导出定义不存在: " + e.getMessage());
        }
    }
}

