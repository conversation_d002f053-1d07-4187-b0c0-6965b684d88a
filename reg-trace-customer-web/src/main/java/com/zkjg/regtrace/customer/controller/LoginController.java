package com.zkjg.regtrace.customer.controller;

import com.zkjg.regtrace.common.Result;
import com.zkjg.regtrace.manager.SysUserManager;
import com.zkjg.regtrace.persistence.vo.request.login.LoginRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/10 15:03
 */
@Api(tags = "登录")
@RestController
@RequestMapping("/user")
public class LoginController {

    @Resource
    private SysUserManager sysUserManager;

    @PostMapping("login")
    @ApiOperation(value = "登录", httpMethod = "POST")
    public Result<String> login(@RequestBody @Validated LoginRequest req) {
        return Result.ofSuccess(sysUserManager.login(req));
    }
}
