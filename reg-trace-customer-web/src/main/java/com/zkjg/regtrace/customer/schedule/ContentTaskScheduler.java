package com.zkjg.regtrace.customer.schedule;

import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.utils.QueryConvertUtils;
import com.zkjg.regtrace.persistence.dto.jpa.QueryWorkRegistrationJpaSpec;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import com.zkjg.regtrace.service.WorkRegistrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ContentTaskScheduler {

    private final WorkRegistrationService workRegistrationService;

    /**
     * 每天凌晨 0 点检查执行内容管理回收站清理任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void executeScheduledTasks() {
        log.debug("开始执行定时内容管理回收站清理任务...");
        QueryWorkRegistrationJpaSpec jpaSpec = new QueryWorkRegistrationJpaSpec();
        LocalDateTime today = LocalDateTime.now();
        LocalDateTime thirtyDaysAgo = today.minusDays(30);
        jpaSpec.setRecycleUpdateTimeLt(thirtyDaysAgo);
        jpaSpec.setRecycleEq(1);
        Specification<WorkRegistrationDO> specification = QueryConvertUtils.toSpecification(jpaSpec);
        List<WorkRegistrationDO> all = workRegistrationService.findAll(specification);
        for (WorkRegistrationDO registrationDO : all) {
            registrationDO.setDeleted(SqlConstant.DELETED);
        }
        workRegistrationService.updateBatch(all);
        log.debug("定时内容管理回收站清理任务执行完成。");
    }

}
