<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zkjg</groupId>
        <artifactId>reg-trace</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>reg-trace-customer-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zkjg</groupId>
            <artifactId>reg-trace-core</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <finalName>reg-trace-customer</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.zkjg.regtrace.customer.RegTraceCustomerWebApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>