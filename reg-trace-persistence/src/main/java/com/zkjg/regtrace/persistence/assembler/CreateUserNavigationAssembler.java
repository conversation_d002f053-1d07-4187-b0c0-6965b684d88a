package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.enums.FileTypeEnum;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.DataLineVo;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.DataTableVo;


public class CreateUserNavigationAssembler {
    public static DataTableVo buildDataTableVo(FileTypeEnum fileType, String dateStr, long count, int accumulateNum, int sequentialGrowthRate) {
        return DataTableVo.builder()
                .dataTime(dateStr)
                .contentType(fileType.getDesc())
                .increaseNum(count)
                .accumulateNum(accumulateNum)
                .sequentialGrowthRate(sequentialGrowthRate)
                .build();
    }


    public static DataTableVo buildEmptyDataTableVo(FileTypeEnum fileType, String dateStr) {
        return DataTableVo.builder()
                .dataTime(dateStr)
                .contentType(fileType.getDesc())
                .increaseNum(0L)
                .accumulateNum(0)
                .sequentialGrowthRate(0)
                .build();
    }

    public static DataLineVo buildDataLineVo(String dateStr, long count, Integer contentType) {
        return DataLineVo.builder()
                .contentType(contentType)
                .dataTime(dateStr)
                .increaseNum(count)
                .build();
    }


    public static DataLineVo buildEmptyDataLineVo(String dateStr, Integer contentTyp) {
        return DataLineVo.builder()
                .contentType(contentTyp)
                .dataTime(dateStr)
                .increaseNum(0L)
                .build();
    }
}
