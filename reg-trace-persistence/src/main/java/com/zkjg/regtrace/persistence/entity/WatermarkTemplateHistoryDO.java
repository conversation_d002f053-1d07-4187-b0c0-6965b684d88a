package com.zkjg.regtrace.persistence.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 水印模板变更记录实体
 */
@Entity
@Table(name = "watermark_template_history")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WatermarkTemplateHistoryDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "template_no", nullable = false)
    private String templateNo;

    @Column(name = "template_name", nullable = false, length = 255)
    private String templateName;

    @Column(name = "operation_type", nullable = false)
    private Integer operationType;

    @Column(name = "operator_id", nullable = false)
    private Integer operatorId;

    // 修改TEXT类型为CLOB
    @Column(name = "operation_content", columnDefinition = "CLOB")
    private String operationContent;

    // 修改TEXT类型为CLOB
    @Column(name = "template_snapshot", columnDefinition = "CLOB")
    private String templateSnapshot;

    // 修改TINYINT为NUMBER(1)
    @Column(name = "rolled_back", nullable = false)
    @Type(type = "org.hibernate.type.NumericBooleanType")
    private Boolean rolledBack = false;

    // 修改DATETIME默认值的定义
    @Column(name = "operation_time", nullable = false, updatable = false)
    @CreationTimestamp
    private LocalDateTime operationTime;

    @Column(name = "create_time", nullable = false, updatable = false)
    @CreationTimestamp
    private LocalDateTime createTime;

    @PrePersist
    protected void onCreate() {
        if (this.operationTime == null) {
            this.operationTime = LocalDateTime.now();
        }
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        if (this.rolledBack == null) {
            this.rolledBack = false;
        }
    }
}