package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 客服工单实体
 * <AUTHOR>
 */
@Entity
@Table(name = "ticket")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TicketDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 工单编号
     */
    @Column(unique = true, nullable = false)
    private String ticketNo;

    /**
     * 标题
     */
    private String title;

    /**
     * 问题分类：详见问题管理
     */
    private String category;

    /**
     * 紧急程度：1-低，2-中，3-高，4-紧急
     */
    private Integer urgency;

    /**
     * 问题描述
     */
    private String description;

    /**
     * 提交人ID
     */
    private Integer creatorId;

    /**
     * 分配的客服ID
     */
    private Integer assignedToId;

    /**
     * 状态：0-待分配，1-待客服确认，2-处理中，3-待用户确认，4-已关闭
     */
    private Integer status;

    /**
     * 创建时间
     */
    @Comment("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Comment("更新时间")
    private LocalDateTime updateTime;

    /**
     * 关闭时间
     */
    private LocalDateTime closeTime;

    /**
     * 是否删除，0标识否，1标识是
     */
    @Comment("是否删除，0标识否，1标识是")
    private Integer deleted;
}
