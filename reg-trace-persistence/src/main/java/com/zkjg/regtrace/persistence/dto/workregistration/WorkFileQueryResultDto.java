package com.zkjg.regtrace.persistence.dto.workregistration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登记文件查询结果 DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("登记文件查询结果")
public class WorkFileQueryResultDto {

    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件大小（单位：字节）")
    private Long fileSize;

    @ApiModelProperty("原始文件哈希")
    private String originalWorkHash;

    @ApiModelProperty("带水印文件哈希")
    private String watermarkedWorkHash;

    @ApiModelProperty("上传时间")
    private LocalDateTime createTime;

    @ApiModelProperty("登记状态")
    private Integer auditStatus;
}
