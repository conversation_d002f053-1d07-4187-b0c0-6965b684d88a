package com.zkjg.regtrace.persistence.vo.request.problem;

import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 问题管理查询请求
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("问题管理查询请求")
public class ProblemManagementQueryRequest extends BaasQuery {

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @ApiModelProperty("问题类型")
    private String problemTypeEq;

    @ApiModelProperty("问题内容")
    @Size(max = 255, message = "问题内容过长")
    private String problemContentLike;

    @ApiModelProperty("问题标签")
    @Size(max = 255, message = "问题标签过长")
    private String problemLabelLike;

    @ApiModelProperty("更新时间开始 格式yyyy-MM-dd")
    private String modifyTimeStart;

    @ApiModelProperty("更新时间结束 格式yyyy-MM-dd")
    private String modifyTimeEnd;

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
