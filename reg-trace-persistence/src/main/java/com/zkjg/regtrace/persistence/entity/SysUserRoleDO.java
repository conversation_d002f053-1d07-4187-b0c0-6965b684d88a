package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_user_role")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysUserRoleDO {

   @Id
   @GeneratedValue(strategy = GenerationType.IDENTITY)
   private Integer id;
   /**
    * 用户ID
    */
   private Integer userId;
   /**
    * 角色ID
    */
   private Integer roleId;
   /**
    * 创建时间
    */
   @Column(name = "create_time", updatable = false, columnDefinition = "datetime COMMENT '创建时间'")
   private LocalDateTime createTime;
   /**
    * 创建人
    */
   private String creator;

   public SysUserRoleDO(Integer id, Integer userId, Integer roleId) {
      this.id = id;
      this.userId = userId;
      this.roleId = roleId;
   }
}
