package com.zkjg.regtrace.persistence.vo.response.ticket;

import com.zkjg.regtrace.common.annotation.FullFilePath;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 附件响应VO
 * <AUTHOR>
 */
@Data
@ApiModel("附件响应")
public class AttachmentVO {

    @ApiModelProperty("附件ID")
    private Long id;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件路径")
    @FullFilePath
    private String filePath;

    @ApiModelProperty("上传时间")
    private LocalDateTime uploadTime;
}
