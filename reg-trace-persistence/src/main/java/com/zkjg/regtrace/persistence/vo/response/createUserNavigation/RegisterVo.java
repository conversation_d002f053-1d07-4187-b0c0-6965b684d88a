package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登记详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterVo {

    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("作品名称")
    private String workName;

    @ApiModelProperty("创作者")
    private String createUser;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("原始文件hash")
    private String originalWorkHash;

    @ApiModelProperty("加水印后文件哈希")
    private String watermarkedWorkHash;

    @ApiModelProperty(value = "溯源时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
