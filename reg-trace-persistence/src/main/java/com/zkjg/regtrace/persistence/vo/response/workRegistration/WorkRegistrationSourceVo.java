package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 作品登记来源信息展示 VO
 */
@Data
@ApiModel("作品登记来源信息展示")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkRegistrationSourceVo {

    @JsonIgnore
    private Integer rowIndex;

    // 文件信息部分
    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("原始文件哈希")
    private String originalFileHash;

    @ApiModelProperty("加水印后文件哈希")
    private String watermarkedFileHash;

    @ApiModelProperty("上传时间")
    private LocalDateTime uploadTime;

    // 作品信息部分
    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("作品名称")
    private String workName;

    @ApiModelProperty("作品类型")
    private Integer workType;

    @ApiModelProperty("创作者")
    private String author;

    @ApiModelProperty("发布地区")
    private String publishLocation;

    @ApiModelProperty("作品描述")
    private String description;

    @ApiModelProperty("登记时间")
    private LocalDateTime registrationTime;

    @ApiModelProperty("登记状态")
    private Integer registrationStatus;

    public String getAuthor() {
        if (author == null || author.trim().isEmpty()) {
            return "";
        }
        String trimmed = author.trim();
        int len = trimmed.length();
        if (len == 1) {
            return trimmed + "***";
        } else if (len == 2) {
            return trimmed.charAt(0) + "***" + trimmed.charAt(1);
        } else {
            return trimmed.charAt(0) + "***" + trimmed.charAt(len - 1);
        }
    }

    public WorkRegistrationSourceVo(
            Integer rowIndex,
            String fileName,
            Long fileSize,
            String originalFileHash,
            String watermarkedFileHash,
            java.sql.Timestamp uploadTime,
            String registrationNumber,
            String workName,
            Integer workType,
            String author,
            String publishLocation,
            String description,
            java.sql.Timestamp registrationTime,
            Integer registrationStatus
    ) {
        this.rowIndex = rowIndex;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.originalFileHash = originalFileHash;
        this.watermarkedFileHash = watermarkedFileHash;
        this.uploadTime = uploadTime == null ? null : uploadTime.toLocalDateTime();
        this.registrationNumber = registrationNumber;
        this.workName = workName;
        this.workType = workType;
        this.author = author;
        this.publishLocation = publishLocation;
        this.description = description;
        this.registrationTime = registrationTime == null ? null : registrationTime.toLocalDateTime();
        this.registrationStatus = registrationStatus;
    }

}
