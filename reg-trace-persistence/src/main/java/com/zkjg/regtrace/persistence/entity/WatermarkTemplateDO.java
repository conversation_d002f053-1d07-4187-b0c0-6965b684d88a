package com.zkjg.regtrace.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "watermark_template")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatermarkTemplateDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 模板所属用户id
     */
    private Integer userId;
    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板编号（唯一标识）
     */
    private String templateNo;

    /**
     * 水印文件类型：0=音频，1=视频，2=图片
     */
    private Integer watermarkFileType;

    private String fontStyle;

    private String colorSpace;

    private String location;

    private BigDecimal offsetX;

    private String offsetXUnit;

    private Integer watermarkId;

    private BigDecimal offsetY;

    private String offsetYUnit;
    /**
     * 旋转角度（度数）
     */
    private BigDecimal rotation;
    /**
     * 透明度（0.0-1.0）
     */
    private BigDecimal opacity;
    /**
     * 水印显示的宽度
     */
    private Integer width;


    /**
     * 开始时间戳（毫秒）
     */
    private String startTime;

    /**
     * 循环次数
     */
    private Long loopCount;

    /**
     * 单次持续时间（毫秒）
     */
    private String duration;

    /**
     * 循环间隔时间（毫秒）
     */
    private String loopInterval;

    @Comment("创建时间")
    @CreationTimestamp
    private LocalDateTime createTime;

    @Comment("创建人账号")
    @Column(name = "creator", length = 64)
    private String creator;

    @Comment("更新时间")
    @UpdateTimestamp
    private LocalDateTime modifyTime;

    @Comment("更新人账号")
    @Column(name = "modifier", length = 64)
    private String modifier;

    @Comment("是否删除，0标识否，1标识是")
    @Column(name = "deleted", nullable = false)
    private Integer deleted;
}
