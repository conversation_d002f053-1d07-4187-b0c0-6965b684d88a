package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "work_registration_application")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkRegistrationApplicationDO implements Serializable {

   private static final long serialVersionUID = 1L;

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;

   /**
    * 作品名称
    */
   @Column(name = "work_name")
   private String workName;

   /**
    * 作品描述
    */
   @Column(name = "description")
   private String description;

   /**
    * 请求IP
    */
   private String ip;

   /**
    * 请求IP所属地理位置  例:中国安徽合肥
    */
   private String location;

   /**
   * 登记编号，格式如：WR2024001001
   */
   @Column(name = "registration_number")
   private String registrationNumber;
   /**
   * 申请人ID
   */
   @Column(name = "applicant_user_id")
   private Integer applicantUserId;
   /**
   * 申请原因
   */
   @Column(name = "application_reason")
   private String applicationReason;
   /**
   * 0-草稿,1-待审核(审核中),2-审核通过,3-审核驳回
   */
   @Column(name = "audit_status")
   private Integer auditStatus;
   /**
   * 审核意见
   */
   @Column(name = "audit_comment")
   private String auditComment;
   /**
   * 审核人ID
   */
   @Column(name = "reviewed_user_id")
   private Integer reviewedUserId;
   /**
   * 申请人姓名
   */
   @Column(name = "applicant_name")
   private String applicantName;
   /**
   * 申请人邮箱
   */
   @Column(name = "applicant_email")
   private String applicantEmail;
   /**
   * 登记结果文件地址
   */
   @Column(name = "registration_result_file_url")
   private String registrationResultFileUrl;

   @Comment("创建时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;

   @Comment("更新时间")
   @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
   private LocalDateTime modifyTime;

   @Comment("审核时间")
   @Column(name = "audit_time")
   private LocalDateTime auditTime;

   @Comment("删除时间")
   @Column(name = "deleted_time")
   private LocalDateTime deletedTime;

   /**
    * 删除人ID
    */
   @Column(name = "deleted_user_id")
   private Integer deletedUserId;

   /**
   * 是否删除，0否，1是
   */
   private Integer deleted;
}
