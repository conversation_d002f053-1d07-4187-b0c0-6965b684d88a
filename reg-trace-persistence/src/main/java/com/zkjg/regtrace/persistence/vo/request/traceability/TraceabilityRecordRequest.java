package com.zkjg.regtrace.persistence.vo.request.traceability;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel("溯源记录请求")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TraceabilityRecordRequest{

    @ApiModelProperty(value = "开始时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty("当前页码")
    @NotNull(message = "当前页码不能为空")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @ApiModelProperty("每页记录数")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数必须大于等于1")
    private Integer size;

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty("溯源类型")
    private String traceabilityType;

    @ApiModelProperty("溯源结果")
    private Integer traceabilityStatus;

    @ApiModelProperty("来源登记编号")
    private String registrationNumber;

    @ApiModelProperty("来源作品类型")
    private Integer fileType;

    @ApiModelProperty("来源地区")
    private String location;

    @JsonIgnore
    private Integer userId;
}
