package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "追踪统计", description = "追踪统计")
public class TraceStatisticsVo {

    @ApiModelProperty(value = "日期")
    private String dataTime;

    @ApiModelProperty(value = "成功数量")
    private Long successNum;

    @ApiModelProperty(value = "失败数量")
    private Long failNum;

}
