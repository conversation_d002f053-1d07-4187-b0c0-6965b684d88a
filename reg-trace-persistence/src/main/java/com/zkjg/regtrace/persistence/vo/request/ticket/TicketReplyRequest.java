package com.zkjg.regtrace.persistence.vo.request.ticket;

import com.zkjg.regtrace.persistence.vo.common.AttachmentInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 工单回复请求
 * <AUTHOR>
 */
@Data
@ApiModel("工单回复请求")
public class TicketReplyRequest {

    @ApiModelProperty(value = "回复内容", required = true)
    @NotBlank(message = "回复内容不能为空")
    @Size(max = 500, message = "回复内容不能超过500个字符")
    private String content;

    @ApiModelProperty("附件列表")
    @Size(max = 5, message = "附件最多不能超过5个")
    private List<AttachmentInfo> attachments;
}
