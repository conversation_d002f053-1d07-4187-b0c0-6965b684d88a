package com.zkjg.regtrace.persistence.vo.request.register;

import com.zkjg.regtrace.common.constants.RegexConstant;
import com.zkjg.regtrace.common.enums.RegisterPermissionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @description 注册
 * @create 2025/6/11 16:09
 */
@Data
public class RegisterRequest {

    @ApiModelProperty(value = "用户名")
    @Pattern(regexp = RegexConstant.USERNAME_REGEX, message = "请输入有效的用户名")
    @NotBlank(message = "用户名不能为空")
    private String username;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "请输入有效的邮箱地址")
    private String email;

    @ApiModelProperty(value = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;

    @ApiModelProperty(value = "权限 REGISTER-登记 TRACE-溯源")
    @Size(min = 1, message = "初始化权限不能为空")
    @NotNull(message = "初始化权限不能为空")
    private List<RegisterPermissionEnum> permission;

    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = RegexConstant.PHONE_REGEX, message = "请输入有效的11位手机号码")
    private String phone;

    @ApiModelProperty(value = "0 系统用户, 1 个人用户，2 企业用户，3 政府用户")
    private Integer userType;

    @ApiModelProperty(value = "机构名称不能为空")
    private String orgName;

    @ApiModelProperty(value = "统一社会信用代码不能为空")
    private String creditCode;

    @ApiModelProperty(value = "企业地址、办公地址")
    private String address;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "法人姓名（企业用户）")
    private String legalName;

    @ApiModelProperty(value = "法人身份证号（企业用户）")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String legalId;

    @ApiModelProperty(value = "经办人姓名（企业用户）")
    private String agentName;

    @ApiModelProperty(value = "经办人身份证号（企业用户）")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String agentId;

    @ApiModelProperty(value = "经办人联系电话（企业用户）")
    @Pattern(regexp = RegexConstant.PHONE_REGEX, message = "请输入有效的11位手机号码")
    private String agentPhone;
}
