package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DocumentJpaSpec extends BaasQuery {

    private List<Integer> idIn;

    private String nameLike;

    private Integer typeEq;

    private Integer isPublicEq;

    private Integer deletedEq;

    private String nameLikeOrDescriptionLike;
}
