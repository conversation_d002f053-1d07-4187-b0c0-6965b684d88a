package com.zkjg.regtrace.persistence.vo.response.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 16:14
 */
@Data
public class PermissionDetailVo {

    @ApiModelProperty("菜单Id")
    private Integer permissionId;

    @ApiModelProperty("父菜单Id")
    private Integer parentId;

    @ApiModelProperty("菜单名称")
    private String permissionName;

    @ApiModelProperty("类型")
    private Integer permissionType;

    @ApiModelProperty("是否拥有菜单")
    private boolean hasPermission;

    public PermissionDetailVo(Integer permissionId, Integer parentId, String permissionName, Integer permissionType, boolean hasPermission) {
        this.permissionId = permissionId;
        this.parentId = parentId;
        this.permissionName = permissionName;
        this.permissionType = permissionType;
        this.hasPermission = hasPermission;
    }
}
