package com.zkjg.regtrace.persistence.vo.response.problem;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问题管理响应VO
 * <AUTHOR>
 */
@Data
@ApiModel("问题管理响应VO")
public class ProblemManagementVO {

    @ApiModelProperty("问题ID")
    private Long id;

    @ApiModelProperty("问题类型")
    private String problemType;

    @ApiModelProperty("问题内容")
    private String problemContent;

    @ApiModelProperty("问题标签")
    private String problemLabel;

    @ApiModelProperty("问题答案")
    private String answer;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;
}
