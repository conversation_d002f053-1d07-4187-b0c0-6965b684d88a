package com.zkjg.regtrace.persistence.dto.jpa;
import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 10:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkRegistrationApplicationJpaSpec extends BaasQuery {
    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @ApiModelProperty(value = "审核状态 0-草稿 1-待审核 2-审核通过 3-审核驳回")
    private Integer auditStatusEq;

    @ApiModelProperty(value = "申请人ID")
    private Integer applicantUserIdEq;

    @ApiModelProperty(value = "登记编号")
    private String registrationNumberLike;

    @ApiModelProperty(value = "申请开始时间")
    private LocalDateTime createTimeGtEq;

    @ApiModelProperty(value = "申请结束时间")
    private LocalDateTime createTimeLtEq;

    @ApiModelProperty(value = "登记开始时间")
    private LocalDateTime auditTimeGtEq;

    @ApiModelProperty(value = "登记结束时间")
    private LocalDateTime auditTimeLtEq;

    @ApiModelProperty(value = "申请人姓名")
    private String applicantNameLike;

    @ApiModelProperty(value = "作品名称")
    private String workNameLike;

    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Integer deletedEq;

    @ApiModelProperty(value = "申请ID")
    private List<Integer> idIn;

}
