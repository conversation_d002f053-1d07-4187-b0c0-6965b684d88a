package com.zkjg.regtrace.persistence.vo.response.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 菜单管理查询-返参
 * @create 2024/6/4 9:55
 */
@Data
public class PermissionListVo {

    @ApiModelProperty("菜单Id")
    private Integer id;

    @ApiModelProperty("父菜单Id")
    private Integer parentId;

    @ApiModelProperty("菜单名称")
    private String permissionName;

    @ApiModelProperty("类型")
    private String permissionType;

    @ApiModelProperty("排序")
    private Integer sortNumber;

    @ApiModelProperty("请求地址")
    private String routeUrl;

    @ApiModelProperty("权限标识")
    private String interfaceIdentity;

    @ApiModelProperty("icon")
    private String icon;

    @ApiModelProperty("菜单类型 0-通用菜单 1-链相关")
    private Integer type;

    @ApiModelProperty("是否可以被角色添加 0-不可以 1-可以")
    private Integer canAdd;
}
