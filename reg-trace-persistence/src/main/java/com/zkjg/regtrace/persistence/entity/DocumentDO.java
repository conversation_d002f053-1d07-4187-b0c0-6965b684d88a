package com.zkjg.regtrace.persistence.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-07-16 11:13
 */
@Entity
@Table(name = "DOCUMENT")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 文档名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否公开
     */
    private Integer isPublic;

    /**
     * 浏览量
     */
    private Long views;

    /**
     * 下载量
     */
    private Long downloads;

    @Comment("创建时间")
    @CreationTimestamp
    private LocalDateTime createTime;

    @Comment("创建人账号")
    private String creator;

    @Comment("更新时间")
    @UpdateTimestamp
    private LocalDateTime modifyTime;

    @Comment("更新人账号")
    private String modifier;

    @Comment("是否删除，0标识否，1标识是")
    @Column(name = "DELETED", nullable = false)
    private Integer deleted = 0;
}
