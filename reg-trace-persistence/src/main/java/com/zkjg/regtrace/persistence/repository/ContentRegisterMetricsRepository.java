package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.ContentRegisterMetricsDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-06-18 08:59
 */
@Repository
public interface ContentRegisterMetricsRepository extends JpaRepository<ContentRegisterMetricsDO, Integer>, JpaSpecificationExecutor<ContentRegisterMetricsDO> {

    @Query(value = "SELECT TO_CHAR(t.create_time, 'MM') AS time_slot, " +
            "ROUND(COUNT(DISTINCT t.application_user_id) * 100.0 / " +
            "NULLIF((SELECT COUNT(*) FROM sys_user u WHERE u.deleted = 0 AND u.user_type IN (1,2,3) " +
            "AND TO_CHAR(u.create_time, 'YYYY-MM') <= TO_CHAR(t.create_time, 'YYYY-MM')), 0), 2) AS ratio " +
            "FROM work_registration_application t " +
            "WHERE TO_CHAR(t.create_time, 'YYYY') = TO_CHAR(SYSDATE, 'YYYY') " +
            "GROUP BY TO_CHAR(t.create_time, 'MM') " +
            "ORDER BY time_slot ASC", nativeQuery = true)
    List<Map<String, Object>> findYearlyActiveUserRatio();

    /**
     * 查询指定日期的每小时活跃用户比例
     */
    @Query(value = "SELECT TO_CHAR(operate_time, 'HH24') as time_slot, " +
            "ROUND(COUNT(DISTINCT user_id) * 100.0 / NULLIF((SELECT COUNT(*) FROM sys_user WHERE deleted = 0), 0), 2) as ratio " +
            "FROM content_register_metrics WHERE TO_CHAR(operate_time, 'YYYYMMDD') = ?1 " +
            "GROUP BY TO_CHAR(operate_time, 'HH24') " +
            "ORDER BY time_slot", nativeQuery = true)
    List<Map<String, Object>> findDailyActiveUserRatioForDate(String date);

    /**
     * 查询指定月份的每天活跃用户比例
     */
    @Query(value = "SELECT TO_CHAR(operate_time, 'DD') as time_slot, " +
            "ROUND(COUNT(DISTINCT user_id) * 100.0 / NULLIF((SELECT COUNT(*) FROM sys_user WHERE deleted = 0), 0), 2) as ratio " +
            "FROM content_register_metrics WHERE TO_CHAR(operate_time, 'YYYYMM') = ?1 " +
            "GROUP BY TO_CHAR(operate_time, 'DD') " +
            "ORDER BY time_slot", nativeQuery = true)
    List<Map<String, Object>> findMonthlyActiveUserRatioForMonth(String yearMonth);

    /**
     * 查询指定年份的每月活跃用户比例
     */
    @Query(value = "SELECT TO_CHAR(operate_time, 'MM') as time_slot, " +
            "ROUND(COUNT(DISTINCT user_id) * 100.0 / NULLIF((SELECT COUNT(*) FROM sys_user WHERE deleted = 0), 0), 2) as ratio " +
            "FROM content_register_metrics WHERE TO_CHAR(operate_time, 'YYYY') = ?1 " +
            "GROUP BY TO_CHAR(operate_time, 'MM') " +
            "ORDER BY time_slot", nativeQuery = true)
    List<Map<String, Object>> findYearlyActiveUserRatioForYear(String year);

    /**
     * 查询指定日期的每小时内容登记数量
     */
    @Query(value = "SELECT TO_CHAR(create_time, 'HH24') AS hour, COUNT(*) AS count " +
            "FROM t_content_register_metrics WHERE (:fileType IS NULL OR file_type = :fileType) " +
            "AND TO_CHAR(create_time, 'YYYYMMDD') = :statTime " +
            "GROUP BY TO_CHAR(create_time, 'HH24') " +
            "ORDER BY hour ASC", nativeQuery = true)
    List<Map<String, Object>> findDailyStatsForDate(@Param("fileType") Integer fileType, @Param("statTime") String statTime);

    /**
     * 查询指定月份的每天内容登记数量
     */
    @Query(value = "SELECT TO_CHAR(create_time, 'DD') AS day, COUNT(*) AS count " +
            "FROM t_content_register_metrics WHERE (:fileType IS NULL OR file_type = :fileType) " +
            "AND TO_CHAR(create_time, 'YYYYMM') = :statTime " +
            "GROUP BY TO_CHAR(create_time, 'DD') " +
            "ORDER BY day ASC", nativeQuery = true)
    List<Map<String, Object>> findMonthlyStatsForMonth(@Param("fileType") Integer fileType, @Param("statTime") String statTime);
}
