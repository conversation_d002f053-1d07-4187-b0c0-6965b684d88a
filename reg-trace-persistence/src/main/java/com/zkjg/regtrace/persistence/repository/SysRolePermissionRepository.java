package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.SysRolePermissionDO;
import com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysRolePermissionRepository extends JpaRepository<SysRolePermissionDO, Integer>, JpaSpecificationExecutor<SysRolePermissionDO> {

    @Query("select new com.zkjg.regtrace.persistence.vo.response.permission.PermissionDetailVo" +
            "(p.id,p.parentId,p.permissionName,p.permissionType,case when rp.id is not null then true else false end) from SysPermissionDO p " +
            "left join SysRolePermissionDO rp on p.id = rp.permissionId and rp.roleId= :roleId " +
            "where p.deleted =:deleted")
    List<PermissionDetailVo> selectRolePermissionListByRoleId(Integer roleId, Integer deleted);

    void deleteByRoleId(Integer roleId);

    void deleteAllByRoleIdIn(List<Integer> roleIds);
}
