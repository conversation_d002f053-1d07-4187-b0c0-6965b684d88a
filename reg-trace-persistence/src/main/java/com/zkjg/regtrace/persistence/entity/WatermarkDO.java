package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "watermark")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatermarkDO {

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;
   /**
   * 用户ID
   */
   private Integer userId;


    /**
     * 水印编号（唯一标识）
     */
    private String watermarkNo;
   /**
   * 水印名称
   */
   private String name;
   /**
   * 水印文件原始名称
   */
   private String fileName;

    /**
     * 水印文字内容（fileType = 3）
     */
   private String textContent;
   /**
   * 水印文件存储路径（相对路径）
   */
   private String filePath;
   /**
   * 水印类型：0=音频，1=视频，2=图片, 3=文字
   */
   private Integer fileType;
   /**
   * 文件格式，例如：mp3, mp4, png 等
   */
   private String fileFormat;
   /**
   * 文件哈希值
   */
   private String fileHash;

    private String fontStyle;

    private String fontSize;

   @Comment("创建时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;
   /**
   * 逻辑删除标志：0=否，1=是
   */
   private Integer deleted;

   /**
    * 是否共享：0=否，1=是
    */
   private Integer isShared = 0;

   @Column(name = "click_count")
   private Integer clickCount = 0;
}
