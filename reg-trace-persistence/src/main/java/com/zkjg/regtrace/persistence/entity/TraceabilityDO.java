package com.zkjg.regtrace.persistence.entity;

import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "traceability")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TraceabilityDO {

   @Id
   @GeneratedValue(strategy = GenerationType.IDENTITY)
   private Integer id;

   @Column(name = "registration_number")
   private String registrationNumber;

   /** 溯源文件名称 */
   @Column(name = "file_name")
   private String fileName;

   /** 文件格式 */
   @Column(name = "file_format")
   private String fileFormat;

   /** 原始文件Hash */
   @Column(name = "original_work_hash")
   private String originalWorkHash;

   /** 水印Hash */
   @Column(name = "watermark_hash")
   private String watermarkHash;

   /** 添加水印的文件Hash */
   @Column(name = "watermarked_work_hash")
   private String watermarkedWorkHash;

   /** 溯源状态 */
   @Column(name = "status")
   private Integer status;

   /** 备注 */
   @Column(name = "remark")
   private String remark;

   /** 时间 */
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;

   /** 创建者ID */
   @Column(name = "creator_id")
   private Integer creatorId;

   /** 文件类型 */
   @Column(name = "file_type")
   private Integer fileType;

   /**
    * 溯源编号
    */
   @Column(name = "traceability_number")
   private String traceabilityNumber;

   /**
    * 条件查询溯源参数
    */
   @Column(name = "traceability_params")
   private String traceabilityParams;

   /**
    * 溯源方式
    */
   @Column(name = "traceability_type")
   private String traceabilityType;

   /**
    * 溯源来源类型
    */
   @Column(name = "source_type")
   private String sourceType;

   /** 创建人 */
   @Column(name = "creator", length = 64)
   private String creator;

   /** 修改时间 */
   @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
   private LocalDateTime modifyTime;

   /** 修改人 */
   @Column(name = "modifier", length = 64)
   private Integer modifier;

   /** 删除标识 */
   @Column(name = "deleted", nullable = false)
   private Integer deleted;

   /** 记录状态 */
   @Column(name = "record_status")
   private String recordStatus;
}
