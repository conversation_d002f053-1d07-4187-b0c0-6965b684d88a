package com.zkjg.regtrace.persistence.vo.request.permission;

import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @description 菜单管理查询-入参
 * @create 2024/6/4 9:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PermissionListRequest extends BaasQuery {

    @ApiModelProperty("菜单名称")
    @Size(max = 255, message = "菜单名称过长")
    private String permissionNameLike;

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
