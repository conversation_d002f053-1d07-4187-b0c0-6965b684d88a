package com.zkjg.regtrace.persistence.dto.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.Column;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 10:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkRegistrationJpaSpec extends BaasQuery {

    @ApiModelProperty(value = "登记编号")
    private String registrationNumberEq;

    @ApiModelProperty(value = "申请id")
    private List<Integer> applicationIdIn;

    @ApiModelProperty(value = "申请人ID")
    private Integer applicantUserIdEq;

    @ApiModelProperty(value = "回收站")
    private Integer recycleEq;

    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Integer deletedEq;

    @ApiModelProperty(value = "回收站更新时间")
    private LocalDateTime recycleUpdateTimeLt;
}
