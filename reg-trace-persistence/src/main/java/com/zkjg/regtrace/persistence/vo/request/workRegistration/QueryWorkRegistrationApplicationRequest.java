package com.zkjg.regtrace.persistence.vo.request.workRegistration;

import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 作品登记申请列表查询请求参数
 * <AUTHOR>
 * @Date 2025/6/11 18:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkRegistrationApplicationRequest extends BaasQuery {

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @ApiModelProperty(value = "审核状态 0-草稿 1-待审核 2-审核通过 3-审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "申请人ID 无需传递")
    private Integer applicantUserId;

    @ApiModelProperty(value="登记编号")
    private String registrationNumber;

    @ApiModelProperty(value = "申请开始时间 格式都为2022-02-12 12:12:12")
    private String startCreateTime;

    @ApiModelProperty(value = "申请结束时间")
    private String endCreateTime;

    @ApiModelProperty(value = "登记开始时间")
    private String startAuditTime;

    @ApiModelProperty(value = "登记结束时间")
    private String endAuditTime;

    @ApiModelProperty(value = "作品名称")
    private String workName;

    @ApiModelProperty(value = "申请人姓名")
    private String applicantName;

    @ApiModelProperty(value = "文件类型 0=音频，1=视频,2-图片")
    private Integer fileType;

    @ApiModelProperty(value = "是否删除 0=否 1=是")
    private Integer deleted;

}
