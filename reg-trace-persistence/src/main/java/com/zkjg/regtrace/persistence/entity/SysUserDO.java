package com.zkjg.regtrace.persistence.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_user")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysUserDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 部门id
     */
    private Integer departmentId;
    /**
     * 昵称
     */
    private String username;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 加盐后的密码
     */
    @JSONField(serialize = false)
    private String password;
    /**
     * 用户类型（0 系统用户, 1 个人用户，2 企业用户，3 政府用户）
     */
    private Integer userType;
    /**
     * 用户状态（0停用 1正常）
     */
    private Integer userStatus;
    /**
     * 备注
     */
    private String remark;

    /**
     * 姓名
     */
    private String personName;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * 身份证前后面地址
     */
    private String idCardBack;
    private String idCardFront;
    /**
     * 站内通知方式 以逗号隔开
     */
    private String noticeType;
    /**
     * 头像地址
     */
    private String headImg;

    @Comment("创建时间")
    @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    @Comment("创建人账号")
    @Column(name = "creator", length = 64)
    private String creator;

    @Comment("更新时间")
    @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
    private LocalDateTime modifyTime;

    @Comment("更新人账号")
    @Column(name = "modifier", length = 64)
    private String modifier;

    @Comment("是否删除，0标识否，1标识是")
    @Column(name = "deleted", nullable = false)
    private Integer deleted;

    @Transient
    private SysUserInfoDO userInfo;
}
