package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "折线图", description = "折线图")
public class DataLineVo {

    @ApiModelProperty(value = "折线图中填写：文件类型 0=音频，1=视频，2=图片")
    private Integer contentType;

    @ApiModelProperty(value = "日期")
    private String dataTime;

    @ApiModelProperty(value = "新增数量")
    private Long increaseNum;
}
