package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.persistence.dto.jpa.QueryMessageNotifyJpaSpec;
import com.zkjg.regtrace.persistence.entity.MessageNotifyDO;
import com.zkjg.regtrace.persistence.vo.request.message.MessageNotifyRequest;
import com.zkjg.regtrace.persistence.vo.response.message.MessageNotifyVo;

public class MessageNotifyAssembler {

    public static MessageNotifyVo toVo(MessageNotifyDO entity) {
        if (entity == null) {
            return null;
        }

        return MessageNotifyVo.builder()
                .id(entity.getId())
                .isRead(entity.getIsRead())
                .content(entity.getContent())
                .category(entity.getCategory())
                .remark(entity.getRemark())
                .createTime(entity.getCreateTime())
                .build();
    }

    public static QueryMessageNotifyJpaSpec toJpaSpec(MessageNotifyRequest req) {
        return QueryMessageNotifyJpaSpec.builder()
                .categoryEq(req.getCategory())
                .createTimeGtEq(req.getStartTime())
                .createTimeLtEq(req.getEndTime())
                .keywordLike(req.getKeyword())
                .receiverIdEq(req.getUserId())
                .isReadEq(req.getIsRead())
                .idIn(req.getIds())
                .deletedEq(SqlConstant.UN_DELETED)
                .build();
    }
}
