package com.zkjg.regtrace.persistence.vo.request.permission;

import com.zkjg.regtrace.common.constants.RegexConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 15:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAddRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 16, message = "请输入有效的用户名,长度为2到16个字符")
    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @Pattern(regexp = RegexConstant.PHONE_REGEX, message = "请输入有效的11位手机号码")
    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("绑定邮箱")
    @Email(message = "请输入有效的邮箱地址")
    @Size(max = 50, message = "请输入有效的邮箱地址")
    private String email;

    @NotBlank(message = "登录密码不能为空")
    @ApiModelProperty(value = "登录密码", required = true)
    private String password;

    @ApiModelProperty(value = "用户角色")
    @NotNull(message = "角色不能为空")
    private List<Integer> roleList;

    @Size(max = 250, message = "备注超长,不能超过250字符")
    @ApiModelProperty("备注")
    private String remark;
}
