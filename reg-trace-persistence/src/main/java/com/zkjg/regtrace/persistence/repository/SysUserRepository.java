package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.SysUserDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SysUserRepository extends JpaRepository<SysUserDO, Integer>, JpaSpecificationExecutor<SysUserDO> {

    SysUserDO findByUsernameAndDeleted(String username, Integer deleted);

    SysUserDO findByEmailAndDeleted(String email, Integer deleted);

    @Query(value = "select u from SysUserRoleDO ur left join SysUserDO u on ur.userId = u.id where ur.roleId = :roleId and u.deleted = :deleted ")
    List<SysUserDO> findUserByRoleId(@Param("roleId") Integer roleId, @Param("deleted") Integer deleted);

    @Query(value = "select u from SysUserRoleDO ur left join SysUserDO u on ur.userId = u.id where ur.roleId = :roleId " +
            "and (u.deleted = :deleted ) " +
            "and (:username is null or u.username like %:username%) ")
    Page<SysUserDO> findUserByRoleId(@Param("roleId") Integer roleId, @Param("username" ) String username, @Param("deleted") Integer deleted, PageRequest pageRequest);

    @Query("select u from SysUserDO u where u.id not in (select ur.userId from SysUserRoleDO ur where ur.roleId =:roleId) " +
            "and (u.deleted = :deleted ) " +
            "and (:username is null or u.username like %:username%) ")
    Page<SysUserDO> findUserWithoutRoleId(@Param("roleId") Integer roleId, @Param("username") String username, @Param("deleted") Integer deleted, PageRequest pageRequest);

    @Query(value = "SELECT cast(DATE_FORMAT(create_time, '%H') as signed) AS hour, COUNT(*) AS count " +
            "FROM sys_user WHERE user_type IN (1,2,3) AND deleted = 0 " +
            "AND DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d') " +
            "GROUP BY DATE_FORMAT(create_time, '%H') " +
            "ORDER BY hour ASC", nativeQuery = true)
    List<Map<String, Object>> findDailyNewUserStats();

    @Query(value = "SELECT cast(DATE_FORMAT(create_time, '%d') as signed) AS day, COUNT(*) AS count " +
            "FROM sys_user WHERE user_type IN (1,2,3) AND deleted = 0 " +
            "AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') " +
            "GROUP BY DATE_FORMAT(create_time, '%d') " +
            "ORDER BY day ASC", nativeQuery = true)
    List<Map<String, Object>> findMonthlyNewUserStats();

    @Query(value = "SELECT cast(DATE_FORMAT(create_time, '%m') as signed) AS month, COUNT(*) AS count " +
            "FROM sys_user WHERE user_type IN (1,2,3) AND deleted = 0 " +
            "AND DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y') " +
            "GROUP BY DATE_FORMAT(create_time, '%m') " +
            "ORDER BY month ASC", nativeQuery = true)
    List<Map<String, Object>> findYearlyNewUserStats();

    /**
     * 查询指定日期的每小时新增用户数
     */
    @Query(value = "SELECT cast(DATE_FORMAT(create_time, '%H') as signed) as hour, COUNT(*) as count " +
            "FROM sys_user WHERE user_type IN (1,2,3) AND deleted = 0 " +
            "AND DATE_FORMAT(create_time, '%Y%m%d') = ?1 " +
            "GROUP BY DATE_FORMAT(create_time, '%H') " +
            "ORDER BY hour ASC", nativeQuery = true)
    List<Map<String, Object>> findDailyNewUserStatsForDate(String date);

    /**
     * 查询指定月份的每天新增用户数
     */
    @Query(value = "SELECT cast(DATE_FORMAT(create_time, '%d') as signed) as day, COUNT(*) as count " +
            "FROM sys_user WHERE user_type IN (1,2,3) AND deleted = 0 " +
            "AND DATE_FORMAT(create_time, '%Y%m') = ?1 " +
            "GROUP BY DATE_FORMAT(create_time, '%d') " +
            "ORDER BY day ASC", nativeQuery = true)
    List<Map<String, Object>> findMonthlyNewUserStatsForMonth(String yearMonth);

    /**
     * 查询指定年份的每月新增用户数
     */
    @Query(value = "SELECT cast(DATE_FORMAT(create_time, '%m') as signed) as month, COUNT(*) as count " +
            "FROM sys_user WHERE user_type IN (1,2,3) AND deleted = 0 " +
            "AND DATE_FORMAT(create_time, '%Y') = ?1 " +
            "GROUP BY DATE_FORMAT(create_time, '%m') " +
            "ORDER BY month ASC", nativeQuery = true)
    List<Map<String, Object>> findYearlyNewUserStatsForYear(String year);

    /**
     * 根据角色名称查询用户列表
     */
    @Query("SELECT u FROM SysUserDO u " +
            "INNER JOIN SysUserRoleDO ur ON u.id = ur.userId " +
            "INNER JOIN SysRoleDO r ON ur.roleId = r.id " +
            "WHERE r.roleName = :roleName AND u.deleted = :deleted")
    List<SysUserDO> findUsersByRoleName(@Param("roleName") String roleName, @Param("deleted") Integer deleted);

    /**
     * 根据姓名模糊查询用户列表
     */
    List<SysUserDO> findByUsernameContainingAndDeleted(String userName, Integer deleted);
}
