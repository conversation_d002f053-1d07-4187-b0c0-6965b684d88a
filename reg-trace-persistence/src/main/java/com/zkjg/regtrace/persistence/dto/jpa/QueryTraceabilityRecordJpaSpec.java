package com.zkjg.regtrace.persistence.dto.jpa;
import com.zkjg.regtrace.common.BaasQuery;
import lombok.*;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryTraceabilityRecordJpaSpec extends BaasQuery {

    private Integer page;

    private Integer size;

    private String createTimeGtEq;

    private String createTimeLtEq;

    private String fileNameLike;

    private Integer deletedEq;

    private String registrationNumberLike;
}
