package com.zkjg.regtrace.persistence.vo.response.traceability;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "溯源记录", description = "溯源记录")
public class TraceabilityVo {

    @ApiModelProperty(value = "溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty(value = "来源登记编号")
    private String registrationNumber;

    @ApiModelProperty(value = "溯源时间")
    private LocalDateTime traceabilityTime;

    @ApiModelProperty(value = "溯源方式")
    private Integer traceabilityType;

    @ApiModelProperty(value = "溯源状态")
    private Integer traceabilityStatus;

    @ApiModelProperty(value = "来源作品类型 0=音频，1=视频，2=图片")
    private Integer fileType;

    @ApiModelProperty(value = "来源地区")
    private String location;
}
