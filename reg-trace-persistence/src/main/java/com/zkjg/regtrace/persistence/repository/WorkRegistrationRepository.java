package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkRegistrationRepository extends JpaRepository<WorkRegistrationDO, Integer>, JpaSpecificationExecutor<WorkRegistrationDO> {

    /**
     * 根据作品名称精确查询且按删除状态过滤
     * @param workName 作品名称
     * @param deleted 是否删除
     * @return 作品登记信息列表
     */
    List<WorkRegistrationDO> findByWorkNameAndDeleted(String workName, Integer deleted);

    WorkRegistrationDO findByIdAndDeleted(Integer id, Integer deleted);
}
