package com.zkjg.regtrace.persistence.vo.request.document;

import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("帮助中心查询请求")
public class HelpCenterQueryRequest extends BaasQuery {

    @ApiModelProperty("条件")
    private String condition;

    @ApiModelProperty("类型:0-操作指南 1-政策文件")
    private Integer type;

    @ApiModelProperty("问题类型")
    private String problemType;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型: DESC, ASC")
    @EnumIntercept(clazz = SortTypeEnum.class, message = "排序类型不合法")
    private String orderType = "DESC";

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "createTime";
}
