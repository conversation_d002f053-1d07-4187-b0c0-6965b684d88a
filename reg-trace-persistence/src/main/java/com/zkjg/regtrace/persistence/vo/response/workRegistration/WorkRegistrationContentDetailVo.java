package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/26 16:41
 */
@Data
public class WorkRegistrationContentDetailVo implements Serializable {

    @ApiModelProperty(value = "作品名称")
    @AdvancedExportUtil.ExportField("作品名称")
    private String workName;

    @ApiModelProperty(value = "登记编号", example = "WR2024001001")
    @AdvancedExportUtil.ExportField("登记编号")
    private String registrationNumber;

    @ApiModelProperty(value = "作品类型：0=音频，1=视频, 2=图片")
    @AdvancedExportUtil.ExportField("作品类型")
    private Integer fileType;

    @ApiModelProperty(value = "作品描述")
    @AdvancedExportUtil.ExportField("作品描述")
    private String description;

    @ApiModelProperty(value = "登记时间")
    @AdvancedExportUtil.ExportField("登记时间")
    private LocalDateTime registrationTime;

    // 嵌套对象字段的导出支持
    @ApiModelProperty(value = "文件名称")
    @AdvancedExportUtil.ExportField("文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件大小")
    @AdvancedExportUtil.ExportField("文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "原始文件哈希")
    @AdvancedExportUtil.ExportField("原始文件哈希")
    private String originalWorkHash;

    @ApiModelProperty(value = "加水印后文件哈希")
    @AdvancedExportUtil.ExportField("加水印后文件哈希")
    private String watermarkedWorkHash;

    @ApiModelProperty(value = "水印哈希")
    @AdvancedExportUtil.ExportField("水印哈希")
    private String watermarkHash;

    @ApiModelProperty(value = "上传时间")
    @AdvancedExportUtil.ExportField("上传时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "申请人", example = "张三")
    @AdvancedExportUtil.ExportField("申请人")
    private String applicantName;

    @ApiModelProperty(value = "申请人联系方式", example = "<EMAIL>")
    @AdvancedExportUtil.ExportField("申请人联系方式")
    private String applicantEmail;

    @ApiModelProperty(value = "申请时间")
    @AdvancedExportUtil.ExportField("申请时间")
    private LocalDateTime applicationTime;

    @ApiModelProperty(value = "申请原因", example = "原创音乐作品版权保护")
    @AdvancedExportUtil.ExportField("申请原因")
    private String applicationReason;

    @ApiModelProperty(value = "状态:2-成功,3-失败")
    @AdvancedExportUtil.ExportField("状态")
    private Integer status;

    @ApiModelProperty(value = "审核流程")
    private List<WorkRegistrationApplicationRecordVo> record;
}
