package com.zkjg.regtrace.persistence.vo.response.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 角色管理查询用户-返参
 * @create 2024/6/4 9:55
 */
@Data
public class RoleQueryUserListVo {

    @ApiModelProperty("用户Id")
    private Integer id;

    @ApiModelProperty("用户姓名")
    private String username;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("用户状态")
    private Integer userStatus;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
