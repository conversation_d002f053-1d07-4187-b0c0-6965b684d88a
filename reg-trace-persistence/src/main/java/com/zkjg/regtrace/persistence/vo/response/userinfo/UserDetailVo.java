package com.zkjg.regtrace.persistence.vo.response.userinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/11 16:41
 */
@Data
@Builder
public class UserDetailVo {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty("姓名")
    private String personName;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("身份证人像面")
    private String idCardBack;

    @ApiModelProperty("身份证国徽面")
    private String idCardFront;

    @ApiModelProperty("通知方式")
    private String noticeType;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty(value = "权限 REGISTER-登记 TRACE-溯源,用于客户端")
    private List<String> permission;

    @ApiModelProperty("0 系统用户, 1 个人用户，2 企业用户，3 政府用户")
    private Integer userType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "企业、政府信息")
    private UserInfoDetailVo userInfo;

    @ApiModelProperty("图片通用前缀")
    private String imageUrl;
}
