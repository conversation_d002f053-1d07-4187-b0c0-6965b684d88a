package com.zkjg.regtrace.persistence.vo.response.traceability;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "异常统计", description = "可追溯异常统计数据")
public class TraceabilityExceptionStatisticsVo {

    @ApiModelProperty(value = "登记编号")
    private String registrationNumber;

    @ApiModelProperty(value = "源文件哈希值")
    private String original_work_hash;

    @ApiModelProperty(value = "异常频率，异常数 / 总记录数")
    private Float exceptionFrequency;

    @ApiModelProperty(value = "异常类型统计列表")
    private List<ExceptionTypeCount> exceptionTypeCounts;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "ExceptionTypeCount", description = "异常类型及对应数量")
    public static class ExceptionTypeCount {

        @ApiModelProperty(value = "异常类型")
        private String exceptionType;

        @ApiModelProperty(value = "该类型的异常数量")
        private Integer count;
    }
}
