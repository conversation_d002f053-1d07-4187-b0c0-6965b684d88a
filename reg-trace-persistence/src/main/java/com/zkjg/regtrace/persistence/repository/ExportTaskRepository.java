package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import com.zkjg.regtrace.persistence.entity.ExportTaskDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 导出任务 Repository
 */
@Repository
public interface ExportTaskRepository extends JpaRepository<ExportTaskDO, Integer> {

    ExportTaskDO findFirstByCreatorId(Integer creatorId);

    List<ExportTaskDO> findAllByScheduleTypeIsNot(ExportTaskScheduleTypeEnum scheduleTypeEnum);
}
