package com.zkjg.regtrace.persistence.vo.request.watermark;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 水印收藏请求VO
 */
@Data
@ApiModel(description = "水印收藏请求参数")
public class WatermarkFavoriteRequest {
    @ApiModelProperty(value = "水印ID", required = true)
    @NotNull(message = "水印ID不能为空")
    private Integer watermarkId;
    
    @ApiModelProperty(value = "收藏备注")
    private String remark;
}
