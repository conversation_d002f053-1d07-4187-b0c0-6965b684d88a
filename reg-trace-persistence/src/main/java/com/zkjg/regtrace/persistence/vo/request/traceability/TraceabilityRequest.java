package com.zkjg.regtrace.persistence.vo.request.traceability;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("新增溯源请求")
public class TraceabilityRequest {

    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("溯源文件名称")
    private String fileName;

    @ApiModelProperty("溯源文件格式")
    private String fileFormat;

    @ApiModelProperty("溯源文件类型")
    private Integer fileType;

    @ApiModelProperty("溯源文件原始Hash")
    private String originalWorkHash;

    @ApiModelProperty("溯源文件水印Hash")
    private String watermarkHash;

    @ApiModelProperty("溯源文件添加水印后的文件哈希")
    private String watermarkedWorkHash;

    @ApiModelProperty("秘钥")
    private String key;

    @ApiModelProperty("备注")
    private String remark;

    private Integer registerId;

    @JsonIgnore
    private String traceabilityNumber;

    @JsonIgnore
    private String creator;
}
