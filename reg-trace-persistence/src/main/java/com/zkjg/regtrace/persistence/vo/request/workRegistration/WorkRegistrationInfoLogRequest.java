package com.zkjg.regtrace.persistence.vo.request.workRegistration;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 作品登记信息错误日志记录请求参数
 * <AUTHOR>
 * @Date 2025/6/12 17:03
 */
@Data
public class WorkRegistrationInfoLogRequest {

    @ApiModelProperty(value = "异常种类")
    @NotEmpty(message = "异常种类不能为空")
    private String exceptionType;

    @ApiModelProperty(value = "登记编号")
    @NotEmpty(message = "登记编号不能为空")
    private String registrationNumber;

    @ApiModelProperty(value = "错误信息")
    @NotEmpty(message = "错误信息不能为空")
    private String errorMessage;

}
