package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationRecordDO;
import com.zkjg.regtrace.persistence.jpa.WorkRegistrationJpaSpec;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.DigitalWatermarkRegistrationVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.InvisibleWatermarkRegistrationVo;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/13 17:18
 */
public class WorkRegistrationRecordAssembler {
    public static WorkRegistrationRecordDO convertApplicationToDO(WorkRegistrationApplicationDO application) {
        return WorkRegistrationRecordDO.builder()
                .applicationId(application.getId())
                .registrationNumber(application.getRegistrationNumber())
                .applicantUserId(application.getApplicantUserId())
                .reviewedUserId(application.getReviewedUserId())
                .auditStatus(application.getAuditStatus())
                .auditComment(application.getAuditComment())
                .workName(application.getWorkName())
                .deleted(SqlConstant.UN_DELETED)
                .createTime(application.getModifyTime())
                .build();
    }

    public static WorkRegistrationJpaSpec convertToJpaSpec(WorkRegistrationApplicationDO workRegistrationApplicationDO) {
        return WorkRegistrationJpaSpec.builder()
                .idEq(workRegistrationApplicationDO.getId())
                .registrationNumberEq(workRegistrationApplicationDO.getRegistrationNumber())
                .auditStatusEq(workRegistrationApplicationDO.getAuditStatus())
                .deletedEq(SqlConstant.UN_DELETED)
                .build();
    }


    public static InvisibleWatermarkRegistrationVo convertToInvisibleWaterRegistrationVo(WorkRegistrationApplicationDO applicationDO, WorkRegistrationApplicationFileDO fileDO) {
        if (Objects.isNull(applicationDO) || Objects.isNull(fileDO)) {
            return null;
        }

        return InvisibleWatermarkRegistrationVo.builder()
                .registrationNumber(applicationDO.getRegistrationNumber())
                .originalWorkHash(fileDO.getOriginalWorkHash())
                .waterMarkLocation("")
                .creator("") // TODO: 填充 creator 逻辑
                .deviceNumber("") // TODO: 填充 deviceNumber 逻辑
                .createTime(fileDO.getCreateTime())
                .build();
    }

    public static DigitalWatermarkRegistrationVo convertToDigitalWaterRegistrationVo(WorkRegistrationApplicationDO workRegistrationApplicationDO, WorkRegistrationApplicationFileDO fileDO) {
        if (Objects.isNull(workRegistrationApplicationDO) || Objects.isNull(fileDO)) {
            return null;
        }

        return DigitalWatermarkRegistrationVo.builder()
                .registrationNumber(workRegistrationApplicationDO.getRegistrationNumber())
                .originalWorkHash(fileDO.getOriginalWorkHash())
                .waterMarkLocation("")
                .creator("") // TODO: 填充 creator 逻辑
                .deviceNumber("") // TODO: 填充 deviceNumber 逻辑
                .createTime(fileDO.getCreateTime())
                .transPath("创作者编号" + ">>>" + "溯源者编号")
                .build();
    }
}
