package com.zkjg.regtrace.persistence.vo.response.exception;


import com.zkjg.regtrace.persistence.dto.exception.ExceptionDistributionDto;
import com.zkjg.regtrace.persistence.dto.exception.ExceptionFrequencyDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("异常统计")
public class ExceptionStatisticsVo {

    @ApiModelProperty("异常分布")
    private List<ExceptionDistributionDto> exceptionFrequency;

    @ApiModelProperty("异常发生频率")
    private List<ExceptionFrequencyDto> exception;
}
