package com.zkjg.regtrace.persistence.dto.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.*;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageQueryExceptionLogJpaSpec extends BaasQuery {

    private LocalDateTime createTimeGt;

    private LocalDateTime createTimeLt;

    private Integer page;

    private Integer size;

    private String operatorLike;

    private String exceptionTypeEq;

    private String errorMessageLike;
}
