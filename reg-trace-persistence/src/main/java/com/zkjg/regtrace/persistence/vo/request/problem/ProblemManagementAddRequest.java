package com.zkjg.regtrace.persistence.vo.request.problem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 问题管理添加请求
 * <AUTHOR>
 */
@Data
@ApiModel("问题管理添加请求")
public class ProblemManagementAddRequest {

    @NotNull(message = "问题类型不能为空")
    @ApiModelProperty(value = "问题类型", required = true)
    private String problemType;

    @NotBlank(message = "问题内容不能为空")
    @Size(max = 1000, message = "问题内容长度不能超过1000字符")
    @ApiModelProperty(value = "问题内容", required = true)
    private String problemContent;

    @Size(max = 200, message = "问题标签长度不能超过200字符")
    @ApiModelProperty(value = "问题标签", required = true)
    private String problemLabel;

    @ApiModelProperty(value = "问题答案", required = true)
    @NotBlank(message = "问题答案不能为空")
    private String answer;
}
