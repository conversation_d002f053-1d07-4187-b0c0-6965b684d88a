package com.zkjg.regtrace.persistence.vo.request.userinfo;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.config.LocalDateTimeDeserializer;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/16 9:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserHistoryInfoListRequest extends BaasQuery {
    @ApiModelProperty(value = "用户id", hidden = true)
    private Integer userIdEq;

    @ApiModelProperty(value = "变革类型 0、CREATE, 2、UPDATE")
    private Integer changeTypeEq;

    @ApiModelProperty("开始时间yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTimeGt;

    @ApiModelProperty("结束时间yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTimeLt;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型")
    @EnumIntercept(clazz = SortTypeEnum.class, message = "排序类型不合法")
    private String orderType;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "createTime";

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
