package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProblemJpaSpec extends BaasQuery {

    private String problemTypeEq;

    private Integer deletedEq;

    private String problemContentLikeOrProblemLabelLikeOrAnswerLike;
}
