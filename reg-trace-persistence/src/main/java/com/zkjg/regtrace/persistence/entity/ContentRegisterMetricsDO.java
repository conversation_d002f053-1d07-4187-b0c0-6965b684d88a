package com.zkjg.regtrace.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-06-18 08:41
 */
@Entity
@Table(name = "content_register_metrics")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContentRegisterMetricsDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 作品登记ID
     */
    private Integer applicationId;

    /**
     * 作品登记用户ID
     */
    private Integer applicantUserId;

    /**
     * 文件类型：0=音频，1=视频
     */
    private Integer fileType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
