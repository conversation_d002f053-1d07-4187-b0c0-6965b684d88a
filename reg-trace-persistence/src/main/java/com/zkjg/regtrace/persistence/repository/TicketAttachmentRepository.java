package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.TicketAttachmentDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工单附件Repository
 * <AUTHOR>
 */
@Repository
public interface TicketAttachmentRepository extends JpaRepository<TicketAttachmentDO, Long> {

    /**
     * 查询交流记录的所有附件
     */
    List<TicketAttachmentDO> findByCommunicationId(Long communicationId);
}
