package com.zkjg.regtrace.persistence.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-07-22 08:36
 */
@Entity
@Table(name = "problem_management")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProblemManagementDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String problemType;

    private String problemContent;

    private String problemLabel;

    private String answer;

    @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
    private LocalDateTime modifyTime;

    private Integer deleted;

}
