package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WatermarkTemplateHistoryDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 水印模板历史记录Repository
 *
 * <AUTHOR>
 * @date 2025/6/16
 */
public interface WatermarkTemplateHistoryRepository extends JpaRepository<WatermarkTemplateHistoryDO, Long>,
        JpaSpecificationExecutor<WatermarkTemplateHistoryDO> {
}
