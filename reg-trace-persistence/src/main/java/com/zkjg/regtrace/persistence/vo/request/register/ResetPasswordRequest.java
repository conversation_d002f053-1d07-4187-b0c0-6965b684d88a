package com.zkjg.regtrace.persistence.vo.request.register;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 10:38
 */
@Data
public class ResetPasswordRequest {

    @NotBlank(message = "邮箱不能为空")
    @ApiModelProperty(value = "邮箱")
    private String email;

    @NotBlank(message = "token不能为空")
    @ApiModelProperty(value = "token")
    private String token;

    @NotBlank(message = "密码不能为空")
    @ApiModelProperty("密码")
    protected String password;
}
