package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_role")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysRoleDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 0：禁用，1：正常
     */
    private Integer roleStatus;

    /**
     * 角色来源 0-系统角色 1-预置角色
     */
    private Integer source;

    /**
     * 角色类型 0-通用角色 1-链通用角色
     */
    private Integer type;

    /**
     * 是否可以删除，0：不可以,1:可以
     */
    private Integer canDelete;

    /**
     * 平台，1、web 2、客户端
     */
    private Integer platform;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    @Column(insertable = false)
    private Integer version;
    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false, columnDefinition = "datetime COMMENT '创建时间'")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 更新时间
     */
    @Column(name = "modify_time", insertable = false, columnDefinition = "datetime COMMENT '更新时间'")
    private LocalDateTime modifyTime;
    /**
     * 更新人
     */
    private String modifier;
    /**
     * 是否删除，0标识否，1标识是
     */
    private Integer deleted;

    public SysRoleDO(Integer id, String roleName) {
        this.id = id;
        this.roleName = roleName;
    }
}
