package com.zkjg.regtrace.persistence.vo.request.traceability;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@ApiModel("新增溯源请求")
@AllArgsConstructor
@NoArgsConstructor
public class ConditionalTraceabilityRequest {

    @JsonIgnore
    private Integer rowIndex;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("哈希")
    private String hash;
}
