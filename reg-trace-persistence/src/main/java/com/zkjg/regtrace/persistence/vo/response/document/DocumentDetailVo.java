package com.zkjg.regtrace.persistence.vo.response.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 15:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "文档管理", description = "文档管理")
public class DocumentDetailVo {

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "文档名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "文档文件")
    private String file;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否公开")
    private Integer isPublic;

    @ApiModelProperty(value = "浏览量")
    private Long views;

    @ApiModelProperty(value = "下载量")
    private Long downloads;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "更新时间")
    List<DocumentFileVo> documentFiles;

    @Data
    public static class DocumentFileVo {

        @ApiModelProperty(value = "id")
        private Integer id;

        @ApiModelProperty(value = "文档文件")
        private String file;

        @Comment("创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
    }
}
