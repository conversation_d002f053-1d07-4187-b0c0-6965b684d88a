package com.zkjg.regtrace.persistence.vo.request.permission;

import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.constants.RegexConstant;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:54
 */
@Data
public class UserManageEditRequest {

    @ApiModelProperty(value = "用户Id", required = true)
    @NotNull(message = "用户Id不能为空")
    private Integer userId;

    @ApiModelProperty(value = "用户角色")
    @NotNull(message = "角色不能为空")
    private List<Integer> roleList;

    @ApiModelProperty(value = "0-禁用 1-启用")
    @EnumIntercept(clazz = StatusEnum.class, message = "用户状态不合法",method = "getCode")
    @NotNull(message = "用户状态不能为空")
    private Integer userStatus;

    @ApiModelProperty(value = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "请输入有效的邮箱地址")
    private String email;

    @ApiModelProperty("手机号")
    @Pattern(regexp = RegexConstant.PHONE_REGEX, message = "请输入有效的11位手机号码")
    private String phone;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "机构名称不能为空")
    private String orgName;

    @ApiModelProperty(value = "统一社会信用代码不能为空")
    private String creditCode;

    @ApiModelProperty(value = "企业地址、办公地址")
    private String address;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "法人姓名（企业用户）")
    private String legalName;

    @ApiModelProperty(value = "法人身份证号（企业用户）")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String legalId;

    @ApiModelProperty(value = "经办人姓名（企业用户）")
    private String agentName;

    @ApiModelProperty(value = "经办人身份证号（企业用户）")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String agentId;

    @ApiModelProperty(value = "经办人联系电话（企业用户）")
    @Pattern(regexp = RegexConstant.PHONE_REGEX, message = "请输入有效的11位手机号码")
    private String agentPhone;
}
