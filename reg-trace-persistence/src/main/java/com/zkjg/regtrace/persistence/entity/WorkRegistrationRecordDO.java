package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "work_registration_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkRegistrationRecordDO {

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;
   /**
   * 申请表主键id
   */
   @Column(name = "application_id")
   private Integer applicationId;

   /**
    * 作品名称
    */
   @Column(name = "work_name")
   private String workName;
   /**
   * 登记编号，格式如：WR2024001001
   */
   @Column(name = "registration_number")
   private String registrationNumber;
   /**
   * 申请人ID
   */
   @Column(name = "applicant_user_id")
   private Integer applicantUserId;
   /**
   * 审核人ID
   */
   @Column(name = "reviewed_user_id")
   private Integer reviewedUserId;
   /**
   * 0-草稿,1-待审核(审核中),2-审核通过,3-审核驳回
   */
   @Column(name = "audit_status")
   private Integer auditStatus;
   /**
   * 审核意见
   */
   @Column(name = "audit_comment")
   private String auditComment;

   @Comment("创建时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;
   /**
   * 是否删除，0否，1是
   */
   private Integer deleted;
}
