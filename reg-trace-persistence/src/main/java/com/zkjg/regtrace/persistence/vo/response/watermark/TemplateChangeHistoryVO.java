package com.zkjg.regtrace.persistence.vo.response.watermark;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模板变更记录VO
 */
@Data
@ApiModel(description = "模板变更记录信息")
public class TemplateChangeHistoryVO {
    @ApiModelProperty(value = "记录ID")
    private Integer id;
    
    @ApiModelProperty(value = "模板ID")
    private String templateNo;

    private Integer templateId;
    
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    
    @ApiModelProperty(value = "操作类型： 1=更新，2=删除,3=回滚")
    private Integer operationType;
    
    @ApiModelProperty(value = "操作类型描述")
    private String operationTypeDesc;
    
    @ApiModelProperty(value = "操作人")
    private String operator;
    
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty(value = "是否已回滚")
    private Boolean rolledBack;
    
    @ApiModelProperty(value = "操作内容（JSON格式的变更详情）")
    private String operationContent;
    
    /**
     * 获取操作类型描述
     */
    public String getOperationTypeDesc() {
        if (operationType == null) {
            return "";
        }
        switch (operationType) {
            case 1: return "更新";
            case 2: return "删除";
            case 3: return "回滚";
            default: return "未知操作";
        }
    }
}
