package com.zkjg.regtrace.persistence.vo.response.traceability;

import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationApplicationVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "溯源详情", description = "溯源详情")
public class TraceabilityDetailVo {

    @ApiModelProperty(value = "溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty(value = "溯源用户")
    private String traceabilityUser;

    @ApiModelProperty(value = "记录状态")
    private String recordStatus;

    @ApiModelProperty(value = "溯源时间")
    private LocalDateTime traceabilityTime;

    @ApiModelProperty(value = "溯源状态")
    private Integer traceabilityStatus;

    @ApiModelProperty("溯源类型")
    private String traceabilityType;

    @ApiModelProperty("溯源条件-json")
    private String traceabilityParams;

    @ApiModelProperty("来源方式")
    private String sourceType;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("原始文件哈希")
    private String originalWorkHash;

    @ApiModelProperty("加水印后的文件哈希")
    private String waterMarkedWorkHash;

    @ApiModelProperty("来源信息")
    private WorkRegistrationSourceVo sourceWorkInfo;
}
