package com.zkjg.regtrace.persistence.vo.response.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 用户拥有菜单
 * @create 2024/6/26 17:45
 */
@Data
@Builder
public class UserMenuListVo {

    @ApiModelProperty("菜单Id")
    private Integer id;

    @ApiModelProperty("父菜单Id")
    private Integer parentId;

    @ApiModelProperty("菜单名称")
    private String permissionName;

    @ApiModelProperty("类型")
    private Integer permissionType;

    @ApiModelProperty("排序")
    private Integer sortNumber;

    @ApiModelProperty("路由地址")
    private String routeUrl;

    @ApiModelProperty("icon")
    private String icon;

}
