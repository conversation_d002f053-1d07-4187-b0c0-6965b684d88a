package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_permission")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysPermissionDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

   /**
    * 平台 pc customer
    */
   private String platform;
    /**
     * 权限名称
     */
    private String permissionName;
    /**
     * 父权限id
     */
    private Integer parentId;
    /**
     * 排序
     */
    private Integer sortNumber;
    /**
     * 路由地址
     */
    private String routeUrl;
    /**
     * 权限类型（1模块 2菜单 3按钮）
     */
    private Integer permissionType;
    /**
     * 后端接口权限标识
     */
    private String interfaceIdentity;
    /**
     * 菜单图标
     */
    private String icon;

    @Comment("创建时间")
    @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    @Comment("创建人账号")
    @Column(name = "creator", length = 64)
    private String creator;

    @Comment("更新时间")
    @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
    private LocalDateTime modifyTime;

    @Comment("更新人账号")
    @Column(name = "modifier", length = 64)
    private String modifier;

    @Comment("是否删除，0标识否，1标识是")
    @Column(name = "deleted", nullable = false)
    private Integer deleted;

    public SysPermissionDO(Integer id, String permissionName, Integer parentId, Integer sortNumber, String routeUrl, Integer permissionType, String icon) {
        this.id = id;
        this.permissionName = permissionName;
        this.parentId = parentId;
        this.sortNumber = sortNumber;
        this.routeUrl = routeUrl;
        this.permissionType = permissionType;
        this.icon = icon;
    }
}
