package com.zkjg.regtrace.persistence.dto.traceability;

import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 溯源记录查询结果 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TraceabilityRecordDto {

    @ApiModelProperty("溯源表主键ID")
    @UniversalExportUtil.ExportIgnore
    private Integer id;

    @ApiModelProperty("溯源编号")
    @UniversalExportUtil.ExportField("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty("溯源类型")
    @UniversalExportUtil.ExportField(value = "溯源状态", converter = "traceabilityTypeConverter")
    private String traceabilityType;

    @ApiModelProperty("溯源状态")
    @UniversalExportUtil.ExportField(value = "溯源状态", converter = "traceabilityStatusConverter" )
    private Integer status;

    @ApiModelProperty("来源登记编号")
    @UniversalExportUtil.ExportField("登记编号")
    private String registrationNumber;

    @ApiModelProperty("文件类型")
    @UniversalExportUtil.ExportField(value = "文件类型", converter = "fileTypeConverter")
    private Integer fileType;

    @ApiModelProperty("来源地区")
    @UniversalExportUtil.ExportField("来源地区")
    private String location;

    @ApiModelProperty(value = "溯源时间")
    @UniversalExportUtil.ExportField("溯源时间")
    private LocalDateTime createTime;
}
