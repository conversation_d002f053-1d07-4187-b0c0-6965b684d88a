package com.zkjg.regtrace.persistence.vo.request.userinfo;

import com.zkjg.regtrace.common.enums.RegisterPermissionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/13 10:53
 */
@Data
public class EditUserPermissionRequest {

    @ApiModelProperty(value = "权限 REGISTER-登记 TRACE-溯源")
    private List<RegisterPermissionEnum> permission;
}
