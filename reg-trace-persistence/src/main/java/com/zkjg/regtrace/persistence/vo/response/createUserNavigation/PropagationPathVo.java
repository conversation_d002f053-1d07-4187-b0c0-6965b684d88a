package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 传播路径
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PropagationPathVo {

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("用户")
    private String userName;

    @ApiModelProperty("设备指纹")
    private String deviceNumber;
}
