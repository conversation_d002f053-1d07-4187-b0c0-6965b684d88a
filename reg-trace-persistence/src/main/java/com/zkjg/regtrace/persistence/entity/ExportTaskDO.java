package com.zkjg.regtrace.persistence.entity;

import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 导出任务实体类
 * 对应表：export_task
 */
@Entity
@Table(name = "export_task")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportTaskDO {

    /**
     * 主键ID，自增
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 计划任务类型（NONE、WEEKLY、MONTHLY）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "schedule_type", nullable = false, length = 20)
    private ExportTaskScheduleTypeEnum scheduleType;

    /**
     * 创建人ID
     */
    @Column(name = "creator_id")
    private Integer creatorId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false, insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime updateTime;

    /**
     * 是否删除标识 0-未删除 1-已删除
     */
    @Column(name = "deleted", insertable = false)
    private Integer deleted;
}
