package com.zkjg.regtrace.persistence.dto.jpa;
import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 11:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkRegistrationApplicationFileJpaSpec extends BaasQuery {
    @ApiModelProperty(value = "文件类型 0=音频，1=视频")
    private Integer fileTypeEq;

    @ApiModelProperty(value = "申请表主键id集合")
    private List<Integer> applicationIdIn;

    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Integer deletedEq;


}
