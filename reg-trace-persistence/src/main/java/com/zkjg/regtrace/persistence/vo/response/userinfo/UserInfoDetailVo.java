package com.zkjg.regtrace.persistence.vo.response.userinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @description
 * @create 2025/6/11 16:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoDetailVo {

    @ApiModelProperty(value = "机构名称不能为空")
    private String orgName;

    @ApiModelProperty(value = "统一社会信用代码不能为空")
    private String creditCode;

    @ApiModelProperty(value = "企业地址、办公地址")
    private String address;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "法人姓名（企业用户）")
    private String legalName;

    @ApiModelProperty(value = "法人身份证号（企业用户）")
    private String legalId;

    @ApiModelProperty(value = "经办人姓名（企业用户）")
    private String agentName;

    @ApiModelProperty(value = "经办人身份证号（企业用户）")
    private String agentId;

    @ApiModelProperty(value = "经办人联系电话（企业用户）")
    private String agentPhone;
}
