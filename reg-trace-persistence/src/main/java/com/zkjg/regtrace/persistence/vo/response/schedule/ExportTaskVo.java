package com.zkjg.regtrace.persistence.vo.response.schedule;

import com.zkjg.regtrace.common.enums.ExportTaskScheduleTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 导出任务 VO
 */
@Data
@ApiModel("导出任务视图对象")
@Builder
@AllArgsConstructor
public class ExportTaskVo {

    @ApiModelProperty("任务ID")
    private Integer id;

    @ApiModelProperty("计划任务类型（NONE、WEEKLY、MONTHLY）")
    private ExportTaskScheduleTypeEnum scheduleType;
}
