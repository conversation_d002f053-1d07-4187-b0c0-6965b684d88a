package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.ProblemManagementDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 问题管理Repository
 * <AUTHOR>
 */
@Repository
public interface ProblemManagementRepository extends JpaRepository<ProblemManagementDO, Long>, JpaSpecificationExecutor<ProblemManagementDO> {
    
    /**
     * 根据ID和删除状态查询
     * @param id 问题ID
     * @param deleted 删除状态
     * @return 问题管理对象
     */
    Optional<ProblemManagementDO> findByIdAndDeleted(Long id, Integer deleted);
}
