package com.zkjg.regtrace.persistence.vo.request.ticket;

import com.zkjg.regtrace.persistence.vo.common.AttachmentInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 工单创建请求
 * <AUTHOR>
 */
@Data
@ApiModel("工单创建请求")
public class TicketCreateRequest {

    @ApiModelProperty(value = "标题", required = true)
    @NotBlank(message = "标题不能为空")
    @Size(max = 50, message = "标题不能超过50个字符")
    private String title;

    @ApiModelProperty(value = "问题分类", required = true)
    @NotNull(message = "问题分类不能为空")
    private String category;

    @ApiModelProperty(value = "紧急程度：1-低，2-中，3-高，4-紧急", required = true)
    @NotNull(message = "紧急程度不能为空")
    private Integer urgency;

    @ApiModelProperty(value = "问题描述", required = true)
    @NotBlank(message = "问题描述不能为空")
    @Size(max = 500, message = "问题描述不能超过500个字符")
    private String description;

    @ApiModelProperty("附件列表")
    @Size(max = 5, message = "附件最多不能超过5个")
    private List<AttachmentInfo> attachments;
}
