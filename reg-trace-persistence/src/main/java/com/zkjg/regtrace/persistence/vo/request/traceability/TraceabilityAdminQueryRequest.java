package com.zkjg.regtrace.persistence.vo.request.traceability;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel("溯源管理列表查询请求")
public class TraceabilityAdminQueryRequest {

    @ApiModelProperty(value = "开始时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty("当前页码")
    @NotNull(message = "当前页码不能为空")
    @Min(value = 0, message = "当前页码必须大于等于0")
    private Integer pageNum;

    @ApiModelProperty("每页记录数")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数必须大于等于1")
    private Integer pageSize;

    @ApiModelProperty("溯源结果")
    private Integer status;

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty("溯源方式")
    private String traceabilityType;

    @ApiModelProperty("记录状态")
    private String recordStatus;
}
