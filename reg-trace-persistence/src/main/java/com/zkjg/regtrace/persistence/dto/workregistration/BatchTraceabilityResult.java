package com.zkjg.regtrace.persistence.dto.workregistration;

import com.zkjg.regtrace.persistence.vo.request.traceability.ConditionalTraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchTraceabilityResult {
    private ConditionalTraceabilityRequest request;
    private WorkRegistrationSourceVo matchedRecord;
}
