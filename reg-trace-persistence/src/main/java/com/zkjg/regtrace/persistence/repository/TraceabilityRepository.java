package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto;
import com.zkjg.regtrace.persistence.entity.TraceabilityDO;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.TraceaRecordVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TraceabilityRepository extends JpaRepository<TraceabilityDO, Integer>, JpaSpecificationExecutor<TraceabilityDO> {

    List<TraceabilityDO> findAllByRegistrationNumberAndDeleted(String registrationNumber, Integer deleted);

    TraceabilityDO findDistinctByOriginalWorkHashOrWatermarkedWorkHash(String original_work_hash, String watermarked_work_hash);

    @Modifying
    @Transactional
    @Query("update TraceabilityDO t set t.recordStatus = :status where t.id in :ids")
    void updateRecordStatusByIds(@Param("ids") List<Integer> ids, @Param("status") String status);

    TraceabilityDO findByTraceabilityNumberAndDeleted(String traceabilityNumber, Integer deleted);


    @Query("SELECT new com.zkjg.regtrace.persistence.vo.response.createUserNavigation.TraceaRecordVo(" +
            "t.id, t.createTime, wra.registrationNumber, wraf.fileName, t.traceabilityNumber, t.traceabilityType) " +
            "FROM TraceabilityDO t " +
            "LEFT JOIN WorkRegistrationApplicationDO wra ON t.registrationNumber  = wra.registrationNumber " +
            "LEFT JOIN WorkRegistrationApplicationFileDO wraf ON wra.id = wraf.applicationId " +
            "WHERE wra.applicantUserId = :userId " +
            "AND (:registrationNumber IS NULL OR wra.registrationNumber = :registrationNumber) " +
            "AND (:fileName IS NULL OR wraf.fileName LIKE :fileName) " +
            "AND (:traceabilityNumber IS NULL OR t.traceabilityNumber = :traceabilityNumber) " +
            "AND (:traceabilityType IS NULL OR t.traceabilityType = :traceabilityType) " +
            "AND (:startTime IS NULL OR t.createTime >= :startTime) " +
            "AND (:endTime IS NULL OR t.createTime <= :endTime) " +
            "ORDER BY t.createTime DESC")
    Page<TraceaRecordVo> queryTraceRecords(Integer userId, String registrationNumber, String fileName, String traceabilityNumber, String traceabilityType, LocalDateTime startTime, LocalDateTime endTime, PageRequest pageRequest);

    @Query("SELECT new com.zkjg.regtrace.persistence.dto.traceability.TraceabilityRecordDto(" +
            "t.id, t.traceabilityNumber, t.traceabilityType, t.status, " +
            "t.registrationNumber, w.fileType, w.location, t.createTime) " +
            "FROM TraceabilityDO t " +
            "LEFT JOIN WorkRegistrationDO w ON t.registrationNumber = w.registrationNumber " +
            "WHERE (:userId IS NULL OR t.creatorId = :userId) " +
            "AND (:traceabilityNumber IS NULL OR t.traceabilityNumber LIKE %:traceabilityNumber%) " +
            "AND (:traceabilityType IS NULL OR t.traceabilityType = :traceabilityType) " +
            "AND (:traceabilityStatus IS NULL OR t.status = :traceabilityStatus) " +
            "AND (:registrationNumber IS NULL OR t.registrationNumber LIKE %:registrationNumber%) " +
            "AND (:fileType IS NULL OR w.fileType = :fileType) " +
            "AND (:location IS NULL OR w.location LIKE %:location%) " +
            "AND (:startTime IS NULL OR t.createTime >= :startTime) " +
            "AND (:endTime IS NULL OR t.createTime <= :endTime) " +
            "ORDER BY t.createTime DESC")
    Page<TraceabilityRecordDto> pageQueryTraceabilityRecord(
            @Param("userId") Integer userId,
            @Param("traceabilityNumber") String traceabilityNumber,
            @Param("traceabilityType") String traceabilityType,
            @Param("traceabilityStatus") Integer traceabilityStatus,
            @Param("registrationNumber") String registrationNumber,
            @Param("fileType") Integer fileType,
            @Param("location") String location,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable
    );

}
