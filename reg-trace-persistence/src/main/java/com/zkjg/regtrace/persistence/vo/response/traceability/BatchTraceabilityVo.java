package com.zkjg.regtrace.persistence.vo.response.traceability;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "批量溯源vo", description = "批量溯源vo")
public class BatchTraceabilityVo {

    @ApiModelProperty("总条数")
    private Integer total;

    @ApiModelProperty("错误行数列表")
    private List<Integer> errors;
}
