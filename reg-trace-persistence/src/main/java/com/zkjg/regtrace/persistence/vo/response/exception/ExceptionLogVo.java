package com.zkjg.regtrace.persistence.vo.response.exception;


import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "异常日志", description = "异常日志")
public class ExceptionLogVo {

    @ApiModelProperty("日志ID")
    @UniversalExportUtil.ExportIgnore
    private Integer id;

    @ApiModelProperty("操作账户")
    @UniversalExportUtil.ExportField(value = "操作账户")
    private String operator;

    @ApiModelProperty("异常类型")
    @UniversalExportUtil.ExportField(value = "异常类型", converter = "exceptionTypeConverter")
    private String exceptionType;

    @ApiModelProperty("错误信息")
    @UniversalExportUtil.ExportField(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty("操作时间")
    @UniversalExportUtil.ExportField(value = "操作时间")
    private LocalDateTime operationTime;
}
