package com.zkjg.regtrace.persistence.vo.response.watermark;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.persistence.entity.WatermarkDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 水印VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "水印信息")
public class WatermarkVO {
    @ApiModelProperty(value = "水印ID")
    private Integer id;
    
    @ApiModelProperty(value = "用户ID")
    private Integer userId;
    
    @ApiModelProperty(value = "水印名称")
    private String name;

    @ApiModelProperty(value = "水印编号（唯一标识）")
    private String watermarkNo;
    
    @ApiModelProperty(value = "水印文件原始名称")
    private String fileName;
    
    @ApiModelProperty(value = "水印文件访问URL")
    private String fileUrl;
    
    @ApiModelProperty(value = "水印文本内容")
    private String textContent;
    
    @ApiModelProperty(value = "水印类型：0=音频，1=视频，2=图片，3=文字")
    private Integer fileType;

    @ApiModelProperty(value = "水印文件哈希值")
    private String fileHash;
    
    @ApiModelProperty(value = "文件格式")
    private String fileFormat;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "是否已收藏")
    private Boolean isFavorite;
    
    /**
     * 是否共享：0=否，1=是
     */
    private Integer isShared;
    
    /**
     * 点击次数（不含作者自己的点击）
     */
    private Integer clickCount;

    private String fontStyle;

    private String fontSize;

    /**
     * 从DO转换为VO
     */
    public static WatermarkVO fromDO(WatermarkDO watermarkDO) {
        if (watermarkDO == null) {
            return null;
        }
        return BeanUtil.copyProperties(watermarkDO, WatermarkVO.class);
    }
}
