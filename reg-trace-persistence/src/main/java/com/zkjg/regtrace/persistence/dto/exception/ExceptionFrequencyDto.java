package com.zkjg.regtrace.persistence.dto.exception;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionFrequencyDto {

    @ApiModelProperty("时间")
    private LocalDateTime time;

    @ApiModelProperty("数量")
    private Long count;
}
