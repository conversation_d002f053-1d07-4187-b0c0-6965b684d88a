package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.dto.exception.ExceptionDistributionDto;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ExceptionLogRepository extends JpaRepository<ExceptionLogDO, Integer>,JpaSpecificationExecutor<ExceptionLogDO> {
    @Query("SELECT new com.zkjg.regtrace.persistence.dto.exception.ExceptionDistributionDto(e.exceptionType, 0.0f, COUNT(e) * 1.0f) " +
            "FROM ExceptionLogDO e " +
            "GROUP BY e.exceptionType")
    List<ExceptionDistributionDto> findExceptionTypeCounts();


    @Query(value = "SELECT DATE_FORMAT(operation_time, '%Y-%m-%d') AS time, COUNT(*) AS count " +
            "FROM exception_log " +
            "WHERE operation_time BETWEEN :start AND :end " +
            "GROUP BY DATE_FORMAT(operation_time, '%Y-%m-%d') " +
            "ORDER BY DATE_FORMAT(operation_time, '%Y-%m-%d')",
            nativeQuery = true)
    List<Object[]> countDailyFrequency(@Param("start") LocalDateTime start,
                                       @Param("end") LocalDateTime end);

}
