package com.zkjg.regtrace.persistence.dto.jpa;
import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 10:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListQueryWorkRegistrationApplicationJpaSpec extends BaasQuery {

    @ApiModelProperty(value = "审核状态 0-草稿 1-待审核 2-审核通过 3-审核驳回")
    private Integer auditStatusEq;

    @ApiModelProperty(value = "申请人ID")
    private Integer applicantUserIdEq;

    @ApiModelProperty(value = "ID")
    private List<Integer> idIn;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime createTimeGtEq;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime createTimeLtEq;

    @ApiModelProperty(value = "登记编号")
    private String registrationNumberEq;

    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Integer deletedEq;

}
