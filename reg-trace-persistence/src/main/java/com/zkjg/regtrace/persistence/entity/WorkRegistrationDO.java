package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "work_registration")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkRegistrationDO implements Serializable {

   private static final long serialVersionUID = 1L;

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   @Comment("主键")
   private Integer id;

   /**
    * 登记编号
    */
   @Column(name = "registration_number", unique = true, length = 128)
   @Comment("登记编号")
   private String registrationNumber;

   /**
    * 作品所属用户id
    */
   @Column(name = "applicant_user_id", nullable = false)
   @Comment("作品所属用户id")
   private Integer applicantUserId;

   /**
    * 申请人名字
    */
   @Column(name = "applicant_name", length = 100)
   @Comment("申请人名字")
   private String applicantName;

   /**
    * 申请人邮箱
    */
   @Column(name = "applicant_email", length = 100)
   @Comment("申请人邮箱")
   private String applicantEmail;

   /**
    * 作品名称
    */
   @Column(name = "work_name", nullable = false, length = 255)
   @Comment("作品名称")
   private String workName;

   @Column(name="file_type")
   @Comment("作品类型 0=音频，1=视频，2=图片")
   private Integer fileType;

   /**
    * 作品描述
    */
   @Column(name = "description", length = 100)
   @Comment("作品描述")
   private String description;

   /**
    * 申请表主键
    */
   @Column(name = "application_id")
   @Comment("申请表主键")
   private Integer applicationId;

   /**
    * 登记时客户端IP地址
    */
   @Column(name = "ip", length = 255)
   @Comment("登记时客户端IP地址")
   private String ip;

   /**
    * 登记时客户端所属地理位置
    */
   @Column(name = "location", length = 255)
   @Comment("登记时客户端所属地理位置")
   private String location;

   /**
    * 删除人用户id
    */
   @Column(name = "deleted_user_id")
   @Comment("删除人用户id")
   private Integer deletedUserId;

   @Comment("创建时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;

   @Comment("更新时间")
   @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
   private LocalDateTime modifyTime;

   @Comment("逻辑删除时间")
   @Column(name = "deleted_time")
   private LocalDateTime deletedTime;

   /**
    * 是否删除，0否，1是
    */
   @Column(name = "deleted", nullable = false, columnDefinition = "tinyint default 0")
   @Comment("是否删除，0否，1是")
   private Integer deleted;

   /**
    * 回收站0-不在回收站 1-在回收站
    */
   @Column(name = "recycle", columnDefinition = "tinyint default 0")
   @Comment("回收站0-不在回收站 1-在回收站")
   private Integer recycle;

   @Comment("回收站更新时间")
   @Column(name = "recycle_update_time")
   private LocalDateTime recycleUpdateTime;
}
