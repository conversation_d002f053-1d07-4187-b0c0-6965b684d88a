package com.zkjg.regtrace.persistence.dto.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryMessageNotifyJpaSpec extends BaasQuery {

    private LocalDateTime createTimeGtEq;

    private LocalDateTime createTimeLtEq;

    private Integer categoryEq;

    private Integer isReadEq;

    private String keywordLike;

    private Integer receiverIdEq;

    private Integer deletedEq;

    private List<Integer> idIn;
}
