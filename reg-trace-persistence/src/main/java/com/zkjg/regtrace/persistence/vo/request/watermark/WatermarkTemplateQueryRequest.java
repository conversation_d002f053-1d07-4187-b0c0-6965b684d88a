package com.zkjg.regtrace.persistence.vo.request.watermark;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 水印模板查询VO
 */
@Data
@ApiModel(description = "水印模板查询参数")
public class WatermarkTemplateQueryRequest {
    @ApiModelProperty(value = "模板名称，支持模糊搜索")
    private String templateName;
    
    @ApiModelProperty(value = "水印文件类型：0=音频，1=视频，2=图片")
    private Integer watermarkFileType;
}
