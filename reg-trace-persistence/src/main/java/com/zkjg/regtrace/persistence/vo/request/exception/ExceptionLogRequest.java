package com.zkjg.regtrace.persistence.vo.request.exception;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("异常日志请求")
public class ExceptionLogRequest {

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("当前页")
    private Integer page;

    @ApiModelProperty("页大小")
    private Integer size;

    @ApiModelProperty("操作账户")
    private String operator;

    @ApiModelProperty("异常类型")
    private String exceptionType;

    @ApiModelProperty("日志内容")
    private String errorContent;
}
