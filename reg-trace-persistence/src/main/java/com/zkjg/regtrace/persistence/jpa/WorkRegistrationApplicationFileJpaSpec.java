package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkRegistrationApplicationFileJpaSpec extends BaasQuery {

    private String fileNameLike;

    private List<Integer> applicationIdIn;

    private String originalWorkHashEqOrWatermarkedWorkHashEq;

    private LocalDateTime createTimeGtEq;

    private LocalDateTime createTimeLt;

    private Long fileSizeGtEq;

    private Long fileSizeLtEq;

    private Integer fileTypeEq;

    private Integer deletedEq;
}
