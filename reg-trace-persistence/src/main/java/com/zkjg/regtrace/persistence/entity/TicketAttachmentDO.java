package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 工单附件实体
 * <AUTHOR>
 */
@Entity
@Table(name = "ticket_attachment")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TicketAttachmentDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的工单ID（首次提交的附件）
     */
    private Long ticketId;

    /**
     * 关联的交流记录ID（后续交流中的附件，可为空）
     */
    private Long communicationId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 上传时间
     */
    @Comment("上传时间")
    private LocalDateTime uploadTime;
}
