package com.zkjg.regtrace.persistence.vo.request.userinfo;

import com.zkjg.regtrace.common.constants.RegexConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @description 修改用户信息
 * @create 2025/6/11 16:09
 */
@Data
public class EditUserInfoRequest {

    @ApiModelProperty(value = "用户id", hidden = true)
    private Integer userId;

    @ApiModelProperty(value = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    @ApiModelProperty(value = "验证码")
    private String verificationCode;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty("姓名")
    private String personName;

    @ApiModelProperty("身份证号")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String idCard;

    @ApiModelProperty("身份证人像面")
    private String idCardBack;

    @ApiModelProperty("身份证国徽面")
    private String idCardFront;

    @ApiModelProperty("通知方式 1：站内信 2：邮件 3：短信，以逗号隔开")
    private String noticeType;

    @ApiModelProperty(value = "机构名称不能为空")
    private String orgName;

    @ApiModelProperty(value = "统一社会信用代码不能为空")
    private String creditCode;

    @ApiModelProperty(value = "企业地址、办公地址")
    private String address;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "法人姓名（企业用户）")
    private String legalName;

    @ApiModelProperty(value = "法人身份证号（企业用户）")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String legalId;

    @ApiModelProperty(value = "经办人姓名（企业用户）")
    private String agentName;

    @ApiModelProperty(value = "经办人身份证号（企业用户）")
    @Pattern(regexp = RegexConstant.ID_CARD_REGEX, message = "请输入有效的身份证号码")
    private String agentId;

    @ApiModelProperty(value = "经办人联系电话（企业用户）")
    @Pattern(regexp = RegexConstant.PHONE_REGEX, message = "请输入有效的11位手机号码")
    private String agentPhone;
}
