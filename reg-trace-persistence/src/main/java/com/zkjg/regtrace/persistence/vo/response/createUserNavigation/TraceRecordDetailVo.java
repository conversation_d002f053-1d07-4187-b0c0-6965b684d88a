package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 溯源记录详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceRecordDetailVo {

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty(value = "溯源时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("溯源方式：hash-条件溯源；file-文件溯源")
    private String traceabilityType;

    /** 创建人 */
    @ApiModelProperty("溯源用户")
    private String creator;

    @ApiModelProperty("来源信息")
    private RegisterVo registerVo;

    @ApiModelProperty("传播路径")
    private List<PropagationPathVo> propagationPathVoList;

    @ApiModelProperty("水印提取记录")
    private WatermarkExtractRecordVo watermarkExtractRecordVo;


}
