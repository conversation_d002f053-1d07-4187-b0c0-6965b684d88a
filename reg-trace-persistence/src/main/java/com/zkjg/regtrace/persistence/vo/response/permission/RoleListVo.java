package com.zkjg.regtrace.persistence.vo.response.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 16:09
 */
@Data
public class RoleListVo {
    @ApiModelProperty("角色编号")
    private Integer id;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("角色状态")
    private Integer roleStatus;

    @ApiModelProperty("是否可以删除，0：不可以,1:可以")
    private Integer canDelete;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
