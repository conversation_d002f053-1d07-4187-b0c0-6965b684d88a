package com.zkjg.regtrace.persistence.vo.request.watermark;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 水印上传请求VO
 */
@Data
@ApiModel(description = "水印上传请求参数")
public class WatermarkUploadRequest {
    @ApiModelProperty(value = "水印名称", required = true)
    @NotBlank(message = "水印名称不能为空")
    private String name;

    @ApiModelProperty(value = "水印文件URL")
    private String fileUrl;

    @ApiModelProperty(value = "水印文件原始名称")
    private String fileName;

    @ApiModelProperty(value = "水印文字内容（fileType = 3时必填）")
    private String textContent;

    @ApiModelProperty(value = "水印类型：0=音频，1=视频，2=图片，3=文字", required = true)
    @NotNull(message = "水印类型不能为空")
    private Integer fileType;

    @ApiModelProperty(value = "文件格式（如：mp4、wav、png等）")
    private String fileFormat;

    private String fontStyle;

    private String fontSize;

    /**
     * 文件哈希值
     */
    private String fileHash;

    /**
     * 是否共享：0=否，1=是
     */
    private Integer isShared = 0;
}
