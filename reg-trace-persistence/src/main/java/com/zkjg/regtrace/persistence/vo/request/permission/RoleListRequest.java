package com.zkjg.regtrace.persistence.vo.request.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.constants.CommonConstant;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 16:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleListRequest extends BaasQuery {
    @ApiModelProperty("角色名称")
    @Size(max = 255, message = "角色名称过长")
    private String roleNameLike;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeGt;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeLt;

    @ApiModelProperty("角色状态")
    @EnumIntercept(clazz = StatusEnum.class, message = "角色状态不合法", method = "getCode")
    private Integer roleStatusEq;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型")
    @EnumIntercept(clazz = SortTypeEnum.class, message = "排序类型不合法")
    private String orderType;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "createTime";

    @ApiModelProperty(hidden = true)
    private final String roleNameNotEq = CommonConstant.SUPPER_ADMIN_CODE;

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
