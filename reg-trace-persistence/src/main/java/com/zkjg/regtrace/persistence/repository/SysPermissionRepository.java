package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.SysPermissionDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface SysPermissionRepository extends JpaRepository<SysPermissionDO, Integer>, JpaSpecificationExecutor<SysPermissionDO> {

    List<SysPermissionDO> findByParentIdAndDeleted(Integer parentId, Integer deleted);

    @Query("select distinct (p.interfaceIdentity) from SysRolePermissionDO rp left join SysPermissionDO p on rp.permissionId = p.id " +
            "where rp.roleId in :roles")
    List<String> selectInterfaceIdentityForRoles(Set<Integer> roles);

    @Query("select new com.zkjg.regtrace.persistence.entity.SysPermissionDO(p.id,p.permissionName,p.parentId,p.sortNumber,p.routeUrl,p" +
            ".permissionType,p.icon) from " +
            "SysRolePermissionDO rp left join SysPermissionDO p on rp.permissionId = p.id " +
            "where rp.roleId in :roles and p.permissionType in :permissionTypes and p.platform = :platform ")
    Set<SysPermissionDO> selectMenuByPermissionType(List<Integer> roles, List<Integer> permissionTypes, String platform);

    @Query("select new com.zkjg.regtrace.persistence.entity.SysPermissionDO(p.id,p.permissionName,p.parentId,p.sortNumber,p.routeUrl,p.permissionType" +
            ",p.icon) from " +
            "SysPermissionDO p  where p.permissionType in :permissionTypes and p.platform = :platform ")
    List<SysPermissionDO> selectMenuByPermissionType(List<Integer> permissionTypes, String platform);
}
