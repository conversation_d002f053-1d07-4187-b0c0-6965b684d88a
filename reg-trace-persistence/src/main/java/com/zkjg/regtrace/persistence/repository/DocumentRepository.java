package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.DocumentDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:55
 */
@Repository
public interface DocumentRepository extends JpaRepository<DocumentDO, Integer>, JpaSpecificationExecutor<DocumentDO> {

    DocumentDO findByIdAndDeleted(Integer id, int deleted);
    
}
