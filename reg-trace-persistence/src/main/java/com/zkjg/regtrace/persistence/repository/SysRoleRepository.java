package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.SysRoleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SysRoleRepository extends JpaRepository<SysRoleDO, Integer>,JpaSpecificationExecutor<SysRoleDO> {

    @Query(value = "select new com.zkjg.regtrace.persistence.entity.SysRoleDO(r.id,r.roleName) from SysUserRoleDO ur left join SysRoleDO r on ur" +
            ".roleId = r.id where ur.userId = :userId")
    List<SysRoleDO> findRoleByUserId(Integer userId);

    Optional<SysRoleDO> findByRoleName(String roleName);

    List<SysRoleDO> findAllByRoleStatusAndDeleted(Integer roleStatus, Integer deleted);
}
