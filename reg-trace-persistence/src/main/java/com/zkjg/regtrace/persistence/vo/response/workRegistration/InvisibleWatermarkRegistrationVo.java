package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "暗水印登记信息")
public class InvisibleWatermarkRegistrationVo {

    @ApiModelProperty("服务提供者代码")
    private String serviceProviderCode;

    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("原始文件哈希")
    private String originalWorkHash;

    @ApiModelProperty(value = "创作者编号")
    private String creator;
    
    @ApiModelProperty("设备编号")
    private String deviceNumber;

    @ApiModelProperty("水印位置")
    private String waterMarkLocation;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
