package com.zkjg.regtrace.persistence.vo.request.ticket;

import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 工单查询请求
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工单查询请求")
public class TicketQueryRequest extends BaasQuery {

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @ApiModelProperty("提交人用户id,前端无需传递")
    private Integer creatorId;

    @ApiModelProperty("提出人")
    private String creatorName;

    @ApiModelProperty("工单编号")
    private String ticketNo;

    @Comment("提交时间-开始 格式yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @Comment("提交时间-结束 格式yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("问题分类")
    private String category;

    @ApiModelProperty("紧急程度 1-低，2-中，3-高，4-紧急")
    private Integer urgency;

    @ApiModelProperty("状态 0-待分配，1-待客服确认，2-处理中，3-待用户确认，4-已关闭")
    private Integer status;
}
