package com.zkjg.regtrace.persistence.vo.request.permission;

import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.PermissionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @description 添加菜单-入参
 * @create 2024/6/5 10:50
 */
@Data
public class PermissionAddRequest {

    @NotNull(message = "上级菜单不能为空")
    @ApiModelProperty(value = "上级菜单", required = true)
    private Integer parentId;

    @NotNull(message = "菜单类型不能为空")
    @ApiModelProperty(value = "菜单类型", required = true)
    @EnumIntercept(clazz = PermissionTypeEnum.class, message = "菜单状态非法")
    private Integer permissionType;

    @NotBlank(message = "菜单名称不能为空")
    @ApiModelProperty(value = "菜单名称", required = true)
    @Size(min = 2, max = 16, message = "请输入有效的菜单名称,长度为2到16个字符")
    private String permissionName;

    @ApiModelProperty("显示排序")
    private Integer sortNumber = 1;

    @ApiModelProperty("请求地址")
    private String routeUrl;

    @ApiModelProperty("icon")
    private String icon;

    @ApiModelProperty("权限标识")
    private String interfaceIdentity;
}
