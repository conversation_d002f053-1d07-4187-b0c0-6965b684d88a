package com.zkjg.regtrace.persistence.vo.response.traceability;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;


@ApiModel("溯源异常日志视图")
@Data
@Builder
public class TraceabilityLogVo {

    @ApiModelProperty("编号")
    private Integer id;

    @ApiModelProperty("登记编号")
    private String registrationNumber;

    @ApiModelProperty("溯源文件名称")
    private String fileName;

    @ApiModelProperty("文件格式")
    private String fileFormat;

    @ApiModelProperty("原始文件Hash")
    private String originalWorkHash;

    @ApiModelProperty("水印Hash")
    private String watermarkHash;

    @ApiModelProperty("异常类型")
    private String exceptionType;

    @ApiModelProperty("异常消息")
    private String errorMessage;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
