package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.SysUserRoleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;
@Repository
public interface SysUserRoleRepository extends JpaRepository<SysUserRoleDO, Integer>, JpaSpecificationExecutor<SysUserRoleDO> {
    void deleteByUserId(Integer userId);

    void removeAllByRoleIdIn(List<Integer> roleIds);

    void removeAllByRoleIdAndUserIdIn(Integer roleId, List<Integer> userIds);

    @Query(value = "select new com.zkjg.regtrace.persistence.entity.SysUserRoleDO(ur.id,ur.userId,ur.roleId) from SysUserRoleDO ur " +
            "left join SysRoleDO r on ur.roleId = r.id where ur.userId = :userId and r.roleStatus= :roleStatus")
    List<SysUserRoleDO> findByUserIdAndStatus(Integer userId, Integer roleStatus);

    List<SysUserRoleDO> findByRoleId(Integer roleId);
}
