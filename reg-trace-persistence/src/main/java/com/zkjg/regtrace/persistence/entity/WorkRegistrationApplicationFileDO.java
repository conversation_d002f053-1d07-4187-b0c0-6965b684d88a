package com.zkjg.regtrace.persistence.entity;

import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import lombok.Data;
import lombok.Builder;;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "work_registration_application_file")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkRegistrationApplicationFileDO {

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;
   /**
   * 申请表主键id
   */
   @Column(name = "application_id")
   private Integer applicationId;
   /**
   * 文件名
   */
   @Column(name = "file_name")
   @AdvancedExportUtil.ExportField("文件名")
   private String fileName;

   /**
   * 文件大小
   */
   @Column(name = "file_size")
   @AdvancedExportUtil.ExportField(value = "文件大小", converter = "fileSizeConverter")
   private Long fileSize;
   /**
   * 上传文件类型：0=音频，1=视频
   */
   @Column(name = "file_type")
   @AdvancedExportUtil.ExportField(value = "文件类型", converter = "fileTypeConverter")
   private Integer fileType;
   /**
   * 文件格式(mp3,mp4,avi等)
   */
   @Column(name = "file_format")
   @AdvancedExportUtil.ExportField("文件格式")
   private String fileFormat;
   /**
   * 原始作品文件 hash（添加水印前）
   */
   @Column(name = "original_work_hash")
   @AdvancedExportUtil.ExportField("原始文件哈希")
   private String originalWorkHash;
   /**
   * 水印处理后的作品文件 hash（添加水印后）
   */
   @Column(name = "watermarked_work_hash")
   @AdvancedExportUtil.ExportField("带水印文件哈希")
   private String watermarkedWorkHash;
   /**
   * 水印的文件hash
   */
   @Column(name = "watermark_hash")
   @AdvancedExportUtil.ExportField("水印哈希")
   private String watermarkHash;

   /**
    * 水印配置
    */
   @Column(name = "watermark_config")
   @AdvancedExportUtil.ExportIgnore()
   private String watermarkConfig;

   /**
    * 暗水印文件hash
    */
   @Column(name = "HIDDEN_WATERMARK_HASH")
   @AdvancedExportUtil.ExportField("暗水印哈希")
   private String hiddenWaterMarkHash;
   /**
   * 音视频的元数据信息
   */
   @Column(name = "meta_data")
   @AdvancedExportUtil.ExportIgnore
   private String metaData;

   /**
    * 品质参数
    */
   @Column(name = "quality_metric")
   @AdvancedExportUtil.ExportIgnore
   private String qualityMetric;

   @Comment("创建时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   @AdvancedExportUtil.ExportField("创建时间")
   private LocalDateTime createTime;

   @Comment("更新时间")
   @Column(name = "modify_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP")
   @AdvancedExportUtil.ExportField("更新时间")
   private LocalDateTime modifyTime;
   /**
   * 是否删除，0否，1是
   */
   @AdvancedExportUtil.ExportIgnore
   private Integer deleted;
}
