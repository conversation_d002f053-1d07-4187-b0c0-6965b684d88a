package com.zkjg.regtrace.persistence.vo.request.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 角色管理查询-入参
 * @create 2024/6/4 9:49
 */
@Data
public class RoleDeleteOrAddUserListRequest {

    @ApiModelProperty("角色Id")
    private Integer roleId;

    @ApiModelProperty("用户Id")
    private List<Integer> userIds;
}
