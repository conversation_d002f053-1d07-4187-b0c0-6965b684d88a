package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.TraceMetricsDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface TraceMetricsRepository extends JpaRepository<TraceMetricsDO, Integer>, JpaSpecificationExecutor<TraceMetricsDO> {

    /**
     * 查询指定日期的每小时溯源数量
     */
    @Query(value = "SELECT TO_CHAR(create_time, 'HH24') AS hour, COUNT(*) AS count " +
            "FROM trace_metrics WHERE (:status IS NULL OR status = :status) " +
            "AND TO_CHAR(create_time, 'YYYYMMDD') = :statTime " +
            "GROUP BY TO_CHAR(create_time, 'HH24') " +
            "ORDER BY hour ASC", nativeQuery = true)
    List<Map<String, Object>> findDailyStatsForDate(@Param("status") Integer status, @Param("statTime") String statTime);

    /**
     * 查询指定月份的每天溯源数量
     */
    @Query(value = "SELECT TO_CHAR(create_time, 'DD') AS day, COUNT(*) AS count " +
            "FROM trace_metrics WHERE (:status IS NULL OR status = :status) " +
            "AND TO_CHAR(create_time, 'YYYYMM') = :statTime " +
            "AND (:endTime IS NULL OR TO_CHAR(create_time, 'YYYYMM') <= :endTime) " +
            "GROUP BY TO_CHAR(create_time, 'DD') " +
            "ORDER BY day ASC", nativeQuery = true)
    List<Map<String, Object>> findMonthlyStatsForMonth(@Param("status") Integer status, @Param("statTime") String statTime ,@Param("endTime") String endTime);

    /**
     * 查询指定年份的每月溯源数量
     */
    @Query(value = "SELECT TO_CHAR(create_time, 'MM') AS month, COUNT(*) AS count " +
            "FROM trace_metrics WHERE (:status IS NULL OR status = :status) " +
            "AND TO_CHAR(create_time, 'YYYY') = :statTime " +
            "GROUP BY TO_CHAR(create_time, 'MM') " +
            "ORDER BY month ASC", nativeQuery = true)
    List<Map<String, Object>> findYearlyStatsForYear(@Param("status") Integer status, @Param("statTime") String statTime);
}
