package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作品登记申请列表响应参数
 * <AUTHOR>
 * @Date 2025/6/11 18:02
 */
@Data
public class WorkRegistrationApplicationVo implements Serializable {
    @ApiModelProperty(value = "申请ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "登记编号", example = "WR2024001001")
    private String registrationNumber;

    @ApiModelProperty(value = "申请人ID", example = "1001")
    private Integer applicantUserId;

    @ApiModelProperty(value = "申请人姓名", example = "张三")
    private String applicantName;

    @ApiModelProperty(value = "申请人邮箱", example = "<EMAIL>")
    private String applicantEmail;

    @ApiModelProperty(value = "申请原因", example = "原创音乐作品版权保护")
    private String applicationReason;

    @ApiModelProperty(value = "审核状态：0-草稿,1-待审核,2-审核通过,3-审核驳回", example = "1")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核意见", example = "材料齐全，可以通过")
    private String auditComment;

    @ApiModelProperty(value = "审核人ID", example = "2001")
    private Integer reviewedUserId;

    @ApiModelProperty(value = "登记结果文件地址", example = "http://minio.example.com/reg-trace/result/WR2024001001.pdf")
    private String registrationResultFileUrl;

    @ApiModelProperty(value = "创建时间", example = "2024-06-11 10:30:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2024-06-11 15:20:00")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;
}
