package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.enums.PlatformEnum;
import com.zkjg.regtrace.common.enums.RoleTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.enums.UserTypeEnum;
import com.zkjg.regtrace.persistence.entity.*;
import com.zkjg.regtrace.persistence.vo.request.permission.*;
import com.zkjg.regtrace.persistence.vo.response.permission.UserManageDetailVo;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 8:58
 */
public class PermissionAssembler {
    public static SysRoleDO initInsertRole(RoleAddRequest req, String loginName) {
        SysRoleDO roleDO = SysRoleDO.builder().build();
        roleDO.setRoleName(req.getRoleName());
        roleDO.setSource(RoleTypeEnum.SYSTEM.getCode());
        roleDO.setCanDelete(StatusEnum.YES.getCode());
        roleDO.setPlatform(req.getPlatform());
        roleDO.setCreator(loginName);
        roleDO.setDeleted(StatusEnum.NO.getCode());
        roleDO.setCreateTime(LocalDateTime.now());
        roleDO.setRoleStatus(StatusEnum.YES.getCode());
        return roleDO;
    }

    public static List<SysRolePermissionDO> initRolePermission(List<Integer> permissionList, Integer roleId, String loginName) {
        return permissionList.stream().map(e -> {
            SysRolePermissionDO rolePermission = SysRolePermissionDO.builder().build();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(e);
            rolePermission.setCreateTime(LocalDateTime.now());
            rolePermission.setCreator(loginName);
            rolePermission.setCanDelete(StatusEnum.YES.getCode());
            return rolePermission;
        }).collect(Collectors.toList());
    }

    public static SysUserDO initInsertSystemUser(UserAddRequest req, String loginName) {
        SysUserDO userDO = SysUserDO.builder().build();
        userDO.setUsername(req.getUsername());
        userDO.setPhone(req.getPhone());
        userDO.setEmail(req.getEmail());
        userDO.setPassword(req.getPassword());
        userDO.setUserType(UserTypeEnum.SYSTEM_USER.getCode());
        userDO.setUserStatus(StatusEnum.YES.getCode());
        userDO.setRemark(req.getRemark());
        userDO.setCreator(loginName);
        userDO.setCreateTime(LocalDateTime.now());
        userDO.setDeleted(StatusEnum.NO.getCode());
        return userDO;
    }

    public static SysRoleDO initEditRole(RoleEditRequest req, SysRoleDO role, String loginName) {
        BeanUtils.copyProperties(req, role);
        role.setModifyTime(LocalDateTime.now());
        role.setModifier(loginName);
        return role;
    }

    public static UserManageDetailVo initUserManageDetail(SysUserDO userDO, SysUserInfoDO userInfoDO, List<SysRoleDO> roleDOS) {
        UserManageDetailVo detail = UserManageDetailVo.builder().build();
        BeanUtils.copyProperties(userDO, detail);
        detail.setUserRoleNames(roleDOS.stream().map(SysRoleDO::getRoleName).distinct().collect(Collectors.joining(",")));
        detail.setRoleList(roleDOS.stream().map(SysRoleDO::getId).collect(Collectors.toList()));
        if (Objects.nonNull(userInfoDO)) {
            detail.setUserInfo(UserAssembler.selectUserInfo(userInfoDO));
        }

        return detail;
    }

    public static void initUserManageEdit(UserManageEditRequest req, SysUserDO sysUserDO, SysUserInfoDO userInfo, String username) {
        sysUserDO.setModifyTime(LocalDateTime.now());
        sysUserDO.setModifier(username);
        sysUserDO.setEmail(req.getEmail());
        sysUserDO.setPhone(req.getPhone());
        sysUserDO.setUserStatus(req.getUserStatus());
        sysUserDO.setRemark(req.getRemark());

        if (Objects.nonNull(userInfo)) {
            if (sysUserDO.getUserType().equals(UserTypeEnum.ENTERPRISE_USER.getCode())) {
                userInfo.setOrgName(req.getOrgName());
                userInfo.setCreditCode(req.getCreditCode());
                userInfo.setCompanyType(req.getCompanyType());
                userInfo.setAddress(req.getAddress());
                userInfo.setContactPhone(req.getPhone());
                userInfo.setLegalId(req.getLegalId());
                userInfo.setLegalName(req.getLegalName());
                userInfo.setAgentName(req.getAgentName());
                userInfo.setAgentId(req.getAgentId());
                userInfo.setAgentPhone(req.getAgentPhone());
                userInfo.setContactEmail(req.getEmail());
            } else if (sysUserDO.getUserType().equals(UserTypeEnum.GOVERNMENT_USER.getCode())) {
                userInfo.setOrgName(req.getOrgName());
                userInfo.setCreditCode(req.getCreditCode());
                userInfo.setContactPhone(req.getPhone());
                userInfo.setContactEmail(req.getEmail());
                userInfo.setAddress(req.getAddress());
            }
        }

    }

    public static SysPermissionDO initEditPermission(PermissionEditRequest req, SysPermissionDO permission, String loginName) {
        BeanUtils.copyProperties(req, permission);
        permission.setModifier(loginName);
        permission.setModifyTime(LocalDateTime.now());
        return permission;
    }

    public static SysPermissionDO initInsertPermission(PermissionAddRequest req, String loginName) {
        SysPermissionDO permissionDO = SysPermissionDO.builder().build();
        BeanUtils.copyProperties(req, permissionDO);
        permissionDO.setCreateTime(LocalDateTime.now());
        permissionDO.setCreator(loginName);
        permissionDO.setDeleted(StatusEnum.NO.getCode());
        permissionDO.setPlatform(PlatformEnum.PC.getCode());
        return permissionDO;
    }
}
