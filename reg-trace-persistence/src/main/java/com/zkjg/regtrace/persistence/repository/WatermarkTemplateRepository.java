package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WatermarkTemplateDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface WatermarkTemplateRepository extends JpaRepository<WatermarkTemplateDO, Integer>,JpaSpecificationExecutor<WatermarkTemplateDO> {
    /**
     * 统计用户有效模板数量
     * @param userId 用户ID
     * @param deleted 删除标志
     * @return 模板数量
     */
    long countByUserIdAndDeleted(Integer userId, Integer deleted);


    @Query("SELECT COUNT(w) FROM WatermarkTemplateDO w WHERE w.createTime >= :start AND w.createTime < :end AND w.deleted = 0")
    long countByCreateTimeBetween(LocalDateTime start, LocalDateTime end);

    Optional<WatermarkTemplateDO> findByTemplateNo(String templateNo);

    WatermarkTemplateDO findByIdAndDeleted(Integer id, int deleted);

    boolean existsByUserIdAndTemplateNameAndDeleted(Integer userId, String templateName, int deleted);
}
