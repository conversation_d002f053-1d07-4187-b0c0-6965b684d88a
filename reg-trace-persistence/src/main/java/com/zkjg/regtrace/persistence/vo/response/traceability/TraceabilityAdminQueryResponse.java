package com.zkjg.regtrace.persistence.vo.response.traceability;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("溯源管理列表查询响应")
public class TraceabilityAdminQueryResponse {

    @ApiModelProperty("ID")
    private Integer id;

    @ApiModelProperty("溯源时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty("溯源用户")
    private String creator;

    @ApiModelProperty("溯源方式")
    private String traceabilityType;

    @ApiModelProperty("溯源结果")
    private Integer status;

    @ApiModelProperty("记录状态")
    private String recordStatus;
}
