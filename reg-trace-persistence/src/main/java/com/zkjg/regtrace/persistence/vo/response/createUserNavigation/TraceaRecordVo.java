package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 溯源记录查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TraceaRecordVo {

    @ApiModelProperty("溯源表主键ID")
    private Integer id;

    @ApiModelProperty(value = "溯源时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("来源登记编号")
    private String registrationNumber;

    @ApiModelProperty("来源作品名称")
    private String fileName;

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty("溯源类型：hash-条件溯源；file-文件溯源")
    private String traceabilityType;

}
