package com.zkjg.regtrace.persistence.entity;

import com.zkjg.regtrace.common.converter.SensitiveEncryptConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/18 9:03
 */
@Entity
@Table(name = "sys_user_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysUserInfoDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 关联的 sys_user 表主键
     */
    private Integer userId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 企业名称或机构全称
     */
    private String orgName;

    /**
     * 统一社会信用代码
     */
    @Convert(converter = SensitiveEncryptConverter.class)
    private String creditCode;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱或企业邮箱
     */
    private String contactEmail;

    /**
     * 企业地址或办公地址
     */
    private String address;

    /**
     * 公司类型（仅企业适用，如有限责任公司）
     */
    private Integer companyType;

    /**
     * 法人身份证号（企业用户）
     */
    @Convert(converter = SensitiveEncryptConverter.class)
    private String legalId;

    /**
     * 法人姓名（企业用户）
     */
    private String legalName;

    /**
     * 经办人姓名（企业用户）
     */
    private String agentName;

    /**
     * 经办人身份证号（企业用户）
     */
    @Convert(converter = SensitiveEncryptConverter.class)
    private String agentId;

    /**
     * 经办人联系电话（企业用户）
     */
    private String agentPhone;
}
