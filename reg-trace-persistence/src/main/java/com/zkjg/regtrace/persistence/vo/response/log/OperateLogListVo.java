package com.zkjg.regtrace.persistence.vo.response.log;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkjg.regtrace.common.converter.ExcelConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 9:06
 */
@Data
@Builder
public class OperateLogListVo {

    @ApiModelProperty("id")
    @ExcelIgnore
    private Integer id;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("操作时间")
    @ColumnWidth(20)
    private LocalDateTime operatorTime;

    @ApiModelProperty("用户名")
    @ExcelProperty("用户名")
    @ColumnWidth(20)
    private String operatorUsername;

    @ApiModelProperty("IP地址")
    @ColumnWidth(20)
    private String ipAddress;

    @ApiModelProperty("操作类型")
    @ColumnWidth(20)
    @ExcelProperty(value = "操作状态", converter = ExcelConvert.class)
    private String operateLogType;

    @ApiModelProperty("日志内容")
    @ExcelProperty("日志内容")
    @ColumnWidth(40)
    private String content;

}
