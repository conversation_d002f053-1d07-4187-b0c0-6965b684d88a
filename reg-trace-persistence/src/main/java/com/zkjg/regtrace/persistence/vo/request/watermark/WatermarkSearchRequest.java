package com.zkjg.regtrace.persistence.vo.request.watermark;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 水印搜索请求VO
 */
@Data
@ApiModel(description = "水印搜索请求参数")
public class WatermarkSearchRequest {
    @ApiModelProperty(value = "水印名称，支持模糊搜索")
    private String name;
    
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "水印类型：0=音频，1=视频，2=图片")
    private Integer fileType;
    
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页数量", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "水印编号")
    private String watermarkNo;

    @ApiModelProperty(value = "文件哈希")
    private String fileHash;
}
