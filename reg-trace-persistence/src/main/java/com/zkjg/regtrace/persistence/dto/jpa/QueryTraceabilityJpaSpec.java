package com.zkjg.regtrace.persistence.dto.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 10:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryTraceabilityJpaSpec extends BaasQuery {

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime createTimeGtEq;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime createTimeLtEq;

    /** 溯源状态 */
    @ApiModelProperty(value = "溯源状态 0-成功；1-失败")
    private Integer statusEq;

    @ApiModelProperty(value = "文件类型 0=音频，1=视频")
    private Integer fileTypeEq;

    @ApiModelProperty(value = "登记ID集合")
    private List<Integer> registerIdIn;

    /** 创建者ID */
    @ApiModelProperty(value = "创建者ID")
    private Integer creatorIdEq;

    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Integer deletedEq;

}
