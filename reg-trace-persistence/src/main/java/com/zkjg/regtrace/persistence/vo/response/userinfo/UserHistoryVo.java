package com.zkjg.regtrace.persistence.vo.response.userinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/16 10:41
 */
@Data
@Builder
public class UserHistoryVo {

    @ApiModelProperty("快照Id")
    private Integer id;

    @ApiModelProperty("变更类型（0、CREATE, 1、DELETE, 2、UPDATE）")
    private Integer changeType;

    @ApiModelProperty("用户信息快照")
    private String snapshotJson;

    @ApiModelProperty("信息快照时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
