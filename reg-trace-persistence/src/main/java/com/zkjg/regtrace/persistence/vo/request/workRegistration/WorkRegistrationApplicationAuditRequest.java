package com.zkjg.regtrace.persistence.vo.request.workRegistration;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 作品登记申请审核请求参数
 * <AUTHOR>
 * @Date 2025/6/11 17:55
 */
@Data
public class WorkRegistrationApplicationAuditRequest {
    @NotNull(message = "申请ID不能为空")
    private Integer id;

    @NotBlank(message = "审核意见不能为空")
    private String auditComment;

    @NotNull(message = "审核状态不能为空,取值为2(通过)或者3(驳回)")
    @Min(2)  //最小值1
    @Max(3)  //最大值2
    private Integer auditStatus;
}
