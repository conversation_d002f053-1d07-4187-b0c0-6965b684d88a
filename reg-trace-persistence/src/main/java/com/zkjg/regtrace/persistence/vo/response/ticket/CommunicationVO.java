package com.zkjg.regtrace.persistence.vo.response.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 交流记录响应VO
 * <AUTHOR>
 */
@Data
@ApiModel("交流记录响应")
public class CommunicationVO {

    @ApiModelProperty("记录ID")
    private Long id;

    @ApiModelProperty("类型 1-用户提交，2-客服确认接收，3-客服拒绝接收，4-客服回复，5-用户追问，6-用户确认，7-系统操作")
    private Integer type;

    @ApiModelProperty("交流内容")
    private String content;

    @ApiModelProperty("操作人ID")
    private Integer operatorId;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("附件列表")
    private List<AttachmentVO> attachments;
}
