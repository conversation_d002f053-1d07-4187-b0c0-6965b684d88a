package com.zkjg.regtrace.persistence.vo.request.workRegistration;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 作品登记申请请求参数
 * @create 2025/6/11 16:50
 */
@Data
public class WorkRegistrationApplicationRequest {

    @ApiModelProperty(value = "申请ID 草稿的时候需要传入")
    private Integer id;

    @ApiModelProperty(value = "作品名称", required = true)
    @NotBlank(message = "作品名称不能为空")
    private String workName;

    @ApiModelProperty(value = "作品描述")
    private String description;

    @ApiModelProperty(value = "登记编号", required = true)
    @NotBlank(message = "登记编号不能为空")
    private String registrationNumber;

    @ApiModelProperty(value = "申请原因", required = true)
    @NotBlank(message = "申请原因不能为空")
    private String applicationReason;

    @ApiModelProperty(value = "是否为草稿", required = false)
    private boolean draft;

    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    @ApiModelProperty(value = "文件大小", required = true)
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型", required = true)
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @ApiModelProperty(value = "文件格式", required = true)
    @NotBlank(message = "文件格式不能为空")
    private String fileFormat;

    @ApiModelProperty(value = "原始文件哈希", required = true)
    @NotBlank(message = "原始文件哈希不能为空")
    private String originalWorkHash;

    @ApiModelProperty(value = "带水印文件哈希", required = true)
    @NotBlank(message = "带水印文件哈希不能为空")
    private String watermarkedWorkHash;

    @ApiModelProperty(value = "水印哈希", required = true)
    @NotBlank(message = "水印哈希不能为空")
    private String watermarkHash;

    @ApiModelProperty(value = "元数据")
    private String metaData;

    @ApiModelProperty(value = "品质参数")
    private String qualityMetric;

    @ApiModelProperty(value = "水印配置")
    private String watermarkConfig;

    @ApiModelProperty(value = "暗水印hash")
    private String hiddenWaterMarkHash;

}

