package com.zkjg.regtrace.persistence.vo.request.workRegistration;

import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkRegistrationApplicationFileRequest extends BaasQuery {

    @ApiModelProperty(value = "水印哈希")
    private String watermarkHash;

    @ApiModelProperty(value = "暗水印哈希")
    private String invisibleWatermarkHash;

    @ApiModelProperty("文件哈希")
    private String fileHash;
}
