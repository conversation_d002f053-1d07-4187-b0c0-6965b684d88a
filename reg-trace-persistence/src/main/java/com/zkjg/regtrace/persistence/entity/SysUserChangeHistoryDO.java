package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_user_change_history")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysUserChangeHistoryDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 关联用户ID
     */
    private Integer userId;
    /**
     * 变更类型（0、CREATE, 1、DELETE, 2、UPDATE）
     */
    private Integer changeType;
    /**
     * 用户信息快照
     */
    private String snapshotJson;

    @Comment("创建时间")
    @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;
}
