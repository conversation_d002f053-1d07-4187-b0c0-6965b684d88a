package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationDO;
import com.zkjg.regtrace.persistence.vo.response.createUserNavigation.ContentManagerVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface WorkRegistrationApplicationRepository extends JpaRepository<WorkRegistrationApplicationDO, Integer>,JpaSpecificationExecutor<WorkRegistrationApplicationDO> {

    /**
     * 根据作品名称列表查询申请信息
     * @param workNames 作品名称列表
     * @return 申请信息列表
     */
    List<WorkRegistrationApplicationDO> findByWorkNameIn(List<String> workNames);

    /**
     * 根据作品名称模糊查询申请信息
     * @param workName 作品名称
     * @return 申请信息列表
     */
    List<WorkRegistrationApplicationDO> findByWorkNameContainingAndAuditStatus(String workName,Integer auditStatus);


    WorkRegistrationApplicationDO findByRegistrationNumberAndAuditStatusAndDeleted(String registrationNumber, Integer auditStatus, Integer deleted);

    @Query("SELECT new com.zkjg.regtrace.persistence.vo.response.createUserNavigation.ContentManagerVo(" +
            "wraf.fileType, wraf.fileName, wra.auditStatus, wra.createTime, wraf.fileSize, wraf.watermarkedWorkHash) " +
            "FROM WorkRegistrationApplicationFileDO wraf " +
            "LEFT JOIN WorkRegistrationApplicationDO wra ON wraf.applicationId = wra.id " +
            "WHERE (:fileType IS NULL OR wraf.fileType = :fileType) " +
            "AND (:fileName IS NULL OR wraf.fileName LIKE :fileName) " +
            "AND (:auditStatus IS NULL OR wra.auditStatus = :auditStatus) " +
            "AND (:startTime IS NULL OR wra.createTime >= :startTime) " +
            "AND (:endTime IS NULL OR wra.createTime <= :endTime) " +
            "ORDER BY wra.createTime DESC")
    Page<ContentManagerVo> findRegisterContent(
            @Param("fileType") Integer fileType,
            @Param("fileName") String fileName,
            @Param("auditStatus") Integer auditStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            PageRequest pageRequest
    );

}
