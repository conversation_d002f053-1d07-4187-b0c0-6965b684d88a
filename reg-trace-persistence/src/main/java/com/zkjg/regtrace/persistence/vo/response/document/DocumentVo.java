package com.zkjg.regtrace.persistence.vo.response.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/7/16 15:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "文档管理", description = "文档管理")
public class DocumentVo {

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "文档名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "是否公开")
    private Integer isPublic;

    @ApiModelProperty(value = "浏览量")
    private Long views;

    @ApiModelProperty(value = "下载量")
    private Long downloads;

    @ApiModelProperty(value = "文档文件")
    private String file;

    @ApiModelProperty(value = "文件大小")
    private Long size;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;
}
