package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.persistence.dto.jpa.PageQueryExceptionLogJpaSpec;
import com.zkjg.regtrace.persistence.entity.ExceptionLogDO;
import com.zkjg.regtrace.persistence.vo.request.exception.ExceptionLogRequest;
import com.zkjg.regtrace.persistence.vo.response.exception.ExceptionLogVo;


public class ExceptionAssembler {

    public static ExceptionLogVo convertDoToVo(ExceptionLogDO exceptionLogDO) {
        return ExceptionLogVo.builder()
                .id(exceptionLogDO.getId())
                .operator(exceptionLogDO.getOperator())
                .exceptionType(exceptionLogDO.getExceptionType())
                .errorMessage(exceptionLogDO.getErrorMessage())
                .build();
    }

    public static PageQueryExceptionLogJpaSpec convertLogReqToJpaSpec(ExceptionLogRequest exceptionLogRequest) {
        return PageQueryExceptionLogJpaSpec.builder()
                .exceptionTypeEq(exceptionLogRequest.getExceptionType())
                .operatorLike(exceptionLogRequest.getOperator())
                .exceptionTypeEq(exceptionLogRequest.getExceptionType())
                .errorMessageLike(exceptionLogRequest.getErrorContent())
                .createTimeGt(exceptionLogRequest.getStartTime())
                .createTimeLt(exceptionLogRequest.getEndTime())
                .build();
    }
}
