package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.TicketCommunicationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单交流记录Repository
 * <AUTHOR>
 */
@Repository
public interface TicketCommunicationRepository extends JpaRepository<TicketCommunicationDO, Long> {

    /**
     * 查询工单的所有交流记录，按创建时间升序排序
     */
    List<TicketCommunicationDO> findByTicketIdOrderByCreateTimeAsc(Long ticketId);

    /**
     * 根据操作人ID、类型和时间范围查询工单ID列表
     * @param operatorId 操作人ID
     * @param type 类型（2-客服确认接收，3-客服拒绝接收）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工单ID列表
     */
    @Query("SELECT DISTINCT tc.ticketId FROM TicketCommunicationDO tc " +
           "WHERE tc.operatorId = :operatorId " +
           "AND tc.type = :type " +
           "AND tc.createTime >= :startTime " +
           "AND tc.createTime <= :endTime")
    List<Long> findTicketIdsByOperatorAndTypeAndTimeRange(
            @Param("operatorId") Integer operatorId,
            @Param("type") Integer type,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
}
