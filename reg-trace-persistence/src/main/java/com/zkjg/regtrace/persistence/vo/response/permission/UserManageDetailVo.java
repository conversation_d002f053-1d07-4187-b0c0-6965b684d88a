package com.zkjg.regtrace.persistence.vo.response.permission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserInfoDetailVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/18 17:47
 */
@Data
@Builder
public class UserManageDetailVo {
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("用户状态")
    private Integer userStatus;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty("0 系统用户, 1 个人用户，2 企业用户，3 政府用户")
    private Integer userType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("用户角色")
    private String userRoleNames;

    @ApiModelProperty("用户角色")
    private List<Integer> roleList;

    @ApiModelProperty("注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "企业、政府信息")
    private UserInfoDetailVo userInfo;
}
