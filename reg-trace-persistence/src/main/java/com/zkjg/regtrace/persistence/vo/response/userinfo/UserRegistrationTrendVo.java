package com.zkjg.regtrace.persistence.vo.response.userinfo;

import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户登记趋势
 * <AUTHOR>
 * @Date 2025/6/16 15:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserRegistrationTrendVo {

    @ApiModelProperty(value = "时间")
    @UniversalExportUtil.ExportField("日期")
    private String time;

    @ApiModelProperty(value = "图片总量")
    @UniversalExportUtil.ExportField("图片总量")
    private Integer imageTotal;

    @ApiModelProperty(value = "视频总量")
    @UniversalExportUtil.ExportField("视频总量")
    private Integer videoTotal;

    @ApiModelProperty(value = "音频总量")
    @UniversalExportUtil.ExportField("音频总量")
    private Integer audioTotal;
}
