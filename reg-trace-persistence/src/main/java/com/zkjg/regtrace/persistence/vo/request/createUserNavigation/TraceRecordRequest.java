package com.zkjg.regtrace.persistence.vo.request.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel("溯源记录请求")
public class TraceRecordRequest {

    @ApiModelProperty(value = "开始时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty("当前页码")
    @NotNull(message = "当前页码不能为空")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @ApiModelProperty("每页记录数")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数必须大于等于1")
    private Integer size;

    @ApiModelProperty("来源登记编号")
    private String registrationNumber;

    @ApiModelProperty("来源作品名称")
    private String fileName;

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty("溯源类型：hash-条件溯源；file-文件溯源")
    private String traceabilityType;

}
