package com.zkjg.regtrace.persistence.vo.request.permission;

import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 15:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserListRequest extends BaasQuery {

    @ApiModelProperty(value = "用户名")
    private String usernameLike;

    @ApiModelProperty(value = "用户类型")
    private Integer userTypeEq;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型")
    @EnumIntercept(clazz = SortTypeEnum.class, message = "排序类型不合法")
    private String orderType;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "createTime";

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
