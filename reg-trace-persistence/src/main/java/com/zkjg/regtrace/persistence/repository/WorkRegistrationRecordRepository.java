package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WorkRegistrationRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkRegistrationRecordRepository extends JpaRepository<WorkRegistrationRecordDO, String>,JpaSpecificationExecutor<WorkRegistrationRecordDO> {
    /**
     * 根据申请ID查询申请记录
     * @param id 申请ID
     * @return 申请记录
     */
    List<WorkRegistrationRecordDO> findByApplicationId(Integer id);
    WorkRegistrationRecordDO findByApplicationIdAndDeleted(Integer applicationId, Integer deleted);
}
