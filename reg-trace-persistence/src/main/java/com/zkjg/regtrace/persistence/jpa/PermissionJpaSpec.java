package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/17 13:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class PermissionJpaSpec extends BaasQuery {
    private String permissionNameEq;

    private Integer systemIdEq;

    private Integer parentIdEq;

    private Integer permissionTypeEq;

    private Integer permissionTypeNotEq;

    private Integer deletedEq;

    private Integer idNotEq;

    private Integer typeEq;
}
