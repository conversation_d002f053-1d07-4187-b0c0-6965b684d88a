package com.zkjg.regtrace.persistence.vo.request.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.ValidDateRange;
import com.zkjg.regtrace.common.config.LocalDateTimeDeserializer;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 9:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ValidDateRange(message = "结束时间需大于开始时间,且在30天内", maxDays = 30, startTimeField = "operatorTimeGt", endTimeField = "operatorTimeLt")
public class OperateLogExportRequest extends BaasQuery {
    @ApiModelProperty(value = "用户名")
    private String operatorUsernameLike;

    @ApiModelProperty(value = "操作类型")
    private List<String> operateLogTypeIn;

    @ApiModelProperty(value = "用户名")
    private String operatorUsernameEq;

    @ApiModelProperty(value = "内容")
    private String contentLike;

    @ApiModelProperty("开始时间yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime operatorTimeGt;

    @ApiModelProperty("结束时间yyyy-MM-dd")
    @NotNull(message = "结束时间不能为空")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime operatorTimeLt;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "operatorTime";

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
