package com.zkjg.regtrace.persistence.vo.response.message;

import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("消息通知VO")
public class MessageNotifyVo {

    @ApiModelProperty("消息ID")
    @UniversalExportUtil.ExportIgnore
    private Integer id;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("消息分类 0-系统消息 1-登记消息 2-溯源消息 3-其他")
    @UniversalExportUtil.ExportField(value = "消息分类", converter = "messageCategoryEnumConverter")
    private Integer category;

    @ApiModelProperty("紧急程度 1-常规 2-紧急")
    @UniversalExportUtil.ExportField(value = "紧急程度", converter = "messageLevelEnumConverter")
    private Integer level;

    @ApiModelProperty("消息概述")
    @UniversalExportUtil.ExportField("消息概述")
    private String remark;

    @ApiModelProperty("是否已读 0-未读 1-已读")
    @UniversalExportUtil.ExportField(value = "是否已读", converter = "statusEnumConverter")
    private Integer isRead;

    @ApiModelProperty("创建时间")
    @UniversalExportUtil.ExportField("创建时间")
    private LocalDateTime createTime;
}
