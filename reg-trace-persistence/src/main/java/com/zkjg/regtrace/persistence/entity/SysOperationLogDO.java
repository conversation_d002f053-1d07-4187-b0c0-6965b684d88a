package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "sys_operation_log")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysOperationLogDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 业务操作类型
     */
    private String operateLogType;
    /**
     * 操作人用户id
     */
    private Integer operatorUserId;
    /**
     * 操作人用户名
     */
    private String operatorUsername;
    /**
     * 操作状态 0 1
     */
    private Integer operationStatus;
    /**
     * 错误消息
     */
    private String errorMsg;
    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;
    /**
     * 消耗时间,单位毫秒
     */
    private Long consumeTime;
    /**
     * 日志内容
     */
    private String content;

    private String ipAddress;
    /**
     * 是否删除，0标识否，1标识是
     */
    private Integer deleted;

}
