package com.zkjg.regtrace.persistence.vo.response.watermark;

import cn.hutool.core.bean.BeanUtil;
import com.zkjg.regtrace.persistence.entity.WatermarkTemplateDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 水印模板VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "水印模板信息")
public class WatermarkTemplateVO {
    @ApiModelProperty(value = "模板ID")
    private Integer id;
    
    @ApiModelProperty(value = "用户ID")
    private Integer userId;
    
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板编号")
    private String templateNo;

    @ApiModelProperty(value = "水印ID")
    private Integer watermarkId;

    @ApiModelProperty(value = "水印文件类型：0=音频，1=视频，2=图片")
    private Integer watermarkFileType;

    @ApiModelProperty(value = "水印字体")
    private String fontStyle;

    @ApiModelProperty(value = "水印颜色")
    private String colorSpace;

    @ApiModelProperty(value = "水印位置（如：1-1, 1-2）")
    private String location;

    @ApiModelProperty(value = "水平偏移")
    private BigDecimal offsetX;

    @ApiModelProperty(value = "水平偏移单位（如：px, %）")
    private String offsetXUnit;

    @ApiModelProperty(value = "垂直偏移")
    private BigDecimal offsetY;

    @ApiModelProperty(value = "垂直偏移单位（如：px, %）")
    private String offsetYUnit;

    @ApiModelProperty(value = "旋转角度（度数）")
    private BigDecimal rotation;

    @ApiModelProperty(value = "透明度（0.0-1.0）")
    private BigDecimal opacity;

    private Integer width;

    @ApiModelProperty(value = "开始时间戳（毫秒）")
    private String startTime;

    @ApiModelProperty(value = "循环次数")
    private Long loopCount;

    @ApiModelProperty(value = "单次持续时间（毫秒）")
    private String duration;

    @ApiModelProperty(value = "循环间隔时间（毫秒）")
    private String loopInterval;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    public static WatermarkTemplateVO fromDO(WatermarkTemplateDO templateDO) {
        if (templateDO == null) {
            return null;
        }
        return BeanUtil.copyProperties(templateDO, WatermarkTemplateVO.class);
    }
}
