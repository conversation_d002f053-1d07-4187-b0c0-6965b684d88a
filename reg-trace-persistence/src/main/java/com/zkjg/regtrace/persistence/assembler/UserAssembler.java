package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.enums.OperateTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import com.zkjg.regtrace.common.enums.UserTypeEnum;
import com.zkjg.regtrace.persistence.entity.SysUserChangeHistoryDO;
import com.zkjg.regtrace.persistence.entity.SysUserDO;
import com.zkjg.regtrace.persistence.entity.SysUserInfoDO;
import com.zkjg.regtrace.persistence.vo.request.register.RegisterRequest;
import com.zkjg.regtrace.persistence.vo.request.userinfo.EditUserInfoRequest;
import com.zkjg.regtrace.persistence.vo.response.userinfo.UserInfoDetailVo;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 9:18
 */
public class UserAssembler {
    public static SysUserDO initInsert(RegisterRequest req) {
        SysUserDO user = SysUserDO.builder()
                .username(req.getUsername())
                .password(req.getPassword())
                .email(req.getEmail())
                .userType(req.getUserType())
                .userStatus(StatusEnum.YES.getCode())
                .createTime(LocalDateTime.now())
                .deleted(StatusEnum.NO.getCode())
                .phone(req.getPhone())
                .build();

        if (req.getUserType().equals(UserTypeEnum.ENTERPRISE_USER.getCode()) || req.getUserType().equals(UserTypeEnum.GOVERNMENT_USER.getCode())) {
            SysUserInfoDO userInfo = new SysUserInfoDO();
            if (req.getUserType().equals(UserTypeEnum.ENTERPRISE_USER.getCode())) {
                userInfo.setOrgName(req.getOrgName());
                userInfo.setCreditCode(req.getCreditCode());
                userInfo.setCompanyType(req.getCompanyType());
                userInfo.setAddress(req.getAddress());
                userInfo.setContactPhone(req.getPhone());
                userInfo.setLegalId(req.getLegalId());
                userInfo.setLegalName(req.getLegalName());
                userInfo.setAgentName(req.getAgentName());
                userInfo.setAgentId(req.getAgentId());
                userInfo.setAgentPhone(req.getAgentPhone());
                userInfo.setContactEmail(req.getEmail());
                userInfo.setUserType(req.getUserType());
            } else if (req.getUserType().equals(UserTypeEnum.GOVERNMENT_USER.getCode())) {
                userInfo.setOrgName(req.getOrgName());
                userInfo.setCreditCode(req.getCreditCode());
                userInfo.setContactPhone(req.getPhone());
                userInfo.setContactEmail(req.getEmail());
                userInfo.setAddress(req.getAddress());
                userInfo.setUserType(req.getUserType());
            }
            user.setUserInfo(userInfo);
        }
        return user;
    }

    public static void initEdit(EditUserInfoRequest req, SysUserDO sysUserDO, SysUserInfoDO userInfo, String username) {
        sysUserDO.setModifyTime(LocalDateTime.now());
        sysUserDO.setModifier(username);
        sysUserDO.setEmail(req.getEmail());
        sysUserDO.setPhone(req.getPhone());
        sysUserDO.setPersonName(req.getPersonName());
        sysUserDO.setIdCard(req.getIdCard());
        sysUserDO.setIdCardFront(req.getIdCardFront());
        sysUserDO.setIdCardBack(req.getIdCardBack());
        sysUserDO.setNoticeType(req.getNoticeType());

        if (Objects.nonNull(userInfo)) {
            if (sysUserDO.getUserType().equals(UserTypeEnum.ENTERPRISE_USER.getCode())) {
                userInfo.setOrgName(req.getOrgName());
                userInfo.setCreditCode(req.getCreditCode());
                userInfo.setCompanyType(req.getCompanyType());
                userInfo.setAddress(req.getAddress());
                userInfo.setContactPhone(req.getPhone());
                userInfo.setLegalId(req.getLegalId());
                userInfo.setLegalName(req.getLegalName());
                userInfo.setAgentName(req.getAgentName());
                userInfo.setAgentId(req.getAgentId());
                userInfo.setAgentPhone(req.getAgentPhone());
                userInfo.setContactEmail(req.getEmail());
            } else if (sysUserDO.getUserType().equals(UserTypeEnum.GOVERNMENT_USER.getCode())) {
                userInfo.setOrgName(req.getOrgName());
                userInfo.setCreditCode(req.getCreditCode());
                userInfo.setContactPhone(req.getPhone());
                userInfo.setContactEmail(req.getEmail());
                userInfo.setAddress(req.getAddress());
            }
        }
    }

    public static SysUserChangeHistoryDO initUserChangeHistory(Integer userId, String username, String jsonData, OperateTypeEnum operateTypeEnum) {
        return SysUserChangeHistoryDO.builder()
                .userId(userId)
                .changeType(operateTypeEnum.getCode())
                .snapshotJson(jsonData)
                .creator(username)
                .createTime(LocalDateTime.now())
                .deleted(StatusEnum.NO.getCode())
                .build();
    }

    public static UserInfoDetailVo selectUserInfo(SysUserInfoDO userInfo) {
        UserInfoDetailVo userInfoVo = new UserInfoDetailVo();
        userInfoVo.setOrgName(userInfo.getOrgName());
        userInfoVo.setCreditCode(userInfo.getCreditCode());
        userInfoVo.setAddress(userInfo.getAddress());
        userInfoVo.setCompanyType(userInfo.getCompanyType());
        userInfoVo.setLegalName(userInfo.getLegalName());
        userInfoVo.setLegalId(userInfo.getLegalId());
        userInfoVo.setAgentName(userInfo.getAgentName());
        userInfoVo.setAgentId(userInfo.getAgentId());
        userInfoVo.setAgentPhone(userInfo.getAgentPhone());
        return userInfoVo;
    }
}
