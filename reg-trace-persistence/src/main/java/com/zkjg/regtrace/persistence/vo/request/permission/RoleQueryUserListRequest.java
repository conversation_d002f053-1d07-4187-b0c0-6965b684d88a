package com.zkjg.regtrace.persistence.vo.request.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 角色管理查询-入参
 * @create 2024/6/4 9:49
 */
@Data
public class RoleQueryUserListRequest {

    @ApiModelProperty("角色id")
    private Integer roleId;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("手机号码")
    private String phone;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型")
    private String orderType;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "createTime";
}
