package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "message_notify")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MessageNotifyDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 发送类型,0(邮箱),1(站内信)
     */
    private Integer sendType;
    /**
     * 发送者id
     */
    private Integer senderId;
    /**
     * 发送结果 0-成功,1-失败
     */
    private Integer status;
    /**
     * 接收者id
     */
    private Integer receiverId;
    /**
     * 内容
     */
    private String content;
    /**
     * 分类,0、系统消息,1、登记消息
     */
    private Integer category;

    /**
     * 概述,对消息的简要概况
     */
    private String remark;

    /**
     * 是否已读 0-未读 1-已读
     */
    private Integer isRead;

    /**
     * 紧急程度 1-常规 2-紧急
     */
    private Integer level;

    /**
     * 删除标识 0-未删除 1-已删除
     */
    private Integer deleted;

    @Comment("创建时间")
    @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
