package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class WorkRegistrationJpaSpec extends BaasQuery {

    private String registrationNumberEq;

    private Integer auditStatusEq;

    private Integer idEq;

    private Integer deletedEq;
}
