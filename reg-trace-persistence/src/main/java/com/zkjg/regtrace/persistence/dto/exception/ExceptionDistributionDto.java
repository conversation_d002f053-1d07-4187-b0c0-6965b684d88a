package com.zkjg.regtrace.persistence.dto.exception;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionDistributionDto {

    @ApiModelProperty(value = "异常类型")
    private String exceptionType;

    @ApiModelProperty("占比")
    private Float percentage;

    @JsonIgnore
    private Float total;
}
