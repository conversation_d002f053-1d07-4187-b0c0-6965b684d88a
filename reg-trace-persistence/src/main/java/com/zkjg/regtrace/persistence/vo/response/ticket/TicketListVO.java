package com.zkjg.regtrace.persistence.vo.response.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单列表响应VO
 * <AUTHOR>
 */
@Data
@ApiModel("工单列表响应")
public class TicketListVO {

    @ApiModelProperty("工单ID")
    private Long id;

    @ApiModelProperty("工单编号")
    private String ticketNo;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("问题分类")
    private String category;

    @ApiModelProperty("紧急程度 1-低，2-中，3-高，4-紧急")
    private Integer urgency;

    @ApiModelProperty("处理状态 0-待分配，1-待客服确认，2-处理中，3-待用户确认，4-已关闭")
    private Integer status;

    @ApiModelProperty("提交人用户ID")
    private Integer submitterId;

    @ApiModelProperty("提出人")
    private String submitterName;

    @ApiModelProperty("分配客服ID")
    private Integer assignedToId;

    @ApiModelProperty("处理人")
    private String assignedToName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
