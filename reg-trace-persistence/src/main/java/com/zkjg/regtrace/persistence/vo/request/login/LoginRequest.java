package com.zkjg.regtrace.persistence.vo.request.login;

import com.zkjg.regtrace.common.annotation.UnionValidateIntercept;
import com.zkjg.regtrace.common.constants.UserConstant;
import com.zkjg.regtrace.common.enums.LoginPlatformEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/10 15:22
 */
@Data
@UnionValidateIntercept.List(value = {
        @UnionValidateIntercept(dependentField = "loginType", dependentFieldValue = {UserConstant.LoginType.ACCOUNT_PASSWORD}, currentField = "username", message = "请输入登录账号"),
        @UnionValidateIntercept(dependentField = "loginType", dependentFieldValue = {UserConstant.LoginType.ACCOUNT_PASSWORD}, currentField = "password", message = "请输入密码"),
        @UnionValidateIntercept(dependentField = "loginType", dependentFieldValue = {UserConstant.LoginType.EMAIL}, currentField = "email", message = "请输入登录邮箱"),
        @UnionValidateIntercept(dependentField = "loginType", dependentFieldValue = {UserConstant.LoginType.EMAIL}, currentField = "code", message = "请输入邮箱验证码")
}
)
public class LoginRequest {

    @ApiModelProperty(value = "登录方式 password-账号密码 email-邮箱登录")
    @NotBlank(message = "登录方式不能为空")
    private String loginType;

    @ApiModelProperty(value = "账户")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "邮箱验证码")
    private String code;

    @ApiModelProperty(value = "登录平台 PC：pc端 REGISTER：登记客户端 TRACE：溯源客户端")
    @NotNull(message = "登录平台不能为空")
    private LoginPlatformEnum loginPlatform;
}
