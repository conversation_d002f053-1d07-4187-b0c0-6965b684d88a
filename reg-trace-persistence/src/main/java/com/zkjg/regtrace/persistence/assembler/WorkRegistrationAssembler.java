package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.enums.WorkRegistrationAuditStatusEnum;
import com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationDO;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;

public class WorkRegistrationAssembler {


    public static WorkRegistrationSourceVo buildWorkRegistrationSourceVoV2(WorkFileQueryResultDto fileDO) {
        return WorkRegistrationSourceVo.builder()
                .fileName(fileDO.getFileName())
                .fileSize(fileDO.getFileSize())
                .originalFileHash(fileDO.getOriginalWorkHash())
                .watermarkedFileHash(fileDO.getWatermarkedWorkHash())
                .uploadTime(fileDO.getCreateTime())
                .registrationNumber(fileDO.getRegistrationNumber())
                .registrationStatus(WorkRegistrationAuditStatusEnum.APPROVED.getCode())
                .build();
    }


    /**
     * 构建作品登记来源信息展示对象
     */
    public static WorkRegistrationSourceVo buildWorkRegistrationSourceVo(
            WorkRegistrationDO registrationDO,
            WorkRegistrationApplicationFileDO fileDO
    ) {
        boolean matched = registrationDO != null;

        return WorkRegistrationSourceVo.builder()
                // 文件信息
                .fileName(fileDO != null ? fileDO.getFileName() : null)
                .fileSize(fileDO != null ? fileDO.getFileSize() : null)
                .originalFileHash(fileDO != null ? fileDO.getOriginalWorkHash() : null)
                .watermarkedFileHash(fileDO != null ? fileDO.getWatermarkedWorkHash() : null)
                .uploadTime(fileDO != null ? fileDO.getCreateTime() : null)
                // 登记信息
                .registrationNumber(matched ? registrationDO.getRegistrationNumber() : null)
                .workName(matched ? registrationDO.getWorkName() : null)
                .workType(fileDO != null ? fileDO.getFileType() : null)
                .author(matched ? registrationDO.getApplicantName() : null)
                .publishLocation(matched ? registrationDO.getLocation() :null)
                .description(matched ? registrationDO.getDescription() : null)
                .registrationTime(matched ? registrationDO.getCreateTime() : null)
                .registrationStatus(matched ? WorkRegistrationAuditStatusEnum.APPROVED.getCode() : null)
                .build();
    }
}
