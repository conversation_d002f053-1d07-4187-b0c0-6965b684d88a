package com.zkjg.regtrace.persistence.vo.request.register;

import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.EmailSendTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @description 发送邮箱验证码
 * @create 2025/6/11 16:09
 */
@Data
public class SendEmailRequest {

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户名")
    private String username;

    @NotNull
    @ApiModelProperty("类型 0:注册 1：登录 2：密码找回 3：更改邮箱")
    @EnumIntercept(clazz = EmailSendTypeEnum.class, message = "菜单状态非法", method = "getCode")
    protected Integer emailType;
}
