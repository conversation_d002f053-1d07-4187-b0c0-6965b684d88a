package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DocumentFileJpaSpec extends BaasQuery {

    private String nameLike;

    private Integer typeEq;

    private Integer isPublicEq;

    private Integer deletedEq;
}
