package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WatermarkDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface WatermarkRepository extends JpaRepository<WatermarkDO, Integer>,JpaSpecificationExecutor<WatermarkDO> {
    /**
     * 根据用户ID查询水印列表
     * @param userId 用户ID
     * @param deleted 删除标志
     * @return 水印列表
     */
    List<WatermarkDO> findByUserIdAndDeleted(Integer userId, Integer deleted);
    
    /**
     * 根据名称模糊查询水印列表
     * @param name 水印名称
     * @param deleted 删除标志
     * @return 水印列表
     */
    List<WatermarkDO> findByNameContainingAndDeleted(String name, Integer deleted);
    
    /**
     * 根据时间范围查询水印列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deleted 删除标志
     * @return 水印列表
     */
    List<WatermarkDO> findByCreateTimeBetweenAndDeleted(LocalDateTime startTime, LocalDateTime endTime, Integer deleted);
    
    /**
     * 根据用户ID和名称查询水印列表
     * @param userId 用户ID
     * @param name 水印名称
     * @param deleted 删除标志
     * @return 水印列表
     */
    List<WatermarkDO> findByUserIdAndNameContainingAndDeleted(Integer userId, String name, Integer deleted);
    
    /**
     * 根据用户ID列表查询水印列表
     * @param userIds 用户ID列表
     * @param deleted 删除标志
     * @return 水印列表
     */
    List<WatermarkDO> findByUserIdInAndDeleted(List<Integer> userIds, Integer deleted);

    /**
     * 统计某天创建的水印数量
     * @param startTime 当天开始时间
     * @param endTime 当天结束时间
     * @return 数量
     */
    @Query("SELECT COUNT(w) FROM WatermarkDO w WHERE w.createTime >= :startTime AND w.createTime < :endTime AND w.deleted = 0")
    long countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    Boolean existsByUserIdAndNameAndDeleted(Integer userId, String name, int deleted);

    WatermarkDO findByFileHashAndDeleted(String hash, Integer deleted);

    long countByUserIdAndDeleted(Integer userId, int deleted);
}
