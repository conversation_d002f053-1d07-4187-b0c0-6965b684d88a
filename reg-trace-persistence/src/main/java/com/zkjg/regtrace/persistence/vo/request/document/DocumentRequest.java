package com.zkjg.regtrace.persistence.vo.request.document;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("文档查询请求")
public class DocumentRequest {

    @ApiModelProperty("文档名称")
    private String name;

    @ApiModelProperty("类型:0-操作指南 1-政策文件")
    private Integer type;

    @ApiModelProperty("文档文件")
    private String file;

    @ApiModelProperty(value = "文件大小")
    private Long size;

    @ApiModelProperty("是否公开:0-不公开 1-公开")
    private Integer isPublic;

    @ApiModelProperty("描述")
    private String description;
}
