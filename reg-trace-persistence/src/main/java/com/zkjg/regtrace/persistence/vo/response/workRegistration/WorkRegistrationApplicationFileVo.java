package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作品登记申请列表响应参数
 * <AUTHOR>
 * @Date 2025/6/11 18:02
 */
@Data
public class WorkRegistrationApplicationFileVo implements Serializable {
    @ApiModelProperty(value = "申请ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "作品名称")
    private String workName;

    @ApiModelProperty(value = "登记ID", example = "WR2024001001")
    private String registrationNumber;

    @ApiModelProperty(value = "原始文件hash", example = "faceba47b4f4")
    private String originalWorkHash;

    @ApiModelProperty(value = "带水印文件哈希")
    private String watermarkedWorkHash;

    @ApiModelProperty(value = "作品类型：0=音频，1=视频, 2=图片")
    private Integer fileType;

    @ApiModelProperty(value = "文件名", example = "2750c5c67b012")
    private String fileName;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

//    @ApiModelProperty(value = "版权归属人", example = "张三")
//    private String applicantName;

    @ApiModelProperty(value = "创建时间", example = "2024-06-11 10:30:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "删除时间", example = "2024-06-11 10:30:00")
    private LocalDateTime deleteTime;
}
