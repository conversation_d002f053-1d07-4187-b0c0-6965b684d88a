package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "创作登记统计表格", description = "创作登记统计表格")
public class DataTableVo {

    @ApiModelProperty(value = "日期")
    @UniversalExportUtil.ExportField("日期")
    private String dataTime;

    @ApiModelProperty(value = "内容类型")
    @UniversalExportUtil.ExportField("内容类型")
    private String contentType;

    @ApiModelProperty(value = "新增数量")
    @UniversalExportUtil.ExportField("新增数量")
    private Long increaseNum;

    @ApiModelProperty(value = "累计总数")
    @UniversalExportUtil.ExportField("累计总数")
    private Integer accumulateNum;

    @ApiModelProperty(value = "环比增长率（%）")
    @UniversalExportUtil.ExportField("环比增长率（%）")
    private Integer sequentialGrowthRate;

    // @ApiModelProperty(value = "溯源数量")
    // @UniversalExportUtil.ExportField("溯源数量")
    // private Long traceabilityNum;
}
