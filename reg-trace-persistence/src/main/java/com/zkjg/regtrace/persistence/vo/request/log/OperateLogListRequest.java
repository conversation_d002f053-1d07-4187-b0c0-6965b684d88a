package com.zkjg.regtrace.persistence.vo.request.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.annotation.ValidDateRange;
import com.zkjg.regtrace.common.config.LocalDateTimeDeserializer;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/23 9:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ValidDateRange(message = "结束时间需大于开始时间,且在30天内", maxDays = 30, startTimeField = "operatorTimeGt", endTimeField = "operatorTimeLt")
public class OperateLogListRequest extends BaasQuery {
    @ApiModelProperty(value = "用户名")
    private String operatorUsernameLike;

    @ApiModelProperty(value = "用户名")
    private String operatorUsernameEq;

    @ApiModelProperty(value = "操作内容")
    private String contentLike;

    @ApiModelProperty(value = "操作类型")
    private List<String> operateLogTypeIn;

    @ApiModelProperty("开始时间yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime operatorTimeGt;

    @ApiModelProperty("结束时间yyyy-MM-dd")
    @NotNull(message = "结束时间不能为空")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime operatorTimeLt;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型")
    @EnumIntercept(clazz = SortTypeEnum.class, message = "排序类型不合法")
    private String orderType;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "operatorTime";

    @ApiModelProperty(hidden = true)
    private final Integer deletedEq = StatusEnum.NO.getCode();
}
