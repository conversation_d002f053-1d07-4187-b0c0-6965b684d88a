package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface WorkRegistrationApplicationFileRepository extends JpaRepository<WorkRegistrationApplicationFileDO, Integer>,JpaSpecificationExecutor<WorkRegistrationApplicationFileDO> {
    /**
     * 根据申请表主键查询申请文件信息
     * @param applicationId 申请表主键
     * @return 申请文件信息
     */
    WorkRegistrationApplicationFileDO findByApplicationId(Integer applicationId);

    WorkRegistrationApplicationFileDO findByOriginalWorkHashOrWatermarkedWorkHashAndDeleted(String originalWorkHash, String WatermarkedWorkHash, Integer deleted);

    /**
     * 根据原始文件哈希查询存在的记录（未删除）
     * @param originalWorkHash 原始文件哈希
     * @param deleted 删除状态
     * @return 匹配的记录列表
     */
    List<WorkRegistrationApplicationFileDO> findByOriginalWorkHashAndDeleted(String originalWorkHash, Integer deleted);

    /**
     * 根据带水印文件哈希查询存在的记录（未删除）
     * @param watermarkedWorkHash 带水印文件哈希
     * @param deleted 删除状态
     * @return 匹配的记录列表
     */
    List<WorkRegistrationApplicationFileDO> findByWatermarkedWorkHashAndDeleted(String watermarkedWorkHash, Integer deleted);

    /**
     * 批量查询原始文件哈希是否存在（未删除）
     * @param originalWorkHashes 原始文件哈希列表
     * @param deleted 删除状态
     * @return 已存在的记录列表
     */
    List<WorkRegistrationApplicationFileDO> findByOriginalWorkHashInAndDeleted(List<String> originalWorkHashes, Integer deleted);

    /**
     * 批量查询带水印文件哈希是否存在（未删除）
     * @param watermarkedWorkHashes 带水印文件哈希列表
     * @param deleted 删除状态
     * @return 已存在的记录列表
     */
    List<WorkRegistrationApplicationFileDO> findByWatermarkedWorkHashInAndDeleted(List<String> watermarkedWorkHashes, Integer deleted);

    @Query("SELECT new com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto(" +
            "   app.registrationNumber, " +
            "   file.fileName, " +
            "   file.fileSize, " +
            "   file.originalWorkHash, " +
            "   file.watermarkedWorkHash, " +
            "   file.createTime, " +
            "   2 ) " +
            "FROM WorkRegistrationApplicationFileDO file " +
            "INNER JOIN WorkRegistrationDO app ON file.applicationId = app.applicationId " +
            "WHERE app.recycle = :recycle " +
            "AND app.deleted = :deleted " +
            "AND (:startTime IS NULL OR file.createTime >= :startTime) " +
            "AND (:endTime IS NULL OR file.createTime <= :endTime) " +
            "AND (:registrationNumber IS NULL OR app.registrationNumber = :registrationNumber) " +
            "AND (:fileName IS NULL OR file.fileName = :fileName) " +
            "AND (:hash IS NULL OR file.originalWorkHash = :hash OR file.watermarkedWorkHash = :hash) " +
            "ORDER BY file.createTime DESC")
    WorkFileQueryResultDto querySingleWorkFileInfo(
            @Param("recycle") Integer recycle,
            @Param("deleted") Integer deleted,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("registrationNumber") String registrationNumber,
            @Param("fileName") String fileName,
            @Param("hash") String hash
    );
}
