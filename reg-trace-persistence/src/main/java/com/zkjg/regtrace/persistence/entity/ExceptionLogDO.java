package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "EXCEPTION_LOG")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExceptionLogDO {

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;
   /**
   * 操作人ID
   */
   @Column(name = "operator_user_id")
   private Integer operatorUserId;

   /**
    * 操作人用户名
    */
   @Column(name = "operator")
   private String operator;
   /**
   * 异常种类
   */
   @Column(name = "exception_type")
   private String exceptionType;
   /**
   * 登记编号
   */
   @Column(name = "registration_number")
   private String registrationNumber;

   /**
    * 登记编号
    */
   @Column(name = "traceability_number")
   private String traceabilityNumber;

   /**
   * 错误信息
   */
   @Column(name = "error_message")
   private String errorMessage;
   /**
   * 操作时间
   */
   @Column(name = "operation_time")
   private LocalDateTime operationTime;
}
