package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import com.zkjg.regtrace.common.utils.AdvancedExportUtil;
import com.zkjg.regtrace.persistence.entity.WorkRegistrationApplicationFileDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品登记申请详情响应参数
 * <AUTHOR>
 * @Date 2025/6/11 18:02
 */
@Data
public class WorkRegistrationApplicationDetailVo implements Serializable {
    @ApiModelProperty(value = "申请ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "登记编号", example = "WR2024001001")
    @AdvancedExportUtil.ExportField(value = "登记编号")
    private String registrationNumber;

    @ApiModelProperty(value = "申请人ID", example = "1001")
    @AdvancedExportUtil.ExportIgnore
    private Integer applicantUserId;

    @ApiModelProperty(value = "申请人姓名", example = "张三")
    @AdvancedExportUtil.ExportField(value = "申请人姓名")
    private String applicantName;

    @ApiModelProperty(value = "申请人邮箱", example = "<EMAIL>")
    @AdvancedExportUtil.ExportField(value = "申请人邮箱")
    private String applicantEmail;

    @ApiModelProperty(value = "作品名称", example = "我的原创音乐作品")
    @AdvancedExportUtil.ExportField(value = "作品名称")
    private String workName;

    @ApiModelProperty(value = "作品描述")
    private String description;

    @ApiModelProperty(value = "申请原因", example = "原创音乐作品版权保护")
    @AdvancedExportUtil.ExportField(value = "申请原因")
    private String applicationReason;

    @ApiModelProperty(value = "审核状态：0-草稿,1-待审核,2-审核通过,3-审核驳回", example = "1")
    @AdvancedExportUtil.ExportField(value = "审核状态", converter = "auditStatusConverter")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核意见", example = "材料齐全，可以通过")
    @AdvancedExportUtil.ExportField(value = "审核意见")
    private String auditComment;

    @ApiModelProperty(value = "审核人ID", example = "2001")
    @AdvancedExportUtil.ExportIgnore
    private Integer reviewedUserId;

    @ApiModelProperty(value = "审核时间(登记时间)")
    @AdvancedExportUtil.ExportField("审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "登记结果文件地址", example = "http://minio.example.com/reg-trace/result/WR2024001001.pdf")
    @AdvancedExportUtil.ExportField("登记结果文件地址")
    private String registrationResultFileUrl;

    @ApiModelProperty(value = "创建时间(申请时间)")
    @AdvancedExportUtil.ExportField("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @AdvancedExportUtil.ExportField("更新时间")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "关联的文件信息")
    private WorkRegistrationApplicationFileDO file;



    @ApiModelProperty(value = "审核流程")
    private List<WorkRegistrationApplicationRecordVo> record;

}
