package com.zkjg.regtrace.persistence.entity;

import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "trace_metrics")
public class TraceMetricsDO{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 溯源编号（唯一标识）
     */
    private String traceNo;

    /**
     * 用户ID
     */
    private Integer traceUserId;

    /**
     * 溯源类型：文件溯源和条件溯源
     */
    private String traceType;

    /**
     * 溯源结果状态：1=已完成，0=失败
     */
    private Integer status;

    /**
     * 溯源时间
     */
    private LocalDateTime createTime;

}
