package com.zkjg.regtrace.persistence.vo.request.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zkjg.regtrace.common.enums.MessageOperationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("批量操作消息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class BatchUpdateMessageRequest {

    @ApiModelProperty("分页查询请求")
    private MessageNotifyRequest pageQuery;

    @ApiModelProperty("消息id列表")
    private List<Integer> ids;

    @ApiModelProperty("操作类型枚举 MESSAGE_DELETE-删除, MESSAGE_EXPORT-导出, MESSAGE_MARK-标记")
    private MessageOperationTypeEnum operationTypeEnum;

    @JsonIgnore
    private Integer messageSize;
}
