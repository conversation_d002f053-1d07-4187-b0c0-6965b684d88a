package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.common.constants.SqlConstant;
import com.zkjg.regtrace.common.enums.TraceabilityStatusEnum;
import com.zkjg.regtrace.common.enums.TraceabilityTypeEnum;
import com.zkjg.regtrace.persistence.dto.workregistration.WorkFileQueryResultDto;
import com.zkjg.regtrace.persistence.entity.TraceabilityDO;
import com.zkjg.regtrace.persistence.vo.request.traceability.TraceabilityRequest;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityDetailVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityResultVo;
import com.zkjg.regtrace.persistence.vo.response.traceability.TraceabilityVo;
import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;

import java.time.LocalDateTime;


public class TraceabilityAssembler {
    public static TraceabilityDO convertToDO(TraceabilityRequest traceabilityRequest) {
        return TraceabilityDO.builder()
                .registrationNumber(traceabilityRequest.getRegistrationNumber())
                .fileType(traceabilityRequest.getFileType())
                .fileName(traceabilityRequest.getFileName())
                .fileFormat(traceabilityRequest.getFileFormat())
                .originalWorkHash(traceabilityRequest.getOriginalWorkHash())
                .watermarkHash(traceabilityRequest.getWatermarkHash())
                .watermarkedWorkHash(traceabilityRequest.getWatermarkedWorkHash())
                .deleted(SqlConstant.UN_DELETED)
                .build();
    }

    public static TraceabilityVo convertToVO(TraceabilityDO traceabilityDO) {
        return TraceabilityVo.builder()
                .registrationNumber(traceabilityDO.getRegistrationNumber())
                .build();
    }

    /**
     * 将 TraceabilityDO 转为 TraceabilityDetailVo
     */
    public static TraceabilityDetailVo convertToDetailVo(TraceabilityDO traceabilityDO) {
        if (traceabilityDO == null) {
            return null;
        }

        return TraceabilityDetailVo.builder()
                .traceabilityNumber(traceabilityDO.getTraceabilityNumber())
                .traceabilityTime(traceabilityDO.getCreateTime())
                .traceabilityStatus(traceabilityDO.getStatus())
                .traceabilityType(traceabilityDO.getTraceabilityType())
                .traceabilityParams(traceabilityDO.getTraceabilityParams())
                .fileName(traceabilityDO.getFileName())
                .originalWorkHash(traceabilityDO.getOriginalWorkHash())
                .waterMarkedWorkHash(traceabilityDO.getWatermarkedWorkHash())
                .sourceType(traceabilityDO.getSourceType())
                .traceabilityUser(traceabilityDO.getCreator())
                .recordStatus(traceabilityDO.getRecordStatus())
                .build();
    }


    public static TraceabilityResultVo buildTraceabilityResultVo(WorkFileQueryResultDto fileDO, String traceabilityNumber) {
        if (fileDO == null) {
            return TraceabilityResultVo.builder()
                    .traceabilityNumber(traceabilityNumber)
                    .traceabilityStatus(TraceabilityStatusEnum.FAIL.getType())
                    .build();
        }

        return TraceabilityResultVo.builder()
                .traceabilityNumber(traceabilityNumber)
                .traceabilityStatus(TraceabilityStatusEnum.SUCCESS.getType())
                .workRegistrationSourceVo(WorkRegistrationAssembler.buildWorkRegistrationSourceVoV2(fileDO))
                .build();
    }

    public static TraceabilityDO buildTraceabilityDO(WorkFileQueryResultDto fileDO, String traceabilityNumber) {
        return TraceabilityDO.builder()
                .traceabilityNumber(traceabilityNumber)
                .registrationNumber(fileDO.getRegistrationNumber())
                .traceabilityType(TraceabilityTypeEnum.HASH_TRACEABILITY.getType())
                .createTime(LocalDateTime.now())
                .status(TraceabilityStatusEnum.SUCCESS.getType())
                .registrationNumber(fileDO.getRegistrationNumber())
                .deleted(SqlConstant.UN_DELETED)
                .build();
    }

    public static TraceabilityDO buildEmptyTraceabilityDO(String traceabilityNumber) {
        return TraceabilityDO.builder()
                .traceabilityNumber(traceabilityNumber)
                .traceabilityType(TraceabilityTypeEnum.HASH_TRACEABILITY.getType())
                .createTime(LocalDateTime.now())
                .status(TraceabilityStatusEnum.FAIL.getType())
                .deleted(SqlConstant.UN_DELETED)
                .build();
    }

    /**
     * 构建溯源结果返回对象
     */
    public static TraceabilityResultVo buildTraceabilityResultVo(
            String traceabilityNumber,
            Integer status,
            String sourceType,
            WorkRegistrationSourceVo sourceVo
    ) {
        return TraceabilityResultVo.builder()
                .traceabilityNumber(traceabilityNumber)
                .traceabilityStatus(status)
                .sourceType(sourceType)
                .workRegistrationSourceVo(sourceVo)
                .build();
    }
}
