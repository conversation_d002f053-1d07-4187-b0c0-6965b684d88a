package com.zkjg.regtrace.persistence.vo.response.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单详情响应VO
 * <AUTHOR>
 */
@Data
@ApiModel("工单详情响应")
public class TicketDetailVO {

    @ApiModelProperty("工单ID")
    private Long id;

    @ApiModelProperty("工单编号")
    private String ticketNo;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("问题分类")
    private String category;

    @ApiModelProperty("紧急程度 1-低，2-中，3-高，4-紧急")
    private Integer urgency;

    @ApiModelProperty("问题描述")
    private String description;

    @ApiModelProperty("提交人ID")
    private Integer creatorId;

    @ApiModelProperty("提交人名称")
    private String creatorName;

    @ApiModelProperty("分配客服ID")
    private Integer assignedToId;

    @ApiModelProperty("分配客服名称")
    private String assignedToName;

    @ApiModelProperty("状态 0-待分配，1-待客服确认，2-处理中，3-待用户确认，4-已关闭")
    private Integer status;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("关闭时间")
    private LocalDateTime closeTime;

    @ApiModelProperty("附件列表-首次提交的附件")
    private List<AttachmentVO> attachments;

    @ApiModelProperty("交流记录列表")
    private List<CommunicationVO> communications;
}
