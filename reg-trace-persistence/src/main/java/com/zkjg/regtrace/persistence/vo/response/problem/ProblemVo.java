package com.zkjg.regtrace.persistence.vo.response.problem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/7/16 15:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "文档管理", description = "文档管理")
public class ProblemVo {

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "问题类型")
    private String problemType;

    @ApiModelProperty(value = "问题内容")
    private String problemContent;

    @ApiModelProperty(value = "问题标签")
    private String problemLabel;

    @ApiModelProperty(value = "回答")
    private String answer;

}
