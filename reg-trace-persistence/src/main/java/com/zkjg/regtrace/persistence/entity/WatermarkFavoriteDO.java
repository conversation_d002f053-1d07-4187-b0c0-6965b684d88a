package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "watermark_favorite")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatermarkFavoriteDO {

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;
   /**
   * 用户ID
   */
   private Integer userId;
   /**
   * 水印ID
   */
   private Integer watermarkId;
   /**
   * 收藏备注
   */
   private String remark;
   /**
   * 收藏时间
   */
   @Comment("收藏时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;
   /**
   * 逻辑删除标志：0=否，1=是
   */
   private Integer deleted;
} 