package com.zkjg.regtrace.persistence.vo.request.watermark;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 水印模板请求VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "水印模板创建/更新请求参数")
public class WatermarkTemplateRequest {
    @ApiModelProperty(value = "模板ID，更新时需要传入")
    private Integer id;
    
    @ApiModelProperty(value = "模板名称", required = true)
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    @ApiModelProperty(value = "水印ID", required = true)
    @NotNull(message = "水印ID不能为空")
    private Integer watermarkId;
    
    @ApiModelProperty(value = "水印文件类型：0=音频，1=视频，2=图片", required = true)
    @NotNull(message = "水印文件类型不能为空")
    private Integer watermarkFileType;

    @ApiModelProperty(value = "水印字体")
    private String fontStyle;

    @ApiModelProperty(value = "水印颜色")
    private String colorSpace;

    @ApiModelProperty(value = "水印位置（如：1-1, 1-2）")
    private String location;

    @ApiModelProperty(value = "水平偏移")
    private BigDecimal offsetX;

    @ApiModelProperty(value = "水平偏移单位（如：px, %）")
    private String offsetXUnit;

    @ApiModelProperty(value = "垂直偏移")
    private BigDecimal offsetY;

    @ApiModelProperty(value = "垂直偏移单位（如：px, %）")
    private String offsetYUnit;

    @ApiModelProperty(value = "旋转角度（度数）")
    private BigDecimal rotation;

    @ApiModelProperty(value = "透明度（0.0-1.0）")
    private BigDecimal opacity;

    @ApiModelProperty(value = "水印显示的宽度")
    private Integer width;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "循环次数")
    private Long loopCount;

    @ApiModelProperty(value = "单次持续时间")
    private String duration;

    @ApiModelProperty(value = "循环间隔时间")
    private String loopInterval;
}
