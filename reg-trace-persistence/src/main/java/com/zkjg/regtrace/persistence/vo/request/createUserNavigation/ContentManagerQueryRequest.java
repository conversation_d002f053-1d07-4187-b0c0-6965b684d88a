package com.zkjg.regtrace.persistence.vo.request.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel("数据表格请求")
public class ContentManagerQueryRequest {

    @ApiModelProperty(value = "开始时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间,格式:yyyy-MM-dd HH:mm:ss", example = "2024-07-26 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty("当前页码")
    @NotNull(message = "当前页码不能为空")
    @Min(value = 0, message = "当前页码必须大于等于0")
    private Integer page;

    @ApiModelProperty("每页记录数")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数必须大于等于1")
    private Integer size;

    @ApiModelProperty(value = "文件类型 0=音频，1=视频")
    private Integer contentType;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "审核状态：0-草稿,1-待审核(审核中),2-审核通过,3-审核驳回")
    private Integer auditStatus;
}
