package com.zkjg.regtrace.persistence.vo.request.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
/**
 * <AUTHOR>
 * @description 编辑菜单-入参
 * @create 2024/6/5 10:49
 */
@Data
public class PermissionEditRequest {

    @NotNull(message = "菜单ID不能为空")
    @ApiModelProperty(value = "菜单ID", required = true)
    private Integer id;

    @NotBlank(message = "菜单名称不能为空")
    @ApiModelProperty(value = "菜单名称", required = true)
    private String permissionName;

    @NotNull(message = "显示排序不能为空")
    @ApiModelProperty("显示排序")
    private Integer sortNumber;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("请求地址")
    private String routeUrl;

    @ApiModelProperty("打开方式")
    private Integer openMode;

    @ApiModelProperty("权限标识")
    private String interfaceIdentity;

    @ApiModelProperty("菜单类型 0-通用菜单 1-链相关菜单")
    private Integer type;

    @ApiModelProperty("是否可以被角色添加")
    private Integer canAdd;
}
