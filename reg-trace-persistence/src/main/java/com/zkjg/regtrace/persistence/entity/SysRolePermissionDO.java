package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "sys_role_permission")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysRolePermissionDO {

   @Id
   @GeneratedValue(strategy= GenerationType.IDENTITY)
   private Integer id;
   /**
   * 角色表id
   */
   private Integer roleId;
   /**
   * 权限表id
   */
   private Integer permissionId;
   /**
   * 是否可以删除，0：不可以,1:可以
   */
   private Integer canDelete;

   @Comment("创建时间")
   @Column(name = "create_time", insertable = false, columnDefinition = "datetime default CURRENT_TIMESTAMP")
   private LocalDateTime createTime;
   /**
   * 创建人
   */
   private String creator;


}
