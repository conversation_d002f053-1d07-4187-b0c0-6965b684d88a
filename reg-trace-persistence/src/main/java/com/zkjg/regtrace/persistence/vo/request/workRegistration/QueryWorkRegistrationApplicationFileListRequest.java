package com.zkjg.regtrace.persistence.vo.request.workRegistration;

import com.zkjg.regtrace.common.BaasQuery;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkRegistrationApplicationFileListRequest extends BaasQuery {

    @ApiModelProperty(value = "登记编号")
    private String registrationNumber;

    @ApiModelProperty(value = "作品名称")
    private String fileName;

    @ApiModelProperty(value = "作品类型：0=音频，1=视频, 2=图片")
    private Integer fileType;

    @ApiModelProperty("文件哈希")
    private String fileHash;

    @ApiModelProperty(value = "文件大小范围")
    private Long fileMinSize;

    @ApiModelProperty(value = "文件大小范围")
    private Long fileMaxSize;

    @ApiModelProperty(value = "回收站 0-正常 1-回收站")
    private Integer recycle;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    @Min(value = 1, message = "非法页数")
    private Integer page;

    @NotNull(message = "分页大小不能为空")
    @ApiModelProperty("分页大小")
    private Integer size;

    @NotNull(message = "排序类型不能为空")
    @ApiModelProperty("排序类型")
    @EnumIntercept(clazz = SortTypeEnum.class, message = "排序类型不合法")
    private String orderType;

    @ApiModelProperty(hidden = true)
    private final String orderColumn = "createTime";
}
