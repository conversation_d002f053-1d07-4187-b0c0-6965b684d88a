package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.DocumentFileDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 16:55
 */
@Repository
public interface DocumentFileRepository extends JpaRepository<DocumentFileDO, Integer>, JpaSpecificationExecutor<DocumentFileDO> {

    List<DocumentFileDO> findByDocumentId(Integer documentId);

    List<DocumentFileDO> findByDocumentIdInAndDeleted(List<Integer> documentIds, Integer deleted);
}
