package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/6/26 17:15
 */
@Data
public class WorkRegistrationApplicationRecordVo {
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operateTime;
    @ApiModelProperty(value = "操作人")
    private String operator;
    @ApiModelProperty(value = "操作意见")
    private String operateContent;
}
