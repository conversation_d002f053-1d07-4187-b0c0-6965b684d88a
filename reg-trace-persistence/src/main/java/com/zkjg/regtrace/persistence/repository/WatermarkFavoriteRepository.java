package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.WatermarkFavoriteDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WatermarkFavoriteRepository extends JpaRepository<WatermarkFavoriteDO, Integer>, JpaSpecificationExecutor<WatermarkFavoriteDO> {
    /**
     * 根据用户ID查询收藏的水印
     * @param userId 用户ID
     * @return 收藏水印列表
     */
    List<WatermarkFavoriteDO> findByUserIdAndDeleted(Integer userId, Integer deleted);
    
    /**
     * 根据用户ID和水印ID查询收藏记录
     * @param userId 用户ID
     * @param watermarkId 水印ID
     * @return 收藏记录
     */
    WatermarkFavoriteDO findByUserIdAndWatermarkIdAndDeleted(Integer userId, Integer watermarkId, Integer deleted);
} 