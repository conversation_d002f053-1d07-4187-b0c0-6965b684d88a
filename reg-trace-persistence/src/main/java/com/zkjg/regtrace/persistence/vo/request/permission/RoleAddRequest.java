package com.zkjg.regtrace.persistence.vo.request.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 16:01
 */
@Data
public class RoleAddRequest {

    @NotBlank(message = "角色名称不能为空")
    @Size(max = 200, message = "请输入有效的角色名称,长度不超过200")
    @ApiModelProperty(value = "角色名称", required = true)
    private String roleName;

    @ApiModelProperty("备注")
    @Size(max = 250, message = "备注超长,不能超过250字符")
    private String remark;

    @Size(min = 1, message = "菜单权限不能为空")
    @NotNull(message = "菜单权限不能为空")
    @ApiModelProperty(value = "菜单权限", required = true)
    private List<Integer> permissionList;

    @ApiModelProperty(value = "平台, 1:web 2:客户端")
    private Integer platform;
}
