package com.zkjg.regtrace.persistence.vo.response.createUserNavigation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zkjg.regtrace.common.utils.UniversalExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "内容管理", description = "内容管理")
public class ContentManagerVo {

    @ApiModelProperty(value = "文件类型 0=音频，1=视频")
    @UniversalExportUtil.ExportField("文件类型")
    private Integer fileType;

    @ApiModelProperty(value = "文件名称")
    @UniversalExportUtil.ExportField("文件名称")
    private String fileName;

    @ApiModelProperty(value = "审核状态：0-草稿,1-待审核(审核中),2-审核通过,3-审核驳回")
    @UniversalExportUtil.ExportField("审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @UniversalExportUtil.ExportField("登记时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "文件大小")
    @UniversalExportUtil.ExportField("文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件哈希")
    @UniversalExportUtil.ExportField("文件哈希")
    private String watermarkedWorkHash;

}
