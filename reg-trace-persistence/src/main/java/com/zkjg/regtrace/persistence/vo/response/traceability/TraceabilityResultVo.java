package com.zkjg.regtrace.persistence.vo.response.traceability;

import com.zkjg.regtrace.persistence.vo.response.workRegistration.WorkRegistrationSourceVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "溯源结果", description = "溯源结果")
public class TraceabilityResultVo {

    @ApiModelProperty("溯源编号")
    private String traceabilityNumber;

    @ApiModelProperty(value = "溯源状态 0-失败 1成功")
    private Integer traceabilityStatus;

    @ApiModelProperty("溯源来源")
    private String sourceType;

    @ApiModelProperty("来源信息")
    WorkRegistrationSourceVo workRegistrationSourceVo;
}
