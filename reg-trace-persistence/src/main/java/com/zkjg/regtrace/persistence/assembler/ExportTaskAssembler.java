package com.zkjg.regtrace.persistence.assembler;

import com.zkjg.regtrace.persistence.entity.ExportTaskDO;
import com.zkjg.regtrace.persistence.vo.response.schedule.ExportTaskVo;

/**
 * ExportTask 相关对象转换器（DO <-> VO）
 */
public class ExportTaskAssembler {

    /**
     * DO -> VO
     */
    public static ExportTaskVo convertDoToVo(ExportTaskDO entity) {
        if (entity == null) {
            return null;
        }
        return ExportTaskVo.builder()
                .id(entity.getId())
                .scheduleType(entity.getScheduleType())
                .build();
    }
}
