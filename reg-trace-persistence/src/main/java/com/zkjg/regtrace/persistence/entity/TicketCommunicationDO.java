package com.zkjg.regtrace.persistence.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 工单交流记录实体
 * <AUTHOR>
 */
@Entity
@Table(name = "ticket_communication")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TicketCommunicationDO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 工单ID
     */
    private Long ticketId;

    /**
     * 类型：1-用户提交，2-客服确认接收，3-客服拒绝接收，4-客服回复，5-用户追问，6-用户确认，7-系统操作
     */
    private Integer type;

    /**
     * 交流内容
     */
    private String content;

    /**
     * 操作人ID，可为空表示系统自动操作
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    @Comment("创建时间")
    @CreationTimestamp
    private LocalDateTime createTime;
}
