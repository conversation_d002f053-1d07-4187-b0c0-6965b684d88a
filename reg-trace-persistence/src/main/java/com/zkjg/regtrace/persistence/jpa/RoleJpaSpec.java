package com.zkjg.regtrace.persistence.jpa;

import com.zkjg.regtrace.common.BaasQuery;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 17:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class RoleJpaSpec extends BaasQuery {

    private Integer deletedEq;

    private String roleNameEq;

    private String roleNameNotEq;

    private Integer idNotEq;

    private Integer typeEq;

    private List<Integer> idIn;
}
