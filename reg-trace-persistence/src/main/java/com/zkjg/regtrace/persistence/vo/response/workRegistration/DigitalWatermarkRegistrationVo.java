package com.zkjg.regtrace.persistence.vo.response.workRegistration;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 数字水印登记内容VO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "数字水印登记信息")
public class DigitalWatermarkRegistrationVo extends InvisibleWatermarkRegistrationVo{

    @ApiModelProperty("传播路径")
    private String transPath;
}
