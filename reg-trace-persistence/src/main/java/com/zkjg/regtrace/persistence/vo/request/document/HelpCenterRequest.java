package com.zkjg.regtrace.persistence.vo.request.document;

import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.SortTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("帮助中心请求")
public class HelpCenterRequest {

    @ApiModelProperty("操作")
    private String operation;

}
