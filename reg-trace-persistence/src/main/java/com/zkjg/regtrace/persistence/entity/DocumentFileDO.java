package com.zkjg.regtrace.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Desc {todo}
 * @date 2025-07-16 11:13
 */
@Entity
@Table(name = "document_file")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentFileDO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 文档id
     */
    private Integer documentId;

    /**
     * 文档文件
     */
    private String file;

    /**
     * 文件大小
     */
    private Long size;

    @Comment("创建时间")
    @CreationTimestamp
    private LocalDateTime createTime;

    @Comment("创建人账号")
    @Column(name = "creator", length = 64)
    private String creator;

    @Comment("更新时间")
    @UpdateTimestamp
    private LocalDateTime modifyTime;

    @Comment("更新人账号")
    @Column(name = "modifier", length = 64)
    private String modifier;

    @Comment("是否删除，0标识否，1标识是")
    @Column(name = "deleted", nullable = false)
    private Integer deleted;
}
