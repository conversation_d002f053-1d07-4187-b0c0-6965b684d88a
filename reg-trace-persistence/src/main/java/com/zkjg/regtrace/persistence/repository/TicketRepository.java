package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.TicketDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单Repository
 * <AUTHOR>
 */
@Repository
public interface TicketRepository extends JpaRepository<TicketDO, Long>, JpaSpecificationExecutor<TicketDO> {
    /**
     * 计算客服的工作量（待客服确认+处理中的工单数）
     */
    @Query("SELECT COUNT(t) FROM TicketDO t WHERE t.assignedToId = :assignedToId AND t.status IN :statusList AND t.deleted = 0")
    int countByAssignedToIdAndStatusIn(@Param("assignedToId") Integer assignedToId, @Param("statusList") List<Integer> statusList);

    /**
     * 查询超时未确认的工单
     */
    @Query("SELECT t FROM TicketDO t WHERE t.status = 1 AND t.updateTime < :timeThreshold AND t.deleted = 0")
    List<TicketDO> findPendingConfirmationTicketsExceedingTime(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 查询用户超时未确认的工单（待用户确认状态）
     */
    @Query("SELECT t FROM TicketDO t WHERE t.status = 3 AND t.updateTime < :timeThreshold AND t.deleted = 0")
    List<TicketDO> findUserPendingConfirmationTicketsExceedingTime(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 根据工单ID列表查询工单
     */
    @Query("SELECT t FROM TicketDO t WHERE t.id IN :ticketIds AND t.deleted = 0")
    List<TicketDO> findByIdIn(@Param("ticketIds") List<Long> ticketIds);

    /**
     * 统计客服在指定时间范围内完成的工单数（状态为已关闭）
     */
    @Query("SELECT COUNT(t) FROM TicketDO t " +
           "WHERE t.assignedToId = :assignedToId " +
           "AND t.status = 4 " + // CLOSED
           "AND t.closeTime >= :startTime " +
           "AND t.closeTime <= :endTime " +
           "AND t.deleted = 0")
    long countCompletedTickets(@Param("assignedToId") Integer assignedToId,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);

    /**
     * 按问题分类统计工单数量
     */
    @Query("SELECT t.category as category, COUNT(t) as count " +
           "FROM TicketDO t " +
           "WHERE t.deleted = 0 " +
           "AND t.createTime >= :startTime " +
           "AND t.createTime <= :endTime " +
           "GROUP BY t.category")
    List<Object[]> countTicketsByCategory(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 根据工单ID列表统计工单数量
     */
    @Query("SELECT COUNT(t) FROM TicketDO t WHERE t.id IN :ticketIds AND t.deleted = 0")
    long countByIdIn(@Param("ticketIds") List<Long> ticketIds);

    /**
     * 统计客服在指定时间范围内指定状态的工单数
     */
    @Query("SELECT COUNT(t) FROM TicketDO t " +
           "WHERE t.assignedToId = :assignedToId " +
           "AND t.status IN :statusList " +
           "AND t.createTime >= :startTime " +
           "AND t.createTime <= :endTime " +
           "AND t.deleted = 0")
    long countConfirmedTickets(@Param("assignedToId") Integer assignedToId,
                                                             @Param("statusList") List<Integer> statusList,
                                                             @Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime);
}
