package com.zkjg.regtrace.persistence.vo.request.permission;
import com.zkjg.regtrace.common.annotation.EnumIntercept;
import com.zkjg.regtrace.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/12 16:23
 */
@Data
public class RoleEditRequest {
    @NotNull(message = "角色Id不能为空")
    @ApiModelProperty(value = "角色Id", required = true)
    private Integer id;

    @NotBlank(message = "角色名称不能为空")
    @Size(max = 250, message = "请输入有效的角色名称,长度不超过200")
    @ApiModelProperty(value = "角色名称", required = true)
    private String roleName;

    @NotNull(message = "角色状态不能为空")
    @ApiModelProperty(value = "角色状态", required = true)
    @EnumIntercept(clazz = StatusEnum.class, message = "角色状态不合法",method = "getCode")
    private Integer roleStatus;

    @ApiModelProperty("备注")
    @Size(max = 250, message = "备注超长,不能超过250字符")
    private String remark;

    @Size(min = 1, message = "菜单权限不能为空")
    @NotNull(message = "菜单权限不能为空")
    @ApiModelProperty(value = "菜单权限", required = true)
    private List<Integer> permissionList;
}
