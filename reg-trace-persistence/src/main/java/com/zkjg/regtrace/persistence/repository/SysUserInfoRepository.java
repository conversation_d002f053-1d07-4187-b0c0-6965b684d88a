package com.zkjg.regtrace.persistence.repository;

import com.zkjg.regtrace.persistence.entity.SysUserInfoDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface SysUserInfoRepository extends JpaRepository<SysUserInfoDO, Integer>, JpaSpecificationExecutor<SysUserInfoDO> {
    SysUserInfoDO findByUserId(Integer userId);
}
