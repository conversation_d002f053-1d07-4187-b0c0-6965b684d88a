package com.zkjg.regtrace.persistence.vo.request.message;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("消息通知请求")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageNotifyRequest {

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("当前页")
    private Integer page;

    @ApiModelProperty("页大小")
    private Integer size;

    @ApiModelProperty("分类 0-系统消息 1-登记消息 2-溯源消息 3-其他")
    private Integer category;

    @ApiModelProperty("是否已读 0-未读 1-已读")
    private Integer isRead;

    @ApiModelProperty("关键词")
    private String keyword;

    @JsonIgnore
    private List<Integer> ids;

    @JsonIgnore
    private Integer userId;
}
