<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <localRepository>${user.home}/repository</localRepository>


    <!-- interactiveMode
     | This will determine whether maven prompts you when it needs input. If set to false,
     | maven will use a sensible default value, perhaps based on some other setting, for
     | the parameter in question.
     |
     | Default: true
    <interactiveMode>true</interactiveMode>
    -->

    <!-- offline
     | Determines whether maven should attempt to connect to the network when executing a build.
     | This will have an effect on artifact downloads, artifact deployment, and others.
     |
     | Default: false
    <offline>false</offline>
    -->

    <!-- pluginGroups
     | This is a list of additional group identifiers that will be searched when resolving plugins by their prefix, i.e.
     | when invoking a command line like "mvn prefix:goal". Maven will automatically add the group identifiers
     | "org.apache.maven.plugins" and "org.codehaus.mojo" if these are not already contained in the list.
     |-->
    <pluginGroups>
        <!-- pluginGroup
         | Specifies a further group identifier to use for plugin lookup.
        <pluginGroup>com.your.plugins</pluginGroup>
        -->
        <pluginGroup>com.spotify</pluginGroup>
    </pluginGroups>

    <!-- proxies
     | This is a list of proxies which can be used on this machine to connect to the network.
     | Unless otherwise specified (by system property or command-line switch), the first proxy
     | specification in this list marked as active will be used.
     |-->
    <proxies>
        <!-- proxy
         | Specification for one proxy, to be used in connecting to the network.
         |
        <proxy>
          <id>optional</id>
          <active>true</active>
          <protocol>http</protocol>
          <username>proxyuser</username>
          <password>proxypass</password>
          <host>proxy.host.net</host>
          <port>80</port>
          <nonProxyHosts>local.net|some.host.com</nonProxyHosts>
        </proxy>
        -->
    </proxies>

    <!-- servers
     | This is a list of authentication profiles, keyed by the server-id used within the system.
     | Authentication profiles can be used whenever maven must make a connection to a remote server.
     |-->
    <servers>
        <!-- 以下两个配置给pom文件中的distributionManagement中repository使用 -->
        <server>
            <!-- 此id与pom文件中distributionManagement中的id一致 -->
            <id>maven-releases</id>
            <!-- 鉴权用户名 -->
            <username>admin</username>
            <!-- 鉴权密码 -->
            <password>ZdedprcWQ7mXoDm</password>
        </server>
        <server>
            <id>maven-snapshots</id>
            <username>admin</username>
            <password>ZdedprcWQ7mXoDm</password>
        </server>

        <!-- id与下面profile >>> repositories >>> repository的id相同 -->
        <server>
            <id>nexus-zkjg</id>
            <username>admin</username>
            <password>ZdedprcWQ7mXoDm</password>
        </server>
    </servers>

    <!-- mirrors
     | This is a list of mirrors to be used in downloading artifacts from remote repositories.
     |
     | It works like this: a POM may declare a repository to use in resolving certain artifacts.
     | However, this repository may have problems with heavy traffic at times, so people have mirrored
     | it to several places.
     |
     | That repository definition will have a unique id, so we can create a mirror reference for that
     | repository, to be used as an alternate download site. The mirror site will be the preferred
     | server for that repository.
     |-->
    <!-- 为仓库列表配置的下载镜像列表，配置多个mirror时只有第一个会生效 -->
    <mirrors>
        <!-- 配置私服镜像 -->
        <mirror>
            <!-- 镜像唯一标识符 -->
            <id>nexus-zkjg</id>
            <!-- 镜像名称 -->
            <name>nexus-zkjg</name>
            <!--
          作用：被镜像的服务器(repository)id
          1. * 匹配所有
          2. external:* 除本地缓存后的所有仓库
          3. repo,repo1 repo 或者 repo1 指的是仓库id
          4. *,!repo1 除repo1的所有仓库
            -->
            <mirrorOf>nexus-zkjg</mirrorOf>
            <url>https://maven.zlattice.top/repository/maven-public/</url>
        </mirror>

        <!-- 配置阿里云镜像 -->
        <mirror>
            <id>nexus-aliyun</id>
            <mirrorOf>nexus-aliyun</mirrorOf>
            <name>Nexus aliyun</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
        </mirror>
    </mirrors>

    <!-- profiles
     | This is a list of profiles which can be activated in a variety of ways, and which can modify
     | the build process. Profiles provided in the settings.xml are intended to provide local machine-
     | specific paths and repository locations which allow the build to work in the local environment.
     |
     | For example, if you have an integration testing plugin - like cactus - that needs to know where
     | your Tomcat instance is installed, you can provide a variable here such that the variable is
     | dereferenced during the build process to configure the cactus plugin.
     |
     | As noted above, profiles can be activated in a variety of ways. One way - the activeProfiles
     | section of this document (settings.xml) - will be discussed later. Another way essentially
     | relies on the detection of a system property, either matching a particular value for the property,
     | or merely testing its existence. Profiles can also be activated by JDK version prefix, where a
     | value of '1.4' might activate a profile when the build is executed on a JDK version of '1.4.2_07'.
     | Finally, the list of active profiles can be specified directly from the command line.
     |
     | NOTE: For profiles defined in the settings.xml, you are restricted to specifying only artifact
     |       repositories, plugin repositories, and free-form properties to be used as configuration
     |       variables for plugins in the POM.
     |
     |-->
    <profiles>
        <!-- profile
         | Specifies a set of introductions to the build process, to be activated using one or more of the
         | mechanisms described above. For inheritance purposes, and to activate profiles via <activatedProfiles/>
         | or the command line, profiles have to have an ID that is unique.
         |
         | An encouraged best practice for profile identification is to use a consistent naming convention
         | for profiles, such as 'env-dev', 'env-test', 'env-production', 'user-jdcasey', 'user-brett', etc.
         | This will make it more intuitive to understand what the set of introduced profiles is attempting
         | to accomplish, particularly when you only have a list of profile id's for debug.
         |
         | This profile example uses the JDK version to trigger activation, and provides a JDK-specific repo.
        <profile>
          <id>jdk-1.4</id>

          <activation>
            <jdk>1.4</jdk>
          </activation>

          <repositories>
            <repository>
              <id>jdk14</id>
              <name>Repository for JDK 1.4 builds</name>
              <url>http://www.myhost.com/maven/jdk14</url>
              <layout>default</layout>
              <snapshotPolicy>always</snapshotPolicy>
            </repository>
          </repositories>
        </profile>
        -->

        <!--
         | Here is another profile, activated by the system property 'target-env' with a value of 'dev',
         | which provides a specific path to the Tomcat instance. To use this, your plugin configuration
         | might hypothetically look like:
         |
         | ...
         | <plugin>
         |   <groupId>org.myco.myplugins</groupId>
         |   <artifactId>myplugin</artifactId>
         |
         |   <configuration>
         |     <tomcatLocation>${tomcatPath}</tomcatLocation>
         |   </configuration>
         | </plugin>
         | ...
         |
         | NOTE: If you just wanted to inject this configuration whenever someone set 'target-env' to
         |       anything, you could just leave off the <value/> inside the activation-property.
         |

        <profile>
          <id>env-dev</id>

          <activation>
            <property>
              <name>target-env</name>
              <value>dev</value>
            </property>
          </activation>

        </profile>
        -->

        <profile>
            <!-- 该配置的唯一标识符 -->
            <id>nexus-default</id>

            <!-- 远程仓库列表，下载项目依赖文件的maven仓库地址; -->
            <repositories>
                <repository>
                    <!-- 远程仓库唯一标识 -->
                    <id>nexus-zkjg</id>
                    <!-- 远程仓库名称 -->
                    <name>maven-public</name>
                    <url>https://maven.zlattice.top/repository/maven-public/</url>
                    <!-- 处理远程仓库发布版本下载的策略 -->
                    <releases>
                        <enabled>true</enabled>
                        <!--
                        1.always：一直
                        2.daily：默认值，每日
                        3.interval:X (X代表以分钟为单位的时间间隔)
                        4.never：从不
                        -->
                        <updatePolicy>always</updatePolicy>
                        <!--
                            作用：当maven验证构件校验文件失败时怎么做
                            1.ignore：忽略
                            2.fail：失败
                            3.warn：警告
                         -->
                        <checksumPolicy>warn</checksumPolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>

                <repository>
                    <id>nexus-aliyun</id>
                    <url>http://maven.aliyun.com/nexus/content/groups/public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>

            <!-- 每个pluginRepository元素指定一个从maven用来寻找新插件的远程地址 -->
            <pluginRepositories>
                <pluginRepository>
                    <id>nexus-zkjg</id>
                    <url>https://maven.zlattice.top/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>warn</checksumPolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                        <checksumPolicy>warn</checksumPolicy>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <!-- 定义激活的profiles -->
    <activeProfiles>
        <activeProfile>nexus-default</activeProfile>
    </activeProfiles>
</settings>
