<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.6</version>
        <relativePath/>
    </parent>
    <groupId>com.zkjg</groupId>
    <artifactId>reg-trace</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>reg-trace</name>

    <modules>
        <module>reg-trace-common</module>
        <module>reg-trace-persistence</module>
        <module>reg-trace-core</module>
        <module>reg-trace-auth</module>
        <module>reg-trace-customer-web</module>
        <module>reg-trace-pc-web</module>
    </modules>

    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.7.6</spring-boot.version>

        <mysql.version>8.0.31</mysql.version>
        <dm.version>8.1.1.193</dm.version>
        <dm-hibernate.version>8.1.2.192</dm-hibernate.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <hutool.version>5.8.20</hutool.version>
        <knife4j.version>4.4.0</knife4j.version>
        <fastjson.version>2.0.37</fastjson.version>
        <minio.version>8.4.3</minio.version>
        <jwt.version>0.9.1</jwt.version>
        <email.version>1.6.2</email.version>
        <poi.version>5.2.3</poi.version>
        <commons-io.version>2.11.0</commons-io.version>
        <flying-saucer-pdf.version>9.1.22</flying-saucer-pdf.version>
        <itextpdf.version>********</itextpdf.version>
        <itext-asian.version>5.2.0</itext-asian.version>
        <poi-ooxml.version>5.2.3</poi-ooxml.version>
        <docx4j.version>8.3.8</docx4j.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <mica-ip2region>2.7.0</mica-ip2region>
        <powerjob.version>4.3.9</powerjob.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zkjg</groupId>
                <artifactId>reg-trace-auth</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkjg</groupId>
                <artifactId>reg-trace-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkjg</groupId>
                <artifactId>reg-trace-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkjg</groupId>
                <artifactId>reg-trace-persistence</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmDialect-for-hibernate5.6</artifactId>
                <version>${dm-hibernate.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${email.version}</version>
            </dependency>

            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>flying-saucer-pdf</artifactId>
                <version>${flying-saucer-pdf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>${itext-asian.version}</version>
            </dependency>

            <!-- POI Excel 相关依赖 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-core</artifactId>
                <version>${docx4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
                <version>${docx4j.version}</version>
            </dependency>

            <!-- HTML 转换支持 -->
            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-ImportXHTML</artifactId>
                <version>${docx4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>net.dreamlu</groupId>
                <artifactId>mica-ip2region</artifactId>
                <version>${mica-ip2region}</version>
            </dependency>
            <dependency>
                <groupId>tech.powerjob</groupId>
                <artifactId>powerjob-worker-spring-boot-starter</artifactId>
                <version>${powerjob.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>nexus-zkjg</id>
            <name>nexus-zkjg</name>
            <url>https://maven.zlattice.top/repository/maven-public/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>nexus-snapshots</name>
            <url>https://maven.zlattice.top/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
