FROM harbordev.zlattice.top/baas/baas-maven:base AS builder

WORKDIR /tmp

COPY . /tmp

RUN mvn package -Dmaven.test.skip=true -s /tmp/settings.xml

# This file is a template, and might need editing before it works on your project.
FROM harbordev.zlattice.top/baas/jgjdk:base as runner

WORKDIR /app

COPY --from=builder /tmp/reg-trace-customer-web/target/reg-trace-customer.jar ./reg-trace-customer.jar

CMD ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005", "-jar", "/app/reg-trace-customer.jar"]
